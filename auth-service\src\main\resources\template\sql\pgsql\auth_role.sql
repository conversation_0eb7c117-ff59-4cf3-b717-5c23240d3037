CREATE TABLE IF NOT EXISTS auth_role_$tenant_id
(
    id          SERIAL NOT NULL,
    tenant_id   VARCHAR(50)  DEFAULT '',
    name        VARCHAR(100) DEFAULT '',
    code        VARCHAR(50)  DEFAULT '',
    remark      VARCHAR(255) DEFAULT '',
    role_type   SMALLINT     DEFAULT 0,
    device      VARCHAR(25)  DEFAULT '',
    create_time BIGINT NOT NULL,
    create_by   VARCHAR(50)  DEFAULT '',
    update_time BIGINT       DEFAULT NULL,
    update_by   VARCHAR(50)  DEFAULT '',
    is_enabled  SMALLINT     DEFAULT 0,
    deleted     SMALLINT     DEFAULT 0,
    PRIMARY KEY (id)
);
COMMENT
ON TABLE auth_role_$tenant_id IS '权限角色';
COMMENT
ON COLUMN auth_role_$tenant_id.id IS '唯一标识';
COMMENT
ON COLUMN auth_role_$tenant_id.tenant_id IS '租户id';
COMMENT
ON COLUMN auth_role_$tenant_id.name IS '角色名称';
COMMENT
ON COLUMN auth_role_$tenant_id.code IS '角色编码';
COMMENT
ON COLUMN auth_role_$tenant_id.remark IS '角色描述';
COMMENT
ON COLUMN auth_role_$tenant_id.role_type IS '角色类型;0:系统角色 1:自定义角色';
COMMENT
ON COLUMN auth_role_$tenant_id.device IS '适用设备';
COMMENT
ON COLUMN auth_role_$tenant_id.create_time IS '创建时间';
COMMENT
ON COLUMN auth_role_$tenant_id.create_by IS '创建者';
COMMENT
ON COLUMN auth_role_$tenant_id.update_time IS '更新时间';
COMMENT
ON COLUMN auth_role_$tenant_id.update_by IS '更新者';
COMMENT
ON COLUMN auth_role_$tenant_id.is_enabled IS '是否启用;0停用1启用';
COMMENT
ON COLUMN auth_role_$tenant_id.deleted IS '是否删除';