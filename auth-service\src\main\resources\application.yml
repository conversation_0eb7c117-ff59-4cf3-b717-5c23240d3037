spring:
 profiles:
  active: local
i18n:
 resource:
  path: i18n/auth/message
msg:
 middleware:
  type: rabbitmq

rabbitmq:
 topics:
  - topic: FORM_PUBLISH
    exchange: caidao.hrpaas
    routingKey: caidao.hrpaas.form.publish
    queue: caidaocloud.auth.form.publish.queue
    exchangeType: DIRECT
    tenantIsolated: false
    consumersCount: 1
wechat:
  tenant:
    tenant01:
      corpId: 123
    tenant02:
      corpId: 234
