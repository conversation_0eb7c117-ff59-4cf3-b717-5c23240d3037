package com.caidaocloud.auth.service.caidao.facade;

import com.caidaocloud.auth.service.caidao.application.service.MenuSyncService;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "菜单")
@RestController
@RequestMapping("/api/auth/v1/tenant/menu")
public class MenuSyncController {
    @Autowired
    private MenuSyncService menuSyncService;

    @ApiOperation(value = "bi菜单同步")
    @GetMapping("bi/sync")
    public Result biSyncMenu() {
        menuSyncService.syncBiMenu();
        return Result.ok();
    }
}