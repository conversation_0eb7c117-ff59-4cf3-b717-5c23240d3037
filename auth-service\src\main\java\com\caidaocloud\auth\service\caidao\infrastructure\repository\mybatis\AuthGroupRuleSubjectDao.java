package com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthGroupRuleSubjectPo;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthSubjectInfoPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/11
 **/
public interface AuthGroupRuleSubjectDao extends BaseMapper<AuthGroupRuleSubjectPo> {


    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    Integer insertBatchSomeColumn(List<AuthGroupRuleSubjectPo> list);


    /**
     * 用户信息分页查询
     *
     * @param page
     * @param ruleIds
     * @param keywords
     * @param queryWrapper
     * @return
     */
    @Select("<script>" +
            "SELECT t2.subject_id, t1.account, t1.user_name, t1.status, t2.rule_id, t1.emp_id FROM user_base_info t1 " +
            "INNER JOIN auth_group_rule_subject t2 ON " +
            " t1.user_id = t2.subject_id WHERE t2.deleted = 0 " +
            "<if test='keywords != null and keywords != \"\"'> " +
            "AND (t1.account LIKE '${keywords}%' OR t1.user_name LIKE '${keywords}%') " +
            "</if>" +
            "<if test='ruleIds != null and ruleIds.size() > 0'> " +
            " AND t2.rule_id IN" +
            " <foreach item='item' index='index' collection='ruleIds' open='(' separator=',' close=')'> " +
            "   ${item} " +
            " </foreach>" +
            "</if> " +
            " <if test='ew.sqlSegment != null and \"\" != ew.sqlSegment'> " +
            "  AND ${ew.sqlSegment}" +
            " </if> " +
            "</script>")
    Page<AuthSubjectInfoPo> selectPageOfUserInfo(Page<AuthSubjectInfoPo> page, @Param("ruleIds") List<Long> ruleIds, @Param("keywords") String keywords, @Param(Constants.WRAPPER) Wrapper<AuthSubjectInfoPo> queryWrapper);

    /**
     * 查看用户信息总数
     *
     * @param ruleIds
     * @param keywords
     * @param queryWrapper
     * @return
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM user_base_info t1 INNER JOIN auth_group_rule_subject t2 ON " +
            " t1.user_id = t2.subject_id WHERE t2.deleted = 0 " +
            " <if test='keywords != null and keywords != \"\"'> " +
            "   AND (t1.account LIKE '${keywords}%' OR t1.user_name LIKE '${keywords}%')" +
            " </if> " +
            " <if test='ruleIds != null and ruleIds.size() > 0'>" +
            "   AND t2.rule_id IN " +
            "   <foreach item='item' index='index' collection='ruleIds' open='(' separator=',' close=')'>" +
            "       ${item} " +
            "   </foreach>" +
            " </if> " +
            " <if test= 'ew.sqlSegment != null and \"\" != ew.sqlSegment' > " +
            "  AND ${ew.sqlSegment}" +
            " </if> " +
            "</script>")
    Long selectCountOfUserInfo(@Param("ruleIds") List<Long> ruleIds, @Param("keywords") String keywords, @Param(Constants.WRAPPER) Wrapper<AuthSubjectInfoPo> queryWrapper);

}
