package com.caidaocloud.auth.service.service.mongo;

import com.caidaocloud.util.FastjsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class CommonAuthListTest {

    @Value("${caidaocloud.common.authList:}")
    private List<String> commonAuthList;

    @Test
    public void test() {
        System.out.println(FastjsonUtil.toJson(commonAuthList));
    }

}
