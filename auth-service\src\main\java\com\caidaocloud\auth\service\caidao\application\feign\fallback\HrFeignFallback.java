package com.caidaocloud.auth.service.caidao.application.feign.fallback;

import com.caidaocloud.auth.service.caidao.application.dto.hr.EmpSearchColumnsDto;
import com.caidaocloud.auth.service.caidao.application.feign.IHrFeign;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.web.Result;

import java.util.Map;

/**
 * created by: FoAng
 * create time: 8/12/2022 3:52 下午
 */
public class HrFeignFallback implements IHrFeign {


    @Override
    public Result<PageResult<Map<String, String>>> searchEmpColumnsPage(EmpSearchColumnsDto empSearchDto) {
        return null;
    }
}
