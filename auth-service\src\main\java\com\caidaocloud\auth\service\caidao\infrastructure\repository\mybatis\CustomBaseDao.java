package com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2022/5/11
 **/
public interface CustomBaseDao extends BaseMapper {

    /**
     * 执行脚本
     *
     * @param sql
     */
    @Insert("${sql}")
    void exectionSql(@Param("sql") String sql);

}
