package com.caidaocloud.auth.service.caidao.infrastructure.repository.tenant;

import com.caidaocloud.auth.service.caidao.application.repository.IInitTenantRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.CustomBaseDao;
import com.caidaocloud.excption.ServerException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2022/5/11
 **/
@Repository
public class InitTenantRepositoryImpl implements IInitTenantRepository {

    @Autowired
    private CustomBaseDao customBaseDao;

    /**
     * 初始化租户表
     *
     * @param sql
     */
    @Override
    public void initTenantTable(String sql) {
        if (StringUtils.isBlank(sql)) {
            throw new ServerException("sql str is empty");
        }
        customBaseDao.exectionSql(sql);
    }

}
