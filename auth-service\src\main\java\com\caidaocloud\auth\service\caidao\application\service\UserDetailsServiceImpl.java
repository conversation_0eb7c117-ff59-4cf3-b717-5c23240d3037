package com.caidaocloud.auth.service.caidao.application.service;

import com.caidaocloud.auth.service.caidao.application.dto.UserDetailDto;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * 授权码模式用户登录业务类
 * <AUTHOR>
 * @date 2011-11-11
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        UserDetailDto userDetail = new UserDetailDto("caidaotest", 56594L,
                username, "123456", "123", AuthorityUtils.commaSeparatedStringToAuthorityList("user"));
        return userDetail;
    }
}
