package com.caidaocloud.auth.service.caidao.domain.repository;

import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleAndRuleDo;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleDo;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRolePermissionDo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequence;

import java.util.List;
import java.util.Map;

/**
 * 权限角色
 *
 * <AUTHOR>
 * @date 2022/5/7
 **/
public interface IAuthRoleRepository {

    /**
     * 保存或更新角色权限
     *
     * @param authRoleDo
     * @return
     */
    Long saveOrUpdateRole(AuthRoleDo authRoleDo);

    /**
     * 删除角色
     *
     * @param roleIdList
     */
    void deleteRole(List<Long> roleIdList);

    /**
     * 复制角色
     *
     * @param roleId
     */
    Long copyRole(Long roleId);

    /**
     * 权限角色分页
     *
     * @param queryPageBean
     * @return
     */
    PageResult<AuthRoleDo> getPageOfRole(QueryPageBean queryPageBean);

    /**
     * 通过code获取角色
     *
     * @param codeList
     * @return
     */
    List<AuthRoleDo> selectAuthRoleByCode(List<String> codeList);

    /**
     * 通过角色查找资源code
     *
     * @param roleIdList
     * @return
     */
    List<String> getResourceCodeListByRoleIdList(List<Long> roleIdList);

    Map<String, String> getResourceScopeDetailBySubjectId(List<Long> roleIdList, String parentCode);

    /**
     * 根据roleIdList查权限code
     *
     * @param roleIdList
     * @param parentCode
     * @return
     */
    List<String> getResourceCodeByRolesAndParentCode(List<Long> roleIdList, String parentCode);

    /**
     * 通过角色查找资源url
     *
     * @param roleIdList
     * @return
     */
    List<String> getResourceUrlListByRoleIdList(List<Long> roleIdList);

    /**
     * 查看角色权限
     *
     * @param roleId
     * @return
     */
    List<AuthRolePermissionDo> getPermissionOfRole(Long roleId);

    /**
     * 通过名字查询roleId和roleName
     *
     * @param roleNameList
     * @return
     */
    List<AuthRoleDo> getRoleIdAndNameByRoleName(List<String> roleNameList);

    /**
     * 根据roleId获取角色
     *
     * @param roleId
     * @return
     */
    Option<AuthRoleDo> getRoleByRoleId(Long roleId);

    /**
     * 根据名字判断角色是否存在
     *
     * @param roleName
     * @return
     */
    boolean isExistByRoleName(String roleName);

    boolean isExistByRoleName(String roleName, Long excludeId);

    List<AuthRoleAndRuleDo> getRoleByRuleIds(List<Long> ruleIds);

    /**
     * 获取所有角色
     *
     * @return
     */
    Sequence<AuthRoleDo> getAllRole();

    /**
     * 删除角色权限
     *
     * @param code
     */
    void deleteRolePermission(String code);

}
