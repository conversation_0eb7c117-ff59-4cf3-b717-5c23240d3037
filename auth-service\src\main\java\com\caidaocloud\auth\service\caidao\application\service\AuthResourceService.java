package com.caidaocloud.auth.service.caidao.application.service;

import com.caidaocloud.auth.service.caidao.application.dto.AuthResourceDto;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthResourceDo;
import com.caidaocloud.auth.service.caidao.domain.service.AuthRoleDomainService;
import com.caidaocloud.auth.service.caidao.facade.vo.AuthResourceUrlVo;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AuthResourceService {
    @Autowired
    private AuthRoleDomainService authRoleDomainService;

    public List<AuthResourceDo> list(ResourceCategoryEnum resourceCategory, String parentCode) {
        return Sequences.sequence(AuthResourceDo.list(resourceCategory, parentCode))
                .unique(e -> e.getCode()).toList();
    }

    public Long create(AuthResourceDo authResourceDo, boolean saas) {
        return authResourceDo.create(saas);
    }

    @Transactional(rollbackFor = Exception.class)
    public void createList(List<AuthResourceDto> resourceList, boolean saas) {
        if (CollectionUtils.isEmpty(resourceList)) {
            log.warn("resourceList is empty");
            return;
        }
        try {
            var authResourceDoList = FastjsonUtil.convertList(resourceList, AuthResourceDo.class);
            AuthResourceDo.createBatch(authResourceDoList, saas);
        } catch (Exception e) {
            var parameterStr = FastjsonUtil.toJson(resourceList);
            log.error("has an error occurred while createing resources , saas = {}, parameter = {} \n {}", saas, parameterStr, e);
            throw new ServerException("has an error occurred while createing resources");
        }
    }

    public void update(AuthResourceDo authResourceDo, boolean saas) {
        authResourceDo.updateByCode(saas);
    }

    @Transactional(rollbackFor = Exception.class)
    public void remove(String code) {
        if (StringUtils.isBlank(code)) {
            throw new ServerException("code is empty");
        }
        AuthResourceDo.remove(code);
        authRoleDomainService.deleteRolePermission(code);
    }

    public Sequence<AuthResourceDto> loadResources(List<String> codeList) {
        List<AuthResourceDo> list = AuthResourceDo.loadByCodes(codeList);
        if (CollectionUtils.isEmpty(list)) {
            return Sequences.empty();
        }
        return Sequences.sequence(FastjsonUtil.convertList(list, AuthResourceDto.class));
    }

    public List<AuthResourceDo> listByCode(String code) {
        return AuthResourceDo.listByCode(code);
    }

    public AuthResourceUrlVo detailByUrl(String url) {
        List<AuthResourceDo> resourceDoList = AuthResourceDo.listByUrl(url);
        AuthResourceDo resourceDo = CollectionUtils.isEmpty(resourceDoList) ? null : resourceDoList.get(0);
        PreCheck.preCheckArgument(resourceDo == null, ErrorMessage.fromCode("RESOURCE_NOT_FOUND"));
        AuthResourceUrlVo resourceUrlVo = FastjsonUtil.convertObject(resourceDo, AuthResourceUrlVo.class);
        resourceUrlVo.setParentName(fetchParentResourceName(resourceDo, null));
        return resourceUrlVo;
    }

    private String fetchParentResourceName(AuthResourceDo resourceDo, List<String> parentNames) {
        parentNames = parentNames == null ? Lists.newArrayList() : parentNames;
        if (resourceDo != null && StringUtils.isNotBlank(resourceDo.getParentCode())) {
            List<AuthResourceDo> parentList = this.listByCode(resourceDo.getParentCode());
            List<AuthResourceDo> menuParent = parentList.stream().filter(it -> it.getCategory() == ResourceCategoryEnum.MENU)
                    .collect(Collectors.toList());
            AuthResourceDo linkParent = CollectionUtils.isEmpty(menuParent) ? null : menuParent.get(0);
            if (linkParent != null) {
                parentNames.add(0, linkParent.getName());
                return fetchParentResourceName(linkParent, parentNames);
            }
        }
        return String.join("-", parentNames);
    }

    public void updateList(List<AuthResourceDto> resourceList) {
        for (AuthResourceDto dto : resourceList) {
            AuthResourceDo resourceDo = FastjsonUtil.convertObject(dto, AuthResourceDo.class);
            update(resourceDo, false);
        }
    }

  public   void removeList(List<AuthResourceDto> removeList) {
      removeList.stream().map(AuthResourceDto::getCode).distinct().forEach(it -> remove(it));
    }
}
