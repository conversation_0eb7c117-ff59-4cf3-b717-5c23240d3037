package com.caidaocloud.auth.service.caidao.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("user_base_info")
public class UserBaseInfoPo implements Serializable {

    private Long userId;
    private Long accountId;
    private String account;
    private Long tenantId;
    private String userName;
    private int sex;
    private String headPortrait;
    private int status;
    private int ifDefault;
    private Long empId;
    private Long corpId;
    private String extInfo;
    private Long createBy;
    private Long updateBy;
    private Long createTime;
    private Long updateTime;
    private int deleted;


}
