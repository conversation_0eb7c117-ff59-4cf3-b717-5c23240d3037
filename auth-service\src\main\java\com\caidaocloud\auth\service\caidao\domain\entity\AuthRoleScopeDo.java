package com.caidaocloud.auth.service.caidao.domain.entity;

import com.caidaocloud.auth.core.enums.AuthRoleScopeRestriction;
import com.caidaocloud.auth.core.enums.AuthRoleScopeTargetType;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.AuthRoleScopeComparator;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.AuthRoleScopeEnum;
import com.caidaocloud.auth.service.caidao.infrastructure.util.proxy.AuthRoleScopeProxy;
import lombok.Data;

import java.util.List;

@Data
public class AuthRoleScopeDo {
    private Long roleId;
    private AuthRoleScopeTargetType targetType = AuthRoleScopeTargetType.STANDARD;
    private String targets;
    private String filterProperty;
    private AuthRoleScopeComparator comparator = AuthRoleScopeComparator.IN;
    private AuthRoleScopeRestriction restriction;
    private String values;
    private String simpleValues;

    public static void flushAll(Long roleId, List<AuthRoleScopeDo> scopeList, AuthRoleScopeEnum type) {
        AuthRoleScopeProxy.getProxy(type).flushAll(roleId, scopeList);
    }

    public static void deleteByRoleIds(List<Long> roleIdList, AuthRoleScopeEnum type) {
        AuthRoleScopeProxy.getProxy(type).deleteByRoleIds(roleIdList);
    }

    public static void copy(Long copyFromId, Long copyToId, AuthRoleScopeEnum type) {
        List<AuthRoleScopeDo> scopes = AuthRoleScopeProxy.getProxy(type).load(copyFromId);
        scopes.forEach(it -> it.setRoleId(copyToId));
        AuthRoleScopeProxy.getProxy(type).flushAll(copyToId, scopes);
    }

    public static List<AuthRoleScopeDo> list(Long roleId, AuthRoleScopeEnum type) {
        return AuthRoleScopeProxy.getProxy(type).load(roleId);
    }

    public static List<AuthRoleScopeDo> list(List<Long> roleIds, AuthRoleScopeEnum type) {
        return AuthRoleScopeProxy.getProxy(type).list(roleIds);
    }
}