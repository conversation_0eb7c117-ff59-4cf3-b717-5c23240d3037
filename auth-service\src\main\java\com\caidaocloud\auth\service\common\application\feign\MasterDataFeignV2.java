package com.caidaocloud.auth.service.common.application.feign;

import java.util.List;

import com.caidaocloud.auth.service.caidao.application.dto.masterdata.EmpInfoVo;
import com.caidaocloud.auth.service.caidao.application.dto.masterdata.EmpNodeVo;
import com.caidaocloud.auth.service.caidao.application.dto.masterdata.EmpSearchDto;
import com.caidaocloud.auth.service.caidao.application.dto.masterdata.EmpWorkInfoVo;
import com.caidaocloud.auth.service.caidao.application.feign.fallback.MasterDataFeignFallback;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 2.0主数据feign
 **/
@FeignClient(value = "${feign.rename.caidaocloud-masterdata-service:caidaocloud-masterdata-service}", path = "/api/masterdata",
        fallback = MasterDataFeignFallback.class, configuration = FeignConfiguration.class, contextId = "masterdataFeignV2")
public interface MasterDataFeignV2 {

    /**
     * 获取员工信息
     *
     * @return
     */
    @PostMapping(value = "/v2/emp/empInfo/list")
    Result<List<EmpInfoVo>> getEmpInfoByEmpIds(@RequestBody EmpSearchDto dto);



    @PostMapping(value = "/v2/emp/queryEmpPage")
    Result<PageResult<EmpNodeVo>> queryEmpPage(@RequestBody EmpSearchDto dto);


    @PostMapping(value = "/v2/emp/queryEmpColumnsPage")
    Result<PageResult<EmpWorkInfoVo>> queryEmpColumnsPage(@RequestBody EmpSearchDto dto);

}