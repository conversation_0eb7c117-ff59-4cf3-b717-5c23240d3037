package com.caidaocloud.auth.service.caidao.application.factory;

import com.caidaocloud.auth.core.dto.AuthRoleScopeTargetDetail;
import com.caidaocloud.auth.core.dto.AuthRoleScopeTargetRegisterDto;
import com.caidaocloud.auth.core.enums.AuthRoleScopeRestriction;
import com.caidaocloud.auth.core.enums.AuthRoleScopeTargetType;
import com.googlecode.totallylazy.Lists;

import static com.caidaocloud.auth.service.caidao.infrastructure.constant.CodeConstant.FORM_AUTH_SCOPE_PREFIX;

/**
 *
 * <AUTHOR>
 * @date 2024/9/3
 */
public class FormAuthScopeFactory {
	public static AuthRoleScopeTargetRegisterDto create(String formName,String formId){
		AuthRoleScopeTargetRegisterDto dto = new AuthRoleScopeTargetRegisterDto();
		dto.setType(AuthRoleScopeTargetType.FORM);
		dto.setName(formName);
		dto.setCode(FORM_AUTH_SCOPE_PREFIX + formId);
		dto.setDetails(Lists.list(
				new AuthRoleScopeTargetDetail(AuthRoleScopeRestriction.MY_ORG, "entity.form." + formId, "form_owner_organize"),
				new AuthRoleScopeTargetDetail(AuthRoleScopeRestriction.MY_ORG_AND_BELONGINGS, "entity.form." + formId, "form_owner_organize"),
				new AuthRoleScopeTargetDetail(AuthRoleScopeRestriction.SELECTED_ORG, "entity.form." + formId, "form_owner_organize"),
				new AuthRoleScopeTargetDetail(AuthRoleScopeRestriction.SELECTED_ORG_AND_BELONGINGS, "entity.form." + formId, "form_owner_organize"),
				new AuthRoleScopeTargetDetail(AuthRoleScopeRestriction.SELECTED_LEADER, "entity.form." + formId, "form_owner_organize"),
				new AuthRoleScopeTargetDetail(AuthRoleScopeRestriction.SELECTED_LEADER_WITH_CONCURRENT, "entity.form." + formId, "form_owner$empId"),
				new AuthRoleScopeTargetDetail(AuthRoleScopeRestriction.DIRECT_SUBORDINATE, "entity.form." + formId, "form_owner$empId"),
				new AuthRoleScopeTargetDetail(AuthRoleScopeRestriction.DIRECT_SUBORDINATE_WITH_CONCURRENT, "entity.form." + formId, "form_owner$empId"),
				new AuthRoleScopeTargetDetail(AuthRoleScopeRestriction.ALL_SUBORDINATE, "entity.form." + formId, "form_owner$empId"),
				new AuthRoleScopeTargetDetail(AuthRoleScopeRestriction.SELECTED_HRBP, "entity.form." + formId, "form_owner_organize"),
				new AuthRoleScopeTargetDetail(AuthRoleScopeRestriction.SELECTED_HRBP_WITH_CONCURRENT, "entity.form." + formId, "form_owner$empId"),
				new AuthRoleScopeTargetDetail(AuthRoleScopeRestriction.SELECTED_EMP, "entity.form." + formId, "form_owner$empId"),
				new AuthRoleScopeTargetDetail(AuthRoleScopeRestriction.SELECTED_EMP_TYPE, "entity.form." + formId, "form_owner_emp_type$dictValue"),
				new AuthRoleScopeTargetDetail(AuthRoleScopeRestriction.ALL, "entity.form." + formId, "tenantId")
		));
		return dto;
	}
}
