package com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthRoleAndPermissionPo;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthRoleAndRulePo;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthRolePo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/7
 **/
public interface AuthRoleDao extends BaseMapper<AuthRolePo> {

    /**
     * 获取角色
     *
     * @param roleId
     * @return
     */
    @Select("select t1.id," +
            "       t1.is_enabled," +
            "       t1.i18n_name   , " +
            "       t1.name," +
            "       t1.code," +
            "       t2.role_id," +
            "       t1.role_type," +
            "       t1.remark," +
            "       t1.device," +
            "       t2.id as permisson_id," +
            "       t2.resource_code," +
            "       t2.parent_code," +
            "       t2.category," +
            "       t2.data_scope_detail " +
            "from auth_role t1 " +
            "       left join (select id,role_id,resource_code,parent_code,category,data_scope_detail " +
            " from auth_role_permission where deleted = 0) as t2 on t1.id = t2.role_id " +
            "where t1.id = #{roleId} and t1.deleted = 0")
    List<AuthRoleAndPermissionPo> getRoleById(@Param("roleId") Long roleId);

    /**
     * 查看角色
     *
     * @param ruleIds
     * @return
     */
    @Select("<script> " +
            "select t2.group_rule_id as rule_id, t1.name as role_name, t1.code, t1.id as role_id " +
            "from auth_role t1 " +
            "         inner join auth_role_group_rule t2 on t1.id = t2.role_id " +
            "where t1.deleted = 0  " +
            " <if test='ruleIds != null and ruleIds.size() > 0'> " +
            "  and t2.group_rule_id in " +
            "  <foreach item='item' index='index' collection='ruleIds' open='(' separator=',' close=')'> " +
            "       #{item} " +
            "  </foreach>" +
            " </if>" +
            "</script>")
    List<AuthRoleAndRulePo> getRoleByRuleIds(@Param("ruleIds") List<Long> ruleIds);

}
