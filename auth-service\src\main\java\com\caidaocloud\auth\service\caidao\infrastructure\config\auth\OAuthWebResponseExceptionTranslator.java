package com.caidaocloud.auth.service.caidao.infrastructure.config.auth;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.security.oauth2.provider.error.DefaultWebResponseExceptionTranslator;

/**
 * 暂时不用
 * <AUTHOR>
 * @date 2021-11-22
 */
@Slf4j
public class OAuthWebResponseExceptionTranslator extends DefaultWebResponseExceptionTranslator {
    @Override
    public ResponseEntity<OAuth2Exception> translate(Exception e) throws Exception {
        log.error("translate err, {}", e.getMessage(), e);
        return super.translate(e);
    }
}
