package com.caidaocloud.auth.service.caidao.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleScopeDo;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthRoleScopeRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.AuthRoleScopeDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthRoleScopePo;
import com.caidaocloud.util.FastjsonUtil;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AuthRoleScopeRepositoryImpl implements IAuthRoleScopeRepository {

    @Autowired
    private AuthRoleScopeDao authRoleScopeDao;

    @Override
    public void flushAll(Long roleId, List<AuthRoleScopeDo> scopeList) {
        authRoleScopeDao.delete(new LambdaQueryWrapper<AuthRoleScopePo>().eq(AuthRoleScopePo::getRoleId, roleId));
        FastjsonUtil.convertList(scopeList,
                AuthRoleScopePo.class).forEach(it->{
            authRoleScopeDao.insert(it);
        });
    }

    @Override
    public void deleteByRoleIds(List<Long> roleIdList) {
        authRoleScopeDao.delete(new LambdaQueryWrapper<AuthRoleScopePo>().in(AuthRoleScopePo::getRoleId, roleIdList));
    }

    @Override
    public List<AuthRoleScopeDo> load(Long roleId) {
        val scopes = authRoleScopeDao.selectList(new LambdaQueryWrapper<AuthRoleScopePo>().eq(AuthRoleScopePo::getRoleId,roleId));
        return FastjsonUtil.convertList(scopes, AuthRoleScopeDo.class);
    }

    @Override
    public List<AuthRoleScopeDo> list(List<Long> roleIds) {
        val scopes = authRoleScopeDao.selectList(new LambdaQueryWrapper<AuthRoleScopePo>()
                .in(AuthRoleScopePo::getRoleId, roleIds));
        return FastjsonUtil.convertList(scopes, AuthRoleScopeDo.class);
    }
}
