package com.caidaocloud.auth.service.caidao.infrastructure.util;

import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;

/**
 * 处理1.0老系统的缓存序列化问题
 */
@Slf4j
public class SerializeUtil {
    public static byte[] serialize(Object object) {
        try(ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(baos)){
            oos.writeObject(object);
            byte[] bytes = baos.toByteArray();
            return bytes;
        } catch (Exception e) {
            log.error("serialize err,{}", e.getMessage(), e);
        }

        return null;
    }

    public static Object unserialize(byte[] bytes) {
        if (bytes == null) {
            return null;
        }

        try (ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
             ObjectInputStream ois = new ObjectInputStream(bais)) {
            return ois.readObject();
        } catch (Exception e) {
            log.error("unserialize err,{}", e.getMessage(), e);
        }

        return null;
    }

    public static Object unOldSerialize(byte[] bytes) {
        if(null == bytes){
            return null;
        }

        /*try (ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
             DefaultRedisObjectInputStream ois = new DefaultRedisObjectInputStream(bais)){
            return ois.readObject();
        } catch (Exception e) {
            log.error("unOldSerialize err,{}", e.getMessage(), e);
        }*/

        return null;
    }

}
