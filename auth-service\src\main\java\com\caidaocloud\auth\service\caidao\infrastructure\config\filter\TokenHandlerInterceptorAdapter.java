package com.caidaocloud.auth.service.caidao.infrastructure.config.filter;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.RequestHelper;
import com.caidaocloud.web.ResponseWrap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
@Component
public class TokenHandlerInterceptorAdapter extends HandlerInterceptorAdapter {

    @Resource
    private ISessionService sessionService;

    @NacosValue(value = "${ignoreAuthList:/api/auth/v1/tenant/init/admin}", autoRefreshed = true)
    private List<String> ignoreAuthList;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if(isSwaggerResourcesUri() || isUrlMatch(request.getRequestURI(), request.getMethod(), ignoreAuthList)){
            return super.preHandle(request, response, handler);
        }

        UserInfo userInfo = sessionService.getUserInfo();
        if (null == userInfo) {
            String errMsg = FastjsonUtil.toJson(ResponseWrap.wrapResult(ErrorCodes.TOKEN_INVALID, "Access-Token illegal!", null));
            response.setStatus(200);
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json; charset=utf-8");
            response.getWriter().write(errMsg);
            return false;
        }

        return super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        super.afterCompletion(request, response, handler, ex);
    }

    private boolean isUrlMatch(String apiPath, String method, List<String> urlList) {
        if (StringUtils.isEmpty(apiPath) || StringUtils.isEmpty(method)) {
            return true;
        }

        String apiPathLower = apiPath.toLowerCase().trim();
        if (RequestHelper.isStaticFile(apiPathLower)) {
            return true;
        }

        for (String i : urlList) {
            if (apiPathLower.startsWith(i.toLowerCase().trim())) {
                return true;
            }
        }
        return false;
    }

    private boolean isSwaggerResourcesUri() {
        HttpServletRequest httpServletRequest = RequestHelper.getRequest();
        String swaggerUri = httpServletRequest.getRequestURI();
        return null != swaggerUri && swaggerUri.indexOf("swagger") > -1;
    }
}