package com.caidaocloud.auth.service.caidao.infrastructure.repository.impl;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.auth.service.caidao.domain.entity.TenantBaseInfoDo;
import com.caidaocloud.auth.service.caidao.domain.repository.ITenantBaseInfoRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.TenantBaseInfoDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.TenantBaseInfoPo;
import com.caidaocloud.util.ObjectConverter;
import com.google.common.collect.Lists;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

@Repository
public class TenantBaseInfoRepositoryImpl implements ITenantBaseInfoRepository {
    @Autowired
    private TenantBaseInfoDao dao;

    @Override
    public void insertBatch(List<TenantBaseInfoDo> dataList) {
        for (TenantBaseInfoDo tenantInfo : dataList) {
            dao.insert(ObjectConverter.convert(tenantInfo, TenantBaseInfoPo.class));
        }
    }

    @Override
    public void insert(TenantBaseInfoDo data) {
        dao.insert(ObjectConverter.convert(data, TenantBaseInfoPo.class));
    }

    @Override
    public void update(TenantBaseInfoDo data) {
        dao.updateById(ObjectConverter.convert(data, TenantBaseInfoPo.class));
    }

    @Override
    public int insertSelective(TenantBaseInfoDo record) {
        return dao.insert(ObjectConverter.convert(record, TenantBaseInfoPo.class));
    }

    @Override
    public int updateByPrimaryKeySelective(TenantBaseInfoDo record) {
        return dao.updateById(ObjectConverter.convert(record, TenantBaseInfoPo.class));
    }

    @Override
    public void delete(List<Long> tenantIds) {
        dao.deleteBatchIds(tenantIds);
    }
    //
    // @Override
    // public void softDelete(List<Long> tenantIds) {
    //     TenantBaseInfoPo TenantBaseInfoPo = new TenantBaseInfoPo();
    //     TenantBaseInfoPo.setDeleted(DeleteStatusEnum.DELETED.getIndex());
    //     TenantBaseInfoPo.setUpdateTime(System.currentTimeMillis());
    //     LambdaQueryWrapper<TenantBaseInfoPo> mQueryInfo = new QueryWrapper<com.caidaocloud.auth.service.caidao.infrastructure.repository.po.sql.TenantBaseInfoPo>().lambda();
    //     mQueryInfo.in(com.caidaocloud.auth.service.caidao.infrastructure.repository.po.sql.TenantBaseInfoPo::getTenantId, tenantIds);
    //     dao.update(TenantBaseInfoPo, mQueryInfo);
    // }

    @Override
    public List<TenantBaseInfoDo> getTenantList(List<Long> tenantIds) {
        LambdaQueryWrapper<TenantBaseInfoPo> mQueryInfo = new QueryWrapper<TenantBaseInfoPo>().lambda();
        mQueryInfo.in(TenantBaseInfoPo::getTenantId, tenantIds);
        List<TenantBaseInfoPo> list = dao.selectList(mQueryInfo);
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : ObjectConverter.convertList(list, TenantBaseInfoDo.class);
    }

    @Override
    public TenantBaseInfoDo getTenantByCode(String tenantCode) {
        LambdaQueryWrapper<TenantBaseInfoPo> queryWrapper = new QueryWrapper<TenantBaseInfoPo>().lambda();
        queryWrapper.eq(TenantBaseInfoPo::getTenantCode, tenantCode);
        List<TenantBaseInfoPo> tenantBaseInfoList = dao.selectList(queryWrapper);
        return CollectionUtils.isEmpty(tenantBaseInfoList) ? null : ObjectConverter.convertList(tenantBaseInfoList, TenantBaseInfoDo.class)
                .get(0);
    }

    @Override
    public List<TenantBaseInfoDo> getTenantListByCorpCode(String corpCode) {
        LambdaQueryWrapper<TenantBaseInfoPo> queryWrapper = new QueryWrapper<TenantBaseInfoPo>().lambda();
        queryWrapper.eq(TenantBaseInfoPo::getCorpCode, corpCode);
        List<TenantBaseInfoPo> tenantBaseInfoList = dao.selectList(queryWrapper);
        return CollectionUtils.isEmpty(tenantBaseInfoList) ? null : ObjectConverter.convertList(tenantBaseInfoList, TenantBaseInfoDo.class);
    }
}
