package com.caidaocloud.auth.service.caidao.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/11
 **/
@Data
public class AuthSubjectInfoPo {

    @TableField(value = "subject_id")
    private Long subjectId;

    @TableField(value = "account")
    private String account;

    @TableField(value = "user_name")
    private String userName;

    @TableField(value = "emp_id")
    private Long empId;

    @TableField(value = "status")
    private Integer status;

    @TableField(value = "rule_id")
    private Long ruleId;

}
