package com.caidaocloud.auth.service.caidao.application.feign.fallback;

import com.caidaocloud.auth.service.caidao.application.dto.TenantCommonConfigDto;
import com.caidaocloud.auth.service.caidao.application.feign.IMaintenanceFeign;
import com.caidaocloud.web.Result;

public class MaintenanceFeignFallback implements IMaintenanceFeign {
    @Override
    public Result<TenantCommonConfigDto> tenantCommonConfig() {
        return Result.fail();
    }
}
