package com.caidaocloud.auth.service.caidao.application.service;

import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.caidaocloud.auth.core.enums.AuthRoleScopeRestriction;
import com.caidaocloud.auth.core.enums.AuthRoleScopeTarget;
import com.caidaocloud.auth.service.caidao.application.dto.AuthDefaultRuleOfRoleDto;
import com.caidaocloud.auth.service.caidao.application.dto.AuthRoleAndRuleDto;
import com.caidaocloud.auth.service.caidao.application.dto.AuthRoleScopeDto;
import com.caidaocloud.auth.service.caidao.application.dto.AuthSubjectRoleDto;
import com.caidaocloud.auth.service.caidao.application.dto.TenantCommonConfigDto;
import com.caidaocloud.auth.service.caidao.application.dto.masterdata.SysEmpInfoDto;
import com.caidaocloud.auth.service.caidao.application.dto.subject.AuthRuleIdAndSubjectIdDto;
import com.caidaocloud.auth.service.caidao.application.dto.subject.AuthSubjectOfRoleDto;
import com.caidaocloud.auth.service.caidao.application.dto.subject.AuthToSubjectDto;
import com.caidaocloud.auth.service.caidao.application.feign.IMaintenanceFeign;
import com.caidaocloud.auth.service.caidao.domain.entity.*;
import com.caidaocloud.auth.service.caidao.domain.repository.ISysEmpRepositoy;
import com.caidaocloud.auth.service.caidao.domain.service.AuthGroupRuleSubjectDomainService;
import com.caidaocloud.auth.service.caidao.domain.service.AuthRoleDomainService;
import com.caidaocloud.auth.service.caidao.domain.service.AuthRoleGroupDomainService;
import com.caidaocloud.auth.service.caidao.domain.service.UserBaseInfoDomainService;
import com.caidaocloud.auth.service.caidao.facade.vo.AuthImportSubjectErrorVo;
import com.caidaocloud.auth.service.caidao.facade.vo.AuthRoleScopeTargetVo;
import com.caidaocloud.auth.service.caidao.facade.vo.AuthSubjectAndRoleVo;
import com.caidaocloud.auth.service.caidao.facade.vo.AuthSubjectInfoVo;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.AuthRoleScopeEnum;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.SystemRoleEnum;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthRoleGroupRulePo;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.service.AuthRoleGroupRuleService;
import com.caidaocloud.auth.service.caidao.infrastructure.util.cache.InitResource;
import com.caidaocloud.auth.service.common.infrastructure.config.thread.ThreadPoolExector;
import com.caidaocloud.dto.FilterBean;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.em.OpEnum;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import com.jarvis.cache.annotation.Cache;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/11
 **/
@Service
@Slf4j
public class AuthGroupRuleSubjectService {
    @Autowired
    private AuthRoleGroupService authRoleGroupService;
    @Resource
    private AuthRoleGroupRuleService authRoleGroupRuleService;
    @Autowired
    private AuthGroupRuleSubjectDomainService authGroupRuleSubjectDomainService;
    @Autowired
    private ISysEmpRepositoy empRepositoy;
    @Autowired
    private AuthRoleGroupDomainService authRoleGroupDomainService;
    @Autowired
    private AuthRoleDomainService authRoleDomainService;
    @Autowired
    private ThreadPoolExector threadPoolExector;
    @Autowired
    private AuthRoleService authRoleService;
    @Autowired
    private AuthResourceService authResourceService;
    @Autowired
    private UserBaseInfoDomainService userBaseInfoDomainService;
    @Value("${caidaocloud.common.authList:}")
    private List<String> commonAuthList;
    @Autowired
    private IMaintenanceFeign maintenanceFeign;


    public List<AuthSubjectInfoVo> getSubjectsOfRole(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            throw new ServerException("roleIds is null");
        }
        List<AuthSubjectInfoVo> resultList = Lists.newArrayList();
        val ruleIds = authRoleGroupService.getRoleDefaultGroupRuleId(roleIds);
        if (!CollectionUtils.isEmpty(ruleIds)) {
            var queryPageBean = new QueryPageBean();
            queryPageBean.setFilterList(Lists.newArrayList());
            queryPageBean.setPageNo(1);
            queryPageBean.setPageSize(100);
            queryPageBean.setTotal(0);
            while (true) {
                var subjectPage = getSubjectPage(ruleIds, queryPageBean);
                queryPageBean.setPageNo(queryPageBean.getPageNo() + 1);
                if (CollectionUtils.isEmpty(subjectPage.getItems())) {
                    break;
                }
                resultList.addAll(subjectPage.getItems());
            }
        }
        return resultList;
    }

    /**
     * 用户修改角色信息
     *
     * @param subjectRoleDto
     */
    @Transactional(rollbackFor = Exception.class)
    public void empChangeRole(AuthSubjectRoleDto subjectRoleDto) {
        //caidao-1860 提供用户修改角色接口；

        //用户id
        String subjectId = subjectRoleDto.getSubjectId();
        //新角色id
        List<String> roleIds = subjectRoleDto.getRoleIds();
        checkConfigRole(subjectId);

        authGroupRuleSubjectDomainService.deleteSubjects(subjectId);

        if (!CollectionUtils.isEmpty(roleIds)) {
            List<AuthRoleGroupRulePo> list = authRoleGroupRuleService.list(Wrappers.<AuthRoleGroupRulePo>lambdaQuery().eq(AuthRoleGroupRulePo::getDeleted, 0));
            Map<Long, Long> roleRuleMap = list.stream().collect(Collectors.toMap(AuthRoleGroupRulePo::getRoleId, AuthRoleGroupRulePo::getGroupRuleId, (k1, k2) -> k1));

            for (String roleId : roleIds) {
                if (roleRuleMap.containsKey(Long.valueOf(roleId))) {
                    Long ruleId = roleRuleMap.get(Long.valueOf(roleId));
                    authGroupRuleSubjectDomainService.createSubject(ruleId, Arrays.asList(Long.valueOf(subjectId)), roleId);
                }

            }
        }
    }

    private void checkConfigRole(String subjectId) {
        checkConfigRole(Lists.newArrayList(Long.valueOf(subjectId)));
    }

    public void checkConfigRole(List<Long> subjectIds) {
        List<AuthRoleAndRuleDto> roles = loadRoleBySubjectId(subjectIds);
        for (AuthRoleAndRuleDto role : roles) {
            authGroupRuleSubjectDomainService.checkConfigRole(String.valueOf(role.getRoleId()), role.getRuleId());
        }
    }


    /**
     * 获取角色下的查看用户分页
     *
     * @param roleIds
     * @param queryPageBean
     * @return
     */
    public PageResult<AuthSubjectInfoVo> getSubjectPageOfRole(List<Long> roleIds, QueryPageBean queryPageBean) {
        if (CollectionUtils.isEmpty(roleIds)) {
            throw new ServerException("roleIds is null");
        }
        val ruleIds = authRoleGroupService.getRoleDefaultGroupRuleId(roleIds);
        return getSubjectPage(ruleIds, queryPageBean);
    }

    public PageResult<AuthSubjectInfoVo> getSubjectPageOfRoles(List<Long> roleIds, QueryPageBean queryPageBean) {
        if (CollectionUtils.isEmpty(roleIds)) {
            throw new ServerException("roleIds is empty");
        }
        if (queryPageBean == null) {
            queryPageBean = new QueryPageBean();
        }
        if (CollectionUtils.isEmpty(queryPageBean.getFilterList())) {
            List<FilterBean> list = Lists.newArrayList();
            var filterBean = new FilterBean();
            filterBean.setProp("t1.status");
            filterBean.setOp(OpEnum.eq);
            filterBean.setValue(1);
            list.add(filterBean);
            queryPageBean.setFilterList(list);
        }
        var ruleIds = authRoleGroupService.getRoleDefaultGroupRuleId(roleIds);
        var page = getSubjectPage(ruleIds, queryPageBean);
        var itemList = Sequences.sequence(page.getItems()).unique(AuthSubjectInfoVo::getSubjectId).toList();
        page.setItems(itemList);
        return page;
    }

    /**
     * 查看用户分页
     *
     * @param ruleIds
     * @param queryPageBean
     * @return
     */
    public PageResult<AuthSubjectInfoVo> getSubjectPage(List<Long> ruleIds, QueryPageBean queryPageBean) {
        PageResult<AuthSubjectDo> page = null;
        try {
            page = authGroupRuleSubjectDomainService.getPage(ruleIds, queryPageBean);
        } catch (Exception e) {
            if (e instanceof ServerException) {
                throw new ServerException(e.getMessage());
            }
            log.error("{}", e);
            throw new ServerException("fail query page");
        }
        var pageResult = new PageResult<AuthSubjectInfoVo>();
        BeanUtils.copyProperties(page, pageResult, "items");
        if (!CollectionUtils.isEmpty(page.getItems())) {
            var empIdList = Sequences.sequence(page.getItems()).map(AuthSubjectDo::getEmpId).filter(it -> it != null).toList();
            List<SysEmpInfoDto> resultEmpInfoList = new ArrayList<>();
            if (!empIdList.isEmpty()) {
                var empIds = Joiner.on(",").join(empIdList);
                resultEmpInfoList = empRepositoy.getEmpInfoByEmpIds(empIds);
            }
            var sysEmpInfoDtoMap = Sequences.sequence(resultEmpInfoList)
                    .stream().collect(Collectors.toMap(SysEmpInfoDto::getEmpid, e -> e, (v1, v2) -> v1));
            var accounts = userBaseInfoDomainService.selectByAccounts(page.getItems().stream()
                    .map(it -> it.getAccount()).collect(Collectors.toList()));
            pageResult.setItems(Sequences.sequence(page.getItems()).map(e -> {
                var authSubjectInfoVo = FastjsonUtil.convertObject(e, AuthSubjectInfoVo.class);
                SysEmpInfoDto sysEmpInfoDto = sysEmpInfoDtoMap.get(e.getEmpId());
                if (sysEmpInfoDto != null) {
                    authSubjectInfoVo.setWorkno(sysEmpInfoDto.getWorkno());
                    authSubjectInfoVo.setOrganizeTxt(sysEmpInfoDto.getOrganizeTxt());
                    authSubjectInfoVo.setEmpId(sysEmpInfoDto.getEmpid());
//                    authSubjectInfoVo.setMobile(sysEmpInfoDto.getMobile());
//                    authSubjectInfoVo.setEmail(sysEmpInfoDto.getEmail());
                }
                accounts.stream().filter(it -> StringUtils.equals(it.getAccount(), e.getAccount()))
                        .findFirst().ifPresent(it -> {
                            authSubjectInfoVo.setMobile(it.getMobNum());
                            authSubjectInfoVo.setEmail(it.getEmail());
                        });
                return authSubjectInfoVo;
            }).toList());
        }
        return pageResult;
    }

    /**
     * 角色下创建员工权限
     *
     * @param authSubjectOfRoleDto
     */
    public void createSubjectOfRole(AuthSubjectOfRoleDto authSubjectOfRoleDto) {
        if (authSubjectOfRoleDto == null) {
            throw new ServerException("parameter is null");
        }
        val ruleId = getRuleIdByRole(authSubjectOfRoleDto.getRoleId());
        authGroupRuleSubjectDomainService.createSubject(ruleId, authSubjectOfRoleDto.getSubjectIdList(), String.valueOf(authSubjectOfRoleDto.getRoleId()));
    }

    /**
     * 删除角色下的员工权限
     *
     * @param
     */
    public void deleteSubjectOfRole(Long roleId, List<Long> subjectIdList) {
        val ruleId = getRuleIdByRole(roleId);
        authGroupRuleSubjectDomainService.deleteSubject(ruleId, subjectIdList, roleId);
    }

    public void deleteSubjects(String subjectIds) {
        List<AuthSubjectAndRoleVo> roleList = getRoleBySubjectIds(Arrays.stream(subjectIds.split(","))
                .map(Long::valueOf).collect(Collectors.toList()));
        for (AuthSubjectAndRoleVo roleVo : roleList) {
            for (String roleId : roleVo.getRoleIds().split(",")) {
                authGroupRuleSubjectDomainService.checkConfigRole(String.valueOf(roleId), null);
            }
        }
        authGroupRuleSubjectDomainService.deleteSubjects(subjectIds);
    }

    private Long getRuleIdByRole(Long roleId) {
        var ruleId = authRoleGroupService.getRoleDefaultGroupRuleId(Lists.newArrayList(roleId));
        if (CollectionUtils.isEmpty(ruleId)) {
            log.error("not found ruleId, roleId is = {}", roleId);
            throw new ServerException("not found ruleId");
        }
        return ruleId.get(0);
    }

    public List<String> getResourceCodeListBySubjectId(Long subjectId) {
        if (subjectId == -1L) {
            return Lists.newArrayList("PLATFORM_ADMIN");
        }
        var ruleIdList = authGroupRuleSubjectDomainService.getRuleIdListBySubject(subjectId);
        if (ruleIdList.isEmpty()) {
            return Lists.newArrayList();
        }
        var roleIdList = authRoleGroupDomainService.getRoleIdListByRuleIdList(ruleIdList);
        if (roleIdList.isEmpty()) {
            return Lists.newArrayList();
        }
        var adminRoleList = authRoleService.getRoleByCode(Lists.newArrayList(SystemRoleEnum.ADMIN.name(),SystemRoleEnum.CONFIG.name()));
        boolean isAdmin = adminRoleList.stream().filter(it -> roleIdList.contains(it.getId())).findFirst().isPresent();
        var codeList = authRoleDomainService.getResourceCodeListByRoleIdList(roleIdList);
        if (isAdmin) {
            codeList.add("isAdmin");
        }
        log.info("temp code list:" + FastjsonUtil.toJson(codeList));
        return Sequences.sequence(codeList).stream().distinct().collect(Collectors.toList());
        /*val allResource = authResourceService.list(null, null);
        List<String> result = Lists.newArrayList();
        for (String code : codeList) {
            String tempCode = code;
            outer:
            while (StringUtils.isNotEmpty(tempCode) && !result.contains(tempCode)) {
                result.add(tempCode);
                inner:
                for (AuthResourceDo resource : allResource) {
                    if (StringUtils.equals(tempCode, resource.getCode())) {
                        tempCode = resource.getParentCode();
                        continue outer;
                    }
                }
                tempCode = null;
            }
        }

        return Sequences.sequence(result).stream().distinct().collect(Collectors.toList());*/
    }

    public Map<String, String> getResourceScopeDetailBySubjectId(Long subjectId, String parentCode) {
        var ruleIdList = authGroupRuleSubjectDomainService.getRuleIdListBySubject(subjectId);
        if (ruleIdList.isEmpty()) {
            return Maps.newHashMap();
        }
        var roleIdList = authRoleGroupDomainService.getRoleIdListByRuleIdList(ruleIdList);
        var detail = authRoleDomainService.getResourceScopeDetailBySubjectId(roleIdList, parentCode);
        return detail;
    }

    public List<String> getResourceCodeBySubjectId(Long subjectId, String parentCode) {
        if (subjectId == null) {
            return Lists.newArrayList();
        }
        var ruleIdList = authGroupRuleSubjectDomainService.getRuleIdListBySubject(subjectId);
        if (CollectionUtils.isEmpty(ruleIdList)) {
            return Lists.newArrayList();
        }
        var roleIdList = authRoleGroupDomainService.getRoleIdListByRuleIdList(ruleIdList);
        return authRoleDomainService.getResourceCodeBySubjectId(roleIdList, parentCode);
    }

    public List<String> getResourceUrlListBySubjectId(Long subjectId) {
        if (subjectId == -1L) {
            return Lists.newArrayList(InitResource.platformAdminUrl);
        }
        var ruleIdList = authGroupRuleSubjectDomainService.getRuleIdListBySubject(subjectId);
        if (ruleIdList.isEmpty()) {
            return Lists.newArrayList();
        }
        List<AuthRoleAndRuleDto> roleByRuleIds = authRoleDomainService.getRoleByRuleIds(ruleIdList);
        var roleIdList = Sequences.sequence(roleByRuleIds).map(AuthRoleAndRuleDto::getRoleId).toList();
        if (roleIdList.isEmpty()) {
            return Lists.newArrayList();
        }
        var urlList = authRoleDomainService.getResourceUrlListByRoleIdList(roleIdList);
        log.info(subjectId + "," + FastjsonUtil.toJson(roleIdList) + " result:" + FastjsonUtil.toJson(urlList));
        systemRoleAuth(roleByRuleIds, urlList);

        if (!CollectionUtils.isEmpty(commonAuthList)) {
            urlList.addAll(commonAuthList);
        }
        return urlList;
    }

    private void systemRoleAuth(List<AuthRoleAndRuleDto> roleByRuleIds, List<String> urlList) {
        for (String code : Sequences.sequence(roleByRuleIds).map(AuthRoleAndRuleDto::getCode)
                .filter(code -> StringUtils.isNotEmpty(code))) {
            SystemRoleEnum role = SystemRoleEnum.getByCode(code);
            if (role != null) {
                urlList.addAll(role.staticAuthUrl());
            }
        }
    }

    /**
     * 导入默认管理员权限
     *
     * @param subjects
     */
    public void authDefaultAdmin(List<Long> subjects) {
        if (CollectionUtils.isEmpty(subjects)) {
            log.info("auth admin role, subjects is empty");
        }
        List<AuthRoleDo> authRoleDoList = authRoleService.getRoleByCode(Lists.newArrayList("ADMIN"));
        if (CollectionUtils.isEmpty(authRoleDoList)) {
            log.info("auth admin role, not find admin role");
        }
        var roleIdList = Sequences.sequence(authRoleDoList).map(AuthRoleDo::getId).toList();
        var ruleMap = Sequences.sequence(authRoleGroupDomainService.getAuthRoleGroupByRoleId(roleIdList))
                .stream().collect(Collectors.toMap(AuthDefaultRuleOfRoleDto::getRoleId, AuthDefaultRuleOfRoleDto::getRuleId, (v1, v2) -> v1));
        // 关联当前的ruleMap参数
        if (!ruleMap.isEmpty()) {
            ruleMap.keySet().forEach(it -> {
                SecurityUserInfo securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
                threadPoolExector.getThreadPool().execute(() -> {
                    try {
                        if (securityUserInfo != null) {
                            SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
                        }
                        final Long ruleId = ruleMap.get(it);
                        authGroupRuleSubjectDomainService.createSubject(ruleId, subjects, String.valueOf(it));
                    } finally {
                        SecurityUserUtil.removeSecurityUserInfo();
                    }
                });
            });
        }
    }

    /**
     * 导入员工授权
     *
     * @param authToSubjectList
     */
    public List<AuthImportSubjectErrorVo> authorizationToUser(List<AuthToSubjectDto> authToSubjectList) {
        if (CollectionUtils.isEmpty(authToSubjectList)) {
            log.info("[authorizationToUser] parameter is null");
            return Lists.newArrayList();
        }
        HashSet<String> roleNameSet = Sets.newHashSet();
        Sequences.sequence(authToSubjectList).filter(e -> StringUtils.isNotBlank(e.getRoleName()))
                .map(e -> e.getRoleName().split(","))
                .forEach(e -> {
                    for (String s : e) {
                        if (StringUtils.isNotBlank(s)) {
                            roleNameSet.add(s.trim());
                        }
                    }
                });
        var roleNameList = Lists.newArrayList(roleNameSet);
        var roleList = authRoleDomainService.getRoleIdAndNameByRoleName(roleNameList);
        for (AuthRoleDo roleDo : roleList) {
            authGroupRuleSubjectDomainService.checkConfigRole(roleDo, null);
        }
        var roleMap = Sequences.sequence(roleList).stream()
                .collect(Collectors.toMap(e -> e.getName(), e -> e.getId(), (v1, v2) -> v1));

        var roleIdList = Sequences.sequence(roleList).map(AuthRoleDo::getId).toList();
        var ruleMap = Sequences.sequence(authRoleGroupDomainService.getAuthRoleGroupByRoleId(roleIdList))
                .stream().collect(Collectors.toMap(e -> e.getRoleId(), e -> e.getRuleId(), (v1, v2) -> v1));
        //key:ruleId value:subjectId
        HashMap<Pair<Long, Long>, List<Long>> createSubjectMap = Maps.newHashMap();
        List<AuthImportSubjectErrorVo> errorVoList = Lists.newArrayList();
        for (AuthToSubjectDto authToSubjectDto : authToSubjectList) {
            String roleNames = authToSubjectDto.getRoleName();
            if (StringUtils.isNotBlank(roleNames)) {
                String[] roleNameArr = roleNames.split(",");
                AuthImportSubjectErrorVo entity = null;
                for (String roleName : roleNameArr) {
                    var roleId = roleMap.get(roleName.trim());
                    var ruleId = ruleMap.get(roleId);
                    if (ruleId != null) {
                        List<Long> subjectList = createSubjectMap.get(ruleId) != null ? createSubjectMap.get(ruleId) : Lists.newArrayList();
                        subjectList.add(authToSubjectDto.getSubjectId());
                        createSubjectMap.put(Pair.pair(roleId, ruleId), subjectList);
                    } else {
                        if (entity == null) {
                            entity = new AuthImportSubjectErrorVo();
                            entity.setSubjectId(authToSubjectDto.getSubjectId());
                        }
                        String errorMsg = String.format("not found %s", roleName);
                        if (StringUtils.isNotBlank(entity.getErrorMsg())) {
                            errorMsg = String.format("%s;%s", entity.getErrorMsg(), errorMsg);
                        }
                        entity.setErrorMsg(errorMsg);
                    }
                }
                if (entity != null) {
                    errorVoList.add(entity);
                }
            }
        }

        if (!createSubjectMap.isEmpty()) {
            SecurityUserInfo securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
            threadPoolExector.getThreadPool().execute(() -> {
                try {
                    if (securityUserInfo != null) {
                        SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
                    }
                    Iterator<Map.Entry<Pair<Long, Long>, List<Long>>> iterator = createSubjectMap.entrySet().iterator();
                    while (iterator.hasNext()) {
                        Map.Entry<Pair<Long, Long>, List<Long>> next = iterator.next();
                        Long ruleId = next.getKey().second();
                        Long roleId = next.getKey().first();
                        List<Long> subjectIdList = next.getValue();
                        authGroupRuleSubjectDomainService.createSubject(ruleId, subjectIdList, String.valueOf(roleId));
                    }
                } finally {
                    SecurityUserUtil.removeSecurityUserInfo();
                }
            });
        }

        return errorVoList;
    }

    public List<AuthRoleAndRuleDto> loadRoleBySubjectId(List<Long> subjectIdList) {
        List<AuthRuleIdAndSubjectIdDto> subjectAndRuleList = authGroupRuleSubjectDomainService.getRuleIdAndSubjectIdBySubjectIds(subjectIdList);
        if (CollectionUtils.isEmpty(subjectAndRuleList)) {
            return Lists.newArrayList();
        }
        var ruleIdList = subjectAndRuleList.stream().map(e -> e.getRuleId()).distinct().collect(Collectors.toList());
        List<AuthRoleAndRuleDto> roleAndRuleList = authRoleDomainService.getRoleByRuleIds(ruleIdList);
        if (CollectionUtils.isEmpty(roleAndRuleList)) {
            return Lists.newArrayList();
        }
        return roleAndRuleList;
    }

    public List<AuthSubjectAndRoleVo> getRoleBySubjectIds(List<Long> subjectIdList) {
        if (CollectionUtils.isEmpty(subjectIdList)) {
            return Lists.newArrayList();
        }
        List<AuthRuleIdAndSubjectIdDto> subjectAndRuleList = authGroupRuleSubjectDomainService.getRuleIdAndSubjectIdBySubjectIds(subjectIdList);
        if (CollectionUtils.isEmpty(subjectAndRuleList)) {
            return Lists.newArrayList();
        }
        var ruleIdList = subjectAndRuleList.stream().map(e -> e.getRuleId()).distinct().collect(Collectors.toList());
        List<AuthRoleAndRuleDto> roleAndRuleList = authRoleDomainService.getRoleByRuleIds(ruleIdList);
        if (CollectionUtils.isEmpty(roleAndRuleList)) {
            return Lists.newArrayList();
        }

        List<AuthSubjectAndRoleVo> resultList = Lists.newArrayList();
        var ruleAndRoleMap = Sequences.sequence(roleAndRuleList).toMap(AuthRoleAndRuleDto::getRuleId);

        Sequences.sequence(subjectAndRuleList).toMap(e -> e.getSubjectId()).forEach((e, t) -> {
            var authSubjectAndRoleVo = new AuthSubjectAndRoleVo();
            authSubjectAndRoleVo.setSubjectId(e);
            var roleNameStr = new StringBuilder();
            var roleCodeStr = new StringBuilder();
            var roleIdStr = new StringBuilder();
            for (AuthRuleIdAndSubjectIdDto authRuleIdAndSubjectIdDto : t) {
                var roleAndRuleDtoList = ruleAndRoleMap.get(authRuleIdAndSubjectIdDto.getRuleId());
                if (!CollectionUtils.isEmpty(roleAndRuleDtoList)) {
                    var roleNames = Joiner.on(",").join(Sequences.sequence(roleAndRuleDtoList).map(en -> en.getRoleName()).toList());
                    if (roleNameStr.length() > 0) {
                        roleNameStr.append(",");
                    }
                    roleNameStr.append(roleNames);

                    var codeList = roleAndRuleDtoList.stream().filter(en -> StringUtils.isNotBlank(en.getCode()))
                            .map(en -> en.getCode()).collect(Collectors.toList());
                    var codesStr = Joiner.on(",").join(codeList);
                    if (roleCodeStr.length() > 0) {
                        roleCodeStr.append(",");
                    }
                    roleCodeStr.append(codesStr);

                    var roleIds = Joiner.on(",").join(Sequences.sequence(roleAndRuleDtoList).map(en -> en.getRoleId()).toList());
                    if (roleIdStr.length() > 0) {
                        roleIdStr.append(",");
                    }
                    roleIdStr.append(roleIds);
                }
            }
            authSubjectAndRoleVo.setRoleNames(roleNameStr.toString());
            authSubjectAndRoleVo.setCodes(roleCodeStr.toString());
            authSubjectAndRoleVo.setRoleIds(roleIdStr.toString());
            resultList.add(authSubjectAndRoleVo);
        });
        return resultList;
    }

    public List<Long> getRoleIdsBySubject(Long subjectId) {
        if (subjectId == -1l) {
            return Lists.newArrayList();
        }
        var ruleIdList = authGroupRuleSubjectDomainService.getRuleIdListBySubject(subjectId);
        if (ruleIdList.isEmpty()) {
            return Lists.newArrayList();
        }
        var roleIdList = authRoleGroupDomainService.getRoleIdListByRuleIdList(ruleIdList);
        return roleIdList;
    }


    public List<AuthRoleScopeDto> getScopeBySubjectId(Long subjectId) {
        val roleIds = getRoleIdsBySubject(subjectId);
        if (roleIds.isEmpty()) {
            return Lists.newArrayList();
        }
        val scopes = AuthRoleScopeDo.list(roleIds, AuthRoleScopeEnum.DATA_SCOPE);
        val tenantConfig = maintenanceFeign.tenantCommonConfig().getData();
        List<AuthRoleScopeDo> result = null;
        if (tenantConfig.getShowDataWhenScopeNotSet()) {
            result = roleIds.stream().anyMatch(it ->
                    !scopes.stream().map(scope -> scope.getRoleId()).collect(Collectors.toList())
                            .contains(it)
            ) ? Lists.newArrayList() : scopes;
        } else {
            List<String> allCode = loadAllCode();
            result = roleIds.stream().map(roleId -> {
                List<AuthRoleScopeDo> roleScopes = scopes.stream().filter(it -> it.getRoleId().equals(roleId)).collect(Collectors.toList());
                List<String> setTargets = roleScopes.stream().map(it -> Lists.newArrayList(it.getTargets().split(",")))
                        .flatMap(Collection::stream).collect(Collectors.toList());
                List<String> notSetTargets = allCode.stream()
                        .filter(it -> !setTargets.contains(it)).collect(Collectors.toList());
                AuthRoleScopeDo authRoleScopeDo = new AuthRoleScopeDo();
                authRoleScopeDo.setRestriction(AuthRoleScopeRestriction.NO_AUTH);
                authRoleScopeDo.setRoleId(roleId);
                authRoleScopeDo.setSimpleValues(null);
                authRoleScopeDo.setTargets(StringUtils.join(notSetTargets, ","));
                roleScopes.add(authRoleScopeDo);
                return roleScopes;
            }).flatMap(Collection::stream).collect(Collectors.toList());
        }
        if (result == null) {
            return Lists.newArrayList();
        }
        return FastjsonUtil.convertList(result, AuthRoleScopeDto.class);
    }

    private List<String> loadAllCode() {
        List<String> all = Sequences.sequence(loadAuthScope()).map(AuthRoleScopeTargetDo::getCode).toList();
        List<String> transferCode = Sequences.sequence(loadTransferScopeCode())
                .map(AuthRoleScopeTargetVo::getCode).toList();
        all.addAll(transferCode);
        return all;
    }

    @Cache(expire = 300, key = "'auth-data-scope-' + #args[0] + '-report-' + #args[1]")
    public List<AuthRoleScopeDto> getReportScopeBySubjectId(Long subjectId, String appId) {
        if (subjectId == null || StringUtils.isBlank(appId)) {
            return Lists.newArrayList();
        }
        val roleIds = getRoleIdsBySubject(subjectId);
        if (roleIds.isEmpty()) {
            return Lists.newArrayList();
        }
        val reportScopes = AuthRoleScopeDo.list(roleIds, AuthRoleScopeEnum.REPORT_SCOPE);
        if (CollectionUtils.isEmpty(reportScopes)) {
            return Lists.newArrayList();
        }
        List<AuthRoleScopeDo> result = reportScopes.stream().filter(e -> {
            if (StringUtils.isBlank(e.getTargets()) || "[]".equals(e.getTargets())) {
                return false;
            }
            return FastjsonUtil.toObject(e.getTargets(), new TypeReference<List<Map<String, Object>>>() {
            }).stream().anyMatch(it -> {
                return it.containsKey("appId") && Objects.equals(it.get("appId"), appId);
            });
        }).collect(Collectors.toList());
        return FastjsonUtil.convertList(result, AuthRoleScopeDto.class);
    }

    public Map<String, List<AuthRoleScopeRestriction>> getStandardAuthScope() {
        Map<String, List<AuthRoleScopeRestriction>> resultMap = Maps.newHashMap();
        val authSopeMap = loadAuthScope().stream().collect(Collectors.toMap(
                target -> target.getCode(), target -> target.getDetails()
                        .stream().map(it -> it.getRestriction()).distinct().collect(Collectors.toList())));
        if (!CollectionUtils.isEmpty(authSopeMap)) {
            resultMap.putAll(authSopeMap);
        }
        val transferScopeList = loadTransferScopeCode();
        if (!CollectionUtils.isEmpty(transferScopeList)) {
            var restrictionList = Lists.newArrayList(AuthRoleScopeRestriction.MY_ORG_AND_BELONGINGS,
                    AuthRoleScopeRestriction.MY_ORG, AuthRoleScopeRestriction.SELECTED_ORG, AuthRoleScopeRestriction.SELECTED_ORG_AND_BELONGINGS);
            val transferMap = transferScopeList.stream()
                    .collect(Collectors.toMap(e -> e.getCode(), e -> restrictionList, (v1, v2) -> v1));
            resultMap.putAll(transferMap);
        }
        return resultMap;
    }

    public List<AuthRoleScopeTargetDo> loadAuthScope() {
        List<AuthRoleScopeTargetDo> custom = AuthRoleScopeTargetDo.loadAll();
        List<AuthRoleScopeTargetDo> standard = Sequences.sequence(AuthRoleScopeTarget.values())
                .map(target -> {
                    AuthRoleScopeTargetDo targetDo = new AuthRoleScopeTargetDo();
                    targetDo.setCode(target.name());
                    targetDo.setDetails(target.getTargetDetails());
                    return targetDo;
                }).toList();
        standard.addAll(custom);
        return standard;
    }

    public List<AuthRoleScopeTargetVo> loadAuthScopeKvList() {
        List<AuthRoleScopeTargetVo> list = Lists.newArrayList();
        var scopeList1 = AuthRoleScopeTargetDo.loadAll().stream().map(scope -> ObjectConverter.convert(scope, AuthRoleScopeTargetVo.class)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(scopeList1)) {
            list.addAll(scopeList1);
        }
        var scopeList2 = loadTransferScopeCode();
        if (!CollectionUtils.isEmpty(scopeList2)) {
            list.addAll(scopeList2);
        }
        return list;
    }

    public List<AuthRoleScopeTargetVo> loadTransferScopeCode() {
        List<AuthResourceDo> transferResourceList = authResourceService.list(ResourceCategoryEnum.MENU, "transfer");
        if (CollectionUtils.isEmpty(transferResourceList)) {
            return Lists.newArrayList();
        }
        return transferResourceList.stream().map(e -> new AuthRoleScopeTargetVo(e.getCode(), String.format("人事异动-%s", e.getName()))).collect(Collectors.toList());
    }

    @Transactional
    public List<AuthImportSubjectErrorVo> refreshAuthorizationToUser(List<AuthToSubjectDto> authToSubjectList) {
        clearAuth(authToSubjectList);
        return authorizationToUser(authToSubjectList);
    }

    private void clearAuth(List<AuthToSubjectDto> authToSubjectList) {
        List<Long> subjectList = Sequences.sequence(authToSubjectList).map(AuthToSubjectDto::getSubjectId).toList();
        authGroupRuleSubjectDomainService.deleteSubjects(StringUtils.join(subjectList, ","));
    }
}