package com.caidaocloud.auth.service.caidao.application.factory;

import com.caidaocloud.auth.service.caidao.application.dto.AuthResourceDto;
import com.caidaocloud.auth.service.caidao.application.dto.bi.MenuDto;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceActionEnum;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum;
import com.caidaocloud.auth.service.caidao.infrastructure.constant.CodeConstant;
import lombok.var;

public class AuthResourceFactory {
    public static AuthResourceDto build(MenuDto menuDto, String tenantId) {
        var authResourceDto = new AuthResourceDto();
        authResourceDto.setCode(String.format(CodeConstant.RESOURCE_PREFIX, menuDto.getPublishedBy()));
        authResourceDto.setName(menuDto.getName());
        authResourceDto.setLang("zh");
        authResourceDto.setCategory(ResourceCategoryEnum.MENU);
        authResourceDto.setUrl(menuDto.getUrl());
        authResourceDto.setResourceAction(ResourceActionEnum.VIEW);
        authResourceDto.setParentCode(CodeConstant.BI_REPORT_MENU_CODE);
        authResourceDto.setTenantId(tenantId);
        return authResourceDto;
    }
}
