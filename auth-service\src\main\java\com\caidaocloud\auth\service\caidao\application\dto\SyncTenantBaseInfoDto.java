package com.caidaocloud.auth.service.caidao.application.dto;

import com.caidaocloud.auth.service.caidao.application.enums.DataOpEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("租户信息同步dto")
public class SyncTenantBaseInfoDto extends TenantBaseInfoDto {
    @ApiModelProperty("操作类型：INSERT,UPDATE,DELETE")
    private DataOpEnum op;
}
