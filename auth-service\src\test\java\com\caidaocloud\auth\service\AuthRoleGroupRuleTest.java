package com.caidaocloud.auth.service;

import com.caidaocloud.auth.service.caidao.application.service.AuthRoleService;
import com.caidaocloud.auth.service.caidao.domain.service.AuthGroupRuleSubjectDomainService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@SpringBootTest(classes = AuthApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class AuthRoleGroupRuleTest {
    @Autowired
    private AuthRoleService oAuthRoleService;
    @Autowired
    private AuthGroupRuleSubjectDomainService authGroupRuleSubjectDomainService;


    @Before
    public void before() {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        userInfo.setUserId(0L);
        userInfo.setEmpId(0L);
        userInfo.setIsAdmin(false);
        userInfo.setLang("");
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }

    @Test
    public void test1() {
        oAuthRoleService.deleteRole(Lists.newArrayList(26L));
    }

    @Test
    public void test122() {
        authGroupRuleSubjectDomainService.deleteByRuleId(58L, "");
    }
}
