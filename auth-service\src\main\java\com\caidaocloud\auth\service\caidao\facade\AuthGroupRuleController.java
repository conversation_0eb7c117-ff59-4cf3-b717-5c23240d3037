package com.caidaocloud.auth.service.caidao.facade;

import com.caidaocloud.auth.service.caidao.application.dto.AuthRoleGroupDto;
import com.caidaocloud.auth.service.caidao.application.dto.subject.AuthGroupRuleSubjectPageDto;
import com.caidaocloud.auth.service.caidao.application.service.AuthGroupRuleService;
import com.caidaocloud.auth.service.caidao.application.service.AuthGroupRuleSubjectService;
import com.caidaocloud.auth.service.caidao.facade.dto.AuthGroupRuleDto;
import com.caidaocloud.auth.service.caidao.facade.vo.AuthSubjectInfoVo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Strings;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "权限角色组操作")
@RestController
@RequestMapping("/api/auth/v1/groupRule")
public class AuthGroupRuleController {
    @Autowired
    private AuthGroupRuleService authGroupRuleService;
    @Autowired
    private AuthGroupRuleSubjectService authGroupRuleSubjectService;

    @ApiOperation(value = "保存角色组")
    @PostMapping("/createGroupRule")
    public Result createGroupRule(@RequestBody AuthRoleGroupDto authRoleGroupDto) {
        authGroupRuleService.saveOrUpdateRoleGroup(authRoleGroupDto);
        return Result.ok();
    }

    @ApiOperation(value = "更新角色组")
    @PutMapping("/updateGroupRule")
    public Result updateGroupRule(@RequestBody AuthRoleGroupDto authRoleGroupDto) {
        authGroupRuleService.saveOrUpdateRoleGroup(authRoleGroupDto);
        return Result.ok();
    }

    @ApiOperation(value = "查询角色组")
    @PostMapping("/getGroupRuleById")
    public Result<AuthGroupRuleDto> getGroupRuleById(@RequestParam("id") Long id) {
        return Result.ok(authGroupRuleService.getGroupRuleById(id));
    }

    @ApiOperation(value = "删除角色组")
    @GetMapping("/deleteGroupRule")
    public Result deleteGroupRule(@RequestParam(value = "ids") @ApiParam(value = "角色组id,多值用逗号分隔") String ids) {
        try {
            authGroupRuleService.deleteGroupRule(Strings.split(",").call(ids)
                    .filter(e -> StringUtils.isNotBlank(e)).map(e -> Long.parseLong(e)).toList());
        } catch (Exception e) {
            return Result.fail();
        }
        return Result.ok();
    }

    @ApiOperation(value = "角色组分页")
    @PostMapping("/pageList")
    public Result<PageResult<AuthGroupRuleDto>> pageList(QueryPageBean queryPageBean) {
        return Result.ok(authGroupRuleService.pageList(queryPageBean));
    }

    @ApiOperation(value = "角色组管理查看用户分页")
    @PostMapping("/queryUserPage")
    public Result<PageResult<AuthSubjectInfoVo>> queryUserPage(@RequestBody AuthGroupRuleSubjectPageDto authGroupRuleSubjectPageDto) {
        if (authGroupRuleSubjectPageDto == null) {
            throw new ServerException("authGroupRuleSubjectPageDto parameter is null");
        }
        return Result.ok(authGroupRuleSubjectService.getSubjectPage(Lists.newArrayList(authGroupRuleSubjectPageDto.getRuleId()), authGroupRuleSubjectPageDto.getQueryPageBean()));
    }

    @ApiOperation(value = "角色组同步用户")
    @PostMapping("/synUser")
    public Result synUser(@RequestParam(value = "ids") @ApiParam(value = "角色组id,多值用逗号分隔") String ids ) {
        try {
            List<Long> idList = Strings.split(",").call(ids).filter(StringUtils::isNotBlank).map(Long::parseLong).toList();
            authGroupRuleService.synUser(idList);
        }catch (ServerException e){
            throw e;
        }
        catch (Exception e) {
            log.error("synUser err,{}", e.getMessage(), e);
            return Result.fail();
        }
        return Result.ok();
    }

    @ApiOperation(value = "角色组下删除用户")
    @DeleteMapping("/user")
    public Result deleteSubject(@RequestParam(value = "subjectIds")
          @ApiParam(value = "用户id,多值用逗号分隔") String subjectIds,
          @RequestParam(value = "ruleId") @ApiParam(value = "角色组id") Long ruleId) {
        try {
            authGroupRuleService.deleteSubject(subjectIds, ruleId);
        } catch (Exception e) {
            log.error("deleteSubject fail, {} \n {}", e.getMessage(), e);
        }
        return Result.ok();
    }
}
