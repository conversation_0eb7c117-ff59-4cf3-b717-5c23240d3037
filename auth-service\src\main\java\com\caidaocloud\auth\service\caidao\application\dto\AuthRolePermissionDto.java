package com.caidaocloud.auth.service.caidao.application.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/7
 **/
@Data
@ApiModel(value = "角色权限映射dto")
public class AuthRolePermissionDto {

    @ApiModelProperty("角色权限映射id")
    private Long id;

    @ApiModelProperty("角色id")
    private Long roleId;

    @ApiModelProperty("资源编码")
    @JSONField(name = "resourceCode")
    private String code;

    @ApiModelProperty("父级编码")
    private String parentCode;

    @ApiModelProperty("数据权限详情")
    private String dataScopeDetail;

    @ApiModelProperty("资源分类")
    private ResourceCategoryEnum category;

}
