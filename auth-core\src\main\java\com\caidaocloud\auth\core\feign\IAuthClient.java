package com.caidaocloud.auth.core.feign;

import java.util.List;

import com.caidaocloud.auth.core.dto.AuthRoleScopeFilterDetailDto;
import com.caidaocloud.auth.core.dto.AuthRoleScopeTargetRegisterDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 *
 * <AUTHOR>
 * @date 2024/1/8
 */
@FeignClient(
		value = "${feign.rename.caidaocloud-auth-service:caidaocloud-auth-service}",
		fallback = AuthFeignFallback.class,
		configuration = {FeignConfiguration.class},
		contextId = "authScopeFeign"
)
public interface IAuthClient {
	@GetMapping({"/api/auth/v1/subject/role/scope/list"})
	Result<List<AuthRoleScopeFilterDetailDto>> getRoleScope(@RequestParam("identifier") String identifier, @RequestParam("subjectId") String subjectId);


	@PostMapping("/api/auth/v1/scope/register")
	Result register(@RequestBody AuthRoleScopeTargetRegisterDto dto);
}
