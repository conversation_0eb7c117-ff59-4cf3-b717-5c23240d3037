package com.caidaocloud.auth.service.caidao.facade;

import com.caidaocloud.auth.service.caidao.application.service.AuthRoleService;
import com.caidaocloud.auth.service.caidao.application.dto.AuthRoleCopyDto;
import com.caidaocloud.auth.service.caidao.application.dto.AuthRoleDto;
import com.caidaocloud.auth.service.caidao.facade.vo.AuthRoleVo;
import com.caidaocloud.auth.service.caidao.facade.vo.RoleDetailVo;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Strings;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/7
 **/
@Api(tags = "权限角色操作")
@Controller
@RequestMapping("/api/auth/v1")
public class AuthRoleController {
    @Resource
    private AuthRoleService oAuthRoleService;

    @ApiOperation(value = "保存角色")
    @PostMapping("/role")
    @ResponseBody
    public Result<String> createRole(@RequestBody AuthRoleDto authRoleDto) {
        oAuthRoleService.saveOrUpdateRole(authRoleDto);
        return Result.ok("success");
    }

    @ApiOperation(value = "更新角色")
    @PutMapping("/role")
    @ResponseBody
    public Result<String> updateRole(@RequestBody AuthRoleDto authRoleDto) {
        oAuthRoleService.saveOrUpdateRole(authRoleDto);
        return Result.ok("success");
    }

    @ApiOperation(value = "获取角色")
    @GetMapping("/role")
    @ResponseBody
    public Result<RoleDetailVo> getPermissionOfRole(@RequestParam("roleId") Long roleId) {
        return Result.ok(oAuthRoleService.getRoleByRoleId(roleId));
    }

    @ApiOperation(value = "删除角色")
    @DeleteMapping("/role")
    @ResponseBody
    public Result<String> deleteRole(@RequestParam(value = "roleIds")
                                     @ApiParam(value = "角色id,多值用逗号分隔")
                                     String roleIds) {
        try {
            oAuthRoleService.deleteRole(Strings.split(",").call(roleIds)
                    .filter(e -> StringUtils.isNotBlank(e)).map(e -> Long.parseLong(e)).toList());
        } catch (Exception e) {
            return Result.fail();
        }
        return Result.ok("success");
    }

    @ApiOperation(value = "复制角色")
    @PostMapping("/role/copy")
    @ResponseBody
    public Result<String> copyRole(@RequestBody AuthRoleCopyDto authRoleCopyDto) {
        if (authRoleCopyDto == null) {
            throw new ServerException("parameter is null");
        }
        oAuthRoleService.copyRole(authRoleCopyDto.getRoleId());
        return Result.ok("success");
    }

    @ApiOperation(value = "角色分页")
    @GetMapping("/role/query-page")
    @ResponseBody
    public Result<PageResult<AuthRoleVo>> queryPageOfRole(QueryPageBean queryPageBean) {
        return Result.ok(oAuthRoleService.queryPageOfRole(queryPageBean));
    }

    @ApiOperation(value = "获取所有启动角色")
    @GetMapping("/role/enabled/all")
    @ResponseBody
    public Result<List<KeyValue>> getAllEnabledRole(@RequestParam(required = false) String param) {
        return Result.ok(oAuthRoleService.getEnabledOfAllRole(param));
    }

    @ApiOperation(value = "历史角色自动添加模块权限")
    @PostMapping("/refresh")
    @ResponseBody
    public Result<String> moduleRefresh(@RequestParam(required = false) Long roleId) {
        oAuthRoleService.moduleRefresh(roleId);
        return Result.ok("success");
    }

}
