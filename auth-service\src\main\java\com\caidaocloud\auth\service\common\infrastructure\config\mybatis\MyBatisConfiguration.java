package com.caidaocloud.auth.service.common.infrastructure.config.mybatis;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.caidaocloud.auth.service.caidao.infrastructure.util.UserContextUtil;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * mybatis配置
 *
 * <AUTHOR>
 * @date 2022/5/7
 **/
@Configuration
@MapperScan(basePackages = {"com.caidaocloud.auth.service.**.mybatis"})
@Slf4j
public class MyBatisConfiguration {
    @NacosValue("${caidaocloud.dbType:mysql}")
    private String dbType;
    private final List<String> ignoreTableNames = Lists.newArrayList("auth_resource", "IF", "if", "user_base_info",
            "account_base_info", "oauth_client", "oauth_code_store", "oauth_open_tenant", "tenant_base_info",
            "external_app_security");
    private final List<String> ignoreSqls = Lists.newArrayList("CREATE TABLE");

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor(getDbType());
        mybatisPlusInterceptor.addInnerInterceptor(paginationInnerInterceptor);
        DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
        dynamicTableNameInnerInterceptor.setTableNameHandler(((sql, tableName) -> {
            if (ignoreTableNames.contains(tableName) || sqlIsContain(sql)) {
                return tableName;
            }
            ISessionService userService = SpringUtil.getBean(ISessionService.class);
            String tenantId = null;
            try {
                tenantId = userService.getTenantId();
            } catch (Exception e) {
                tenantId = UserContextUtil.getTenantId();
                if (StringUtils.isBlank(tenantId)) {
                    log.error("getTenantId fail, {}", e);
                    throw new ServerException("nout found tenantId");
                }
            }
            if (StringUtils.isBlank(tenantId)) {
                tenantId = UserContextUtil.getTenantId();
            }
            return String.format("%s_%s", tableName, tenantId);
        }));
        mybatisPlusInterceptor.addInnerInterceptor(dynamicTableNameInnerInterceptor);

        mybatisPlusInterceptor.addInnerInterceptor(new ReservedWordReplaceInnerInterceptor(getEscapeSymbol()));

        return mybatisPlusInterceptor;
    }

    private DbType getDbType() {
        DbType type;
        switch (dbType) {
            case "pgsql":
                type = DbType.POSTGRE_SQL;
                break;
            case "mysql":
                type = DbType.MYSQL;
                break;
            default:
                throw new ServerException("no this db type");
        }
        return type;
    }

    private boolean sqlIsContain(String sql) {
        if (!CollectionUtils.isEmpty(ignoreSqls)) {
            for (String ignoreSql : ignoreSqls) {
                int i = sql.indexOf(ignoreSql);
                if (i >= 0) {
                    return true;
                }
            }
        }
        return false;
    }

    private String getEscapeSymbol() {
        switch (getDbType()) {
            case POSTGRE_SQL:
                return "\"";
            case MYSQL:
                return "`";
            default:
                return "";
        }
    }

    @Bean
    public SqlInjector sqlInjector() {
        return new SqlInjector();
    }

}
