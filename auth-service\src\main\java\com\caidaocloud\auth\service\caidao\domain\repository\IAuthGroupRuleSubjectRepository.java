package com.caidaocloud.auth.service.caidao.domain.repository;

import com.caidaocloud.auth.service.caidao.domain.entity.AuthRuleIdAndSubjectIdDo;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthSubjectDo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/11
 **/
public interface IAuthGroupRuleSubjectRepository {

    /**
     * 分页
     *
     * @param ruleId
     * @param queryPageBean
     * @return
     */
    PageResult<AuthSubjectDo> getPage(List<Long> ruleIds, QueryPageBean queryPageBean);

    /**
     * 创建权限员工
     *
     * @param ruleId
     * @param subjectIdList
     */
    void createSubject(Long ruleId, List<Long> subjectIdList);

    /**
     * 删除权限员工
     *
     * @param ruleId
     * @param subjectIdList
     */
    void deleteSubject(Long ruleId, List<Long> subjectIdList);

    void deleteByRuleId(Long ruleId);

    void deleteSubjects(String subjectIds);

    List<Long> getRuleIdListBySubject(Long subjectId);

    /**
     * 查看RuleId SubjectId
     * @param subjectIds
     * @return
     */
    List<AuthRuleIdAndSubjectIdDo> getRuleIdAndSubjectIdBySubjectIds(List<Long> subjectIds);
}
