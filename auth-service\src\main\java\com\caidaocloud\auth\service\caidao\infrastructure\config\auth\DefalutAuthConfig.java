package com.caidaocloud.auth.service.caidao.infrastructure.config.auth;

import com.caidaocloud.auth.service.caidao.domain.entity.OAuthClientDo;
import com.caidaocloud.auth.service.caidao.domain.entity.OAuthCodeStoreDo;
import com.caidaocloud.auth.service.caidao.domain.entity.OAuthOpenTenantDo;
import com.caidaocloud.auth.service.caidao.domain.repository.OAuthClientRepository;
import com.caidaocloud.auth.service.caidao.domain.repository.OAuthCodeStoreRepository;
import com.caidaocloud.auth.service.caidao.domain.repository.OAuthOpenTenantRepository;
import com.caidaocloud.util.SpringUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.code.AuthorizationCodeServices;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 提供默认 bean 实现
 * <AUTHOR>
 * @date 2011-11-11
 */
@Configuration
public class DefalutAuthConfig {

    @Bean
    @ConditionalOnMissingBean(OAuthClientRepository.class)
    public OAuthClientRepository defaultOAuthClientDetailRepository(){
        return new DefaultOAuthClientRepository();
    }

    @Bean
    @ConditionalOnMissingBean(OAuthCodeStoreRepository.class)
    public OAuthCodeStoreRepository defaultOAuthCodeStoreRepository(){
        return new DefaultOAuthCodeStoreRepository();
    }

    @Bean
    @ConditionalOnMissingBean(AuthorizationCodeServices.class)
    public AuthorizationCodeServices defaultAuthorizationCodeServices(){
        return new DefaultAuthorizationCodeServices();
    }

    @Bean
    @ConditionalOnMissingBean(OAuthOpenTenantRepository.class)
    public OAuthOpenTenantRepository defaultOAuthOpenTenantRepository(){
        return new DefaultOAuthOpenTenantRepository();
    }

    @ConditionalOnMissingBean(SpringUtil.class)
    @Bean
    public SpringUtil springContext(){
        return new SpringUtil();
    }

    private class DefaultAuthorizationCodeServices implements AuthorizationCodeServices{

        @Override
        public String createAuthorizationCode(OAuth2Authentication authentication) {
            return "";
        }

        @Override
        public OAuth2Authentication consumeAuthorizationCode(String code) throws InvalidGrantException {
            return null;
        }
    }

    private class DefaultOAuthClientRepository implements OAuthClientRepository {

        @Override
        public List<OAuthClientDo> selectListAll() {
            OAuthClientDo clientDetail = new OAuthClientDo();
            clientDetail.setClientId("3e63c2cc-9158-4568-b281-05e1af2ad54e");
            clientDetail.setResourceIds(null);
            clientDetail.setClientSecret("0edc19c2-cac9-44a1-89cc-97f8d3030d14");
            clientDetail.setScope("all");
            clientDetail.setAuthorizedGrantTypes("authorization_code,refresh_token,client_credentials");
            clientDetail.setRefreshTokenValidity(1800);
            clientDetail.setAccessTokenValidity(3600);
            clientDetail.setTenantId("caidaotest");
            clientDetail.setBelongId(56594L);
            clientDetail.setStatus(0);

            List<OAuthClientDo> list = new ArrayList();
            list.add(clientDetail);
            return list;
        }

        @Override
        public OAuthClientDo selectByClientId(String clientId) {
            return selectListAll().get(0);
        }

    }

    private class DefaultOAuthCodeStoreRepository implements OAuthCodeStoreRepository {
        Map<Long, OAuthCodeStoreDo> map = new HashMap();

        @Override
        public void insertAuthCode(OAuthCodeStoreDo codeStorePo) {
            map.put(System.currentTimeMillis(), codeStorePo);
        }
    }

    private class DefaultOAuthOpenTenantRepository implements OAuthOpenTenantRepository {

        @Override
        public OAuthOpenTenantDo selectTenantByCorpKey(String clientId, String tenantCorpKey) {
            return null;
        }

        @Override
        public void insertTenant(OAuthOpenTenantDo openTenant) {

        }

        @Override
        public Long getMaxBelongId() {
            return null;
        }
    }

}
