CREATE TABLE IF NOT EXISTS auth_group_rule_$tenant_id
(
    id                    BIGINT NOT NULL AUTO_INCREMENT COMMENT '',
    tenant_id             VARCHAR(50)  DEFAULT "" COMMENT '租户id',
    name                  VARCHAR(50)  DEFAULT "" COMMENT '规则名称',
    remark                VARCHAR(255) DEFAULT "" COMMENT '角色组描述',
    last_refreshed        BIGINT UNSIGNED DEFAULT NULL COMMENT '最近刷新时间',
    expression            JSON NULL COMMENT '规则表达式',
    status                TINYINT UNSIGNED DEFAULT 0 COMMENT '状态;0：启用 1：禁用',
    create_time           BIGINT UNSIGNED NOT NULL COMMENT '创建时间',
    create_by             VARCHAR(50)  DEFAULT "" COMMENT '创建者',
    update_time           BIGINT UNSIGNED DEFAULT NULL COMMENT '更新时间',
    update_by             VARCHA<PERSON>(50)  DEFAULT "" COMMENT '更新者',
    is_default_role_group TINYINT UNSIGNED DEFAULT 0 COMMENT '是否是角色默认角色组',
    deleted               TINYINT UNSIGNED DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT = '用户分组规则';