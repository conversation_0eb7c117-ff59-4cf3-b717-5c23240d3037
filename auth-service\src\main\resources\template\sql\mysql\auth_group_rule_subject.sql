CREATE TABLE IF NOT EXISTS auth_group_rule_subject_$tenant_id
(
    id          BIGINT(255) NOT NULL AUTO_INCREMENT COMMENT '主键唯一标识',
    rule_id     BIGINT(255) NOT NULL COMMENT '分组规则id',
    subject_id  BIGINT(255) NOT NULL COMMENT '员工id',
    tenant_id   VARCHAR(50) NOT NULL COMMENT '租户id',
    create_time BIGINT UNSIGNED NOT NULL COMMENT '创建时间',
    create_by   VARCHAR(50) DEFAULT "" COMMENT '创建者',
    update_time BIGINT UNSIGNED DEFAULT NULL COMMENT '更新时间',
    update_by   VARCHAR(50) DEFAULT "" COMMENT '更新者',
    deleted     TINYINT UNSIGNED DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT = '分组与员工对象关系';
