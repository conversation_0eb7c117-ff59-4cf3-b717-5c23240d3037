package com.caidaocloud.auth.core.scope.mybatis;

import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.SqlSource;

/**
 *
 * <AUTHOR>
 * @date 2024/1/9
 */
public class SimpleSqlSource implements SqlSource {
	private BoundSql boundSql;

	public SimpleSqlSource(BoundSql boundSql) {
		this.boundSql = boundSql;
	}

	@Override
	public BoundSql getBoundSql(Object parameterObject) {
		return boundSql;
	}

}
