package com.caidaocloud.auth.service.external.application.authenticate;

import com.caidaocloud.auth.service.external.application.authenticate.impl.AtsAuthService;
import com.caidaocloud.auth.service.external.application.authenticate.impl.HuaweiTokenAuthService;
import com.caidaocloud.auth.service.external.infrastructure.enums.ExternalAuthEnum;
import com.caidaocloud.util.SpringUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/8
 **/
@Slf4j
public class ExternalAuthenticateBuilder {
    private static final Map<ExternalAuthEnum, IExternalAuthService> container = Maps.newConcurrentMap();

    static {
        container.put(ExternalAuthEnum.ATS, SpringUtil.getBean(AtsAuthService.class));
        container.put(ExternalAuthEnum.HUAWEI_TOKEN, SpringUtil.getBean(HuaweiTokenAuthService.class));
    }

    public static IExternalAuthService getService(ExternalAuthEnum type) {
        if (type == null) {
            log.info("not found service of this type, type={}", type);
            return null;
        }
        return container.get(type);
    }
}