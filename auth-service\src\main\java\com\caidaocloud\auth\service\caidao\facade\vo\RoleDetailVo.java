package com.caidaocloud.auth.service.caidao.facade.vo;

import com.caidaocloud.auth.service.caidao.infrastructure.enums.AdapterDeviceEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/21
 **/
@Data
@ApiModel(value = "角色详情vo")
public class RoleDetailVo {

    @ApiModelProperty("角色id")
    private Long id;

    @ApiModelProperty("角色名称")
    private String name;

    @ApiModelProperty("角色名称多语言")
    private Map<String,Object> i18nName;

    @ApiModelProperty("角色code")
    private String code;

    @ApiModelProperty("角色描述")
    private String remark;

    @ApiModelProperty("是否启用")
    private Boolean isEnabled;

    @ApiModelProperty("角色类型;0：系统角色 1：自定义角色")
    private Integer roleType;

    @ApiModelProperty("适用设备")
    private List<AdapterDeviceEnum> device;

    @ApiModelProperty("权限")
    private List<AuthRolePermissionVo> authRolePermissionList;
    @ApiModelProperty("数据权限")
    private List<AuthRoleScopeVo> authRoleScopeList;
    @ApiModelProperty("报表数据权限")
    private List<AuthRoleScopeVo> reportRoleScopeList;

    public void sort() {
        int i = 1;
        if (!CollectionUtils.isEmpty(this.authRoleScopeList)) {
            for (AuthRoleScopeVo scope : this.authRoleScopeList) {
                scope.setIndex(i);
                i++;
            }
        }
        if (!CollectionUtils.isEmpty(this.reportRoleScopeList)) {
            i = 1;
            for (AuthRoleScopeVo scope : this.reportRoleScopeList) {
                scope.setIndex(i);
                i++;
            }
        }
    }
}
