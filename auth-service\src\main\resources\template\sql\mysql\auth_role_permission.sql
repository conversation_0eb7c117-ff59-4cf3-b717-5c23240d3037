CREATE TABLE IF NOT EXISTS auth_role_permission_$tenant_id
(
    id                BIGINT NOT NULL AUTO_INCREMENT,
    tenant_id         VARCHAR(50) COMMENT '租户id',
    role_id           BIGINT NOT NULL COMMENT '角色id',
    resource_code     VARCHAR(50) DEFAULT "" COMMENT '资源编码',
    create_time       BIGINT UNSIGNED NOT NULL COMMENT '创建时间',
    create_by         VARCHAR(50) DEFAULT "" COMMENT '创建者',
    update_time       BIGINT UNSIGNED DEFAULT NULL COMMENT '更新时间',
    update_by         VARCHAR(50) DEFAULT "" COMMENT '更新者',
    parent_code       VARCHAR(50) DEFAULT NULL COMMENT '父级编码',
    deleted           TINYINT UNSIGNED DEFAULT 0 COMMENT '是否删除',
    data_scope_detail text        default null comment '数据权限详情',
    category          varchar(20) default NULL COMMENT '资源分类',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT = '权限映射';