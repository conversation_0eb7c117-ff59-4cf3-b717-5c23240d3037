package com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthRolePermissionPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/7
 **/
public interface AuthRolePermissionDao extends BaseMapper<AuthRolePermissionPo> {

    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    Integer insertBatchSomeColumn(List<AuthRolePermissionPo> list);

    /**
     * 通过角色查询资源url
     *
     * @param roleIdList
     * @return
     */
    @Select("<script>" +
            " SELECT t2.url FROM auth_role_permission t1 " +
            " INNER JOIN auth_resource t2 ON t1.resource_code = t2.code " +
            " INNER JOIN auth_role t3 ON t3.id = t1.role_id " +
            " <where> " +
            "   t3.deleted = 0 and t1.deleted = 0 and t2.deleted = 0 " +
            "   <if test='roleIdList != null and roleIdList.size() > 0'>" +
            "       AND t1.role_id IN" +
            "       <foreach item='item' index='index' collection='roleIdList' open='(' separator=',' close=')'>" +
            "           #{item} " +
            "       </foreach>" +
            "   </if>" +
            "</where>" +
            "</script>")
    List<String> selectResourceUrlByRoleIds(@Param("roleIdList") List<Long> roleIdList);

}
