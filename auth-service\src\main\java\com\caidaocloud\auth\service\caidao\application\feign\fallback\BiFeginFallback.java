package com.caidaocloud.auth.service.caidao.application.feign.fallback;

import com.caidaocloud.auth.service.caidao.application.dto.bi.MenuDto;
import com.caidaocloud.auth.service.caidao.application.feign.BiFegin;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BiFeginFallback implements BiFegin {
    @Override
    public Result<List<MenuDto>> allMenus(String tenantId) {
        return Result.fail();
    }
}
