package com.caidaocloud.auth.service.caidao.application.handler;

import java.util.Map;

import javax.annotation.Resource;

import com.caidaocloud.auth.service.caidao.domain.entity.TenantBaseInfoDo;
import com.caidaocloud.auth.service.caidao.domain.repository.ITenantBaseInfoRepository;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.stereotype.Service;

@Slf4j
@Service("DefaultHandler")
public class DefaultTenantHandler implements IHandlerType {

    @Resource
    private ITenantBaseInfoRepository tenantBaseInfoRepository;

    @Override
    public void executeHandlerType(Map<String, String> sourceMap, Map<String, Object> targetMap) {
        String tenantKey = sourceMap.getOrDefault("tenantCorpKey", "");
        String clientId = sourceMap.getOrDefault("client_id", "");
        if(StringUtil.isEmpty(tenantKey) || StringUtil.isEmpty(clientId)){
            log.error("tenantCorpKey or clientId is empty, executeHandlerType end.");
            throw new BadCredentialsException("The current app has been disabled or the status is abnormal");
        }

        TenantBaseInfoDo tenantInfo = tenantBaseInfoRepository.getTenantByCode(tenantKey);
        if(null == tenantInfo){
            log.error("tenant not exist,tenant key={}", tenantKey);
            throw new ServerException("tenant not exist");
        }

        targetMap.put("belongId", tenantInfo.getTenantId());
        targetMap.put("corpId", tenantInfo.getCorpId());
    }

}
