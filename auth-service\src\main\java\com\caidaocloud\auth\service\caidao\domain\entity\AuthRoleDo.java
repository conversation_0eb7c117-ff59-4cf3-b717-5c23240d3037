package com.caidaocloud.auth.service.caidao.domain.entity;

import com.caidaocloud.auth.service.caidao.domain.repository.IAuthGroupRuleSubjectRepository;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthRoleRepository;
import com.caidaocloud.auth.service.caidao.domain.repository.OAuthClientRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.AdapterDeviceEnum;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.SystemRoleEnum;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.ObjectUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequence;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/5/10
 **/
@Data
public class AuthRoleDo {

    private Long id;

    private String name;

    private String i18nName;

    private String code;

    private String remark;

    private Integer roleType;

    private Boolean isEnabled;

    private List<AdapterDeviceEnum> device;

    private List<AuthRolePermissionDo> authRolePermissionList;

    private static IAuthRoleRepository repository() {
        return SpringUtil.getBean(IAuthRoleRepository.class);
    }

    public PageResult<AuthRoleDo> getPage(QueryPageBean queryPageBean) {
        IAuthRoleRepository repository = repository();
        if (repository == null) {
            throw new ServerException("not found IAuthRoleRepository");
        }
        return repository.getPageOfRole(queryPageBean);
    }

    public static List<AuthRoleDo> getAuthRoleByCode(List<String> codeList) {
        if (CollectionUtils.isEmpty(codeList)) {
            throw new ServerException("parameter is null");
        }
        IAuthRoleRepository repository = repository();
        return repository.selectAuthRoleByCode(codeList);
    }

    public static Option<AuthRoleDo> getById(Long id) {
        if (id == null) {
            throw new ServerException("id is null");
        }
        IAuthRoleRepository repository = repository();
        return repository.getRoleByRoleId(id);
    }

    public static Sequence<AuthRoleDo> getAllRole() {
        IAuthRoleRepository repository = repository();
        return repository.getAllRole();
    }

    public void createSubject(Long ruleId, List<Long> subjectIdList) {
        checkConfigRole();
        SpringUtil.getBean(IAuthGroupRuleSubjectRepository.class).createSubject(ruleId, subjectIdList);
    }

    public void checkConfigRole() {
        Optional<SystemRoleEnum> systemRoleEnumOptional = SystemRoleEnum.getCurrentUserRole();
        if (!systemRoleEnumOptional.isPresent() || systemRoleEnumOptional.get()!= SystemRoleEnum.CONFIG) {
            return;
        }
        //
        if (ObjectUtil.nullSafeEquals(getRoleType(), 0)) {
            throw new ServerException("不可调整配置管理员的权限");
        }
    }

    public void deleteSubject(Long ruleId, List<Long> subjectIdList) {
        checkConfigRole();
        SpringUtil.getBean(IAuthGroupRuleSubjectRepository.class).deleteSubject(ruleId, subjectIdList);
    }

    public void delete() {
        checkConfigRole();
        SpringUtil.getBean(IAuthRoleRepository.class).deleteRole(Lists.list(id));
    }
}
