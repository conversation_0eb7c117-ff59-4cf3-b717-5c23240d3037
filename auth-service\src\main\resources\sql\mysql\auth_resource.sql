CREATE TABLE IF NOT EXISTS auth_resource
(
    id                BIGINT       NOT NULL AUTO_INCREMENT COMMENT '唯一标识',
    tenant_id         VARCHAR(50)  NOT NULL COMMENT '租户id',
    name              VARCHAR(100) NOT NULL COMMENT '名称',
    code              VARCHAR(50)  NOT NULL COMMENT '资源标识code',
    lang              VARCHAR(20)  NOT NULL COMMENT '语言',
    category          varchar(20)  NOT NULL COMMENT '类别',
    url               VARCHAR(500) DEFAULT NULL COMMENT 'API链接',
    resource_action   VARCHAR(20)  DEFAULT NULL COMMENT '动作',
    parent_code       VARCHAR(50)  DEFAULT NULL COMMENT '父级编码',
    extension         text  DEFAULT NULL COMMENT '扩展字段',
    create_time       bigint       NOT NULL COMMENT '创建时间',
    create_by         VARCHAR(50)  DEFAULT NULL COMMENT '创建者',
    update_time       bigint       DEFAULT NULL COMMENT '更新时间',
    update_by         VARCHAR(50)  DEFAULT NULL COMMENT '更新者',
    deleted           TINYINT UNSIGNED DEFAULT 0 COMMENT '是否删除',
    is_gateway_filter TINYINT UNSIGNED DEFAULT 0 COMMENT '是否网关拦截',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT = '权限资源';