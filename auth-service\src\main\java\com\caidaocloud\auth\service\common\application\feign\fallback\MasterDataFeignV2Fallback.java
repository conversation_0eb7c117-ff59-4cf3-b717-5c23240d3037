package com.caidaocloud.auth.service.common.application.feign.fallback;

import java.util.List;

import com.caidaocloud.auth.service.caidao.application.dto.masterdata.EmpInfoVo;
import com.caidaocloud.auth.service.caidao.application.dto.masterdata.EmpNodeVo;
import com.caidaocloud.auth.service.caidao.application.dto.masterdata.EmpSearchDto;
import com.caidaocloud.auth.service.caidao.application.dto.masterdata.EmpWorkInfoVo;
import com.caidaocloud.auth.service.common.application.feign.MasterDataFeignV2;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.web.Result;

public class MasterDataFeignV2Fallback implements MasterDataFeignV2 {

    /**
     * 获取员工信息
     *
     *
     * @param dto@return
     */
    @Override
    public Result<List<EmpInfoVo>> getEmpInfoByEmpIds(EmpSearchDto dto) {
        return null;
    }

    @Override
    public Result<PageResult<EmpNodeVo>> queryEmpPage(EmpSearchDto dto) {
        return null;
    }

    @Override
    public Result<PageResult<EmpWorkInfoVo>> queryEmpColumnsPage(EmpSearchDto dto) {
        return null;
    }
}
