package com.caidaocloud.auth.service.caidao.facade.vo;

import com.caidaocloud.auth.service.caidao.infrastructure.enums.AdapterDeviceEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/7
 **/
@Data
@ApiModel(value = "角色vo")
public class AuthRoleVo {

    @ApiModelProperty("角色id")
    private Long id;

    @ApiModelProperty("角色名称")
    private String name;

    @ApiModelProperty("角色名称")
    private Map<String,Object> i18nName;

    @ApiModelProperty("角色编码")
    private String code;

    @ApiModelProperty("角色描述")
    private String remark;

    @ApiModelProperty("角色类型; 0：系统角色 1：自定义角色")
    private Integer roleType;

    @ApiModelProperty("适用设备")
    private List<AdapterDeviceEnum> device;

}
