<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.caidaocloud</groupId>
    <artifactId>caidao-auth-service</artifactId>
    <version>1.0.2-SNAPSHOT</version>
    <modules>
        <module>auth-service</module>
        <module>auth-core</module>
    </modules>
    <packaging>pom</packaging>
    <description>caidao cloud auth service</description>
    <parent>
        <groupId>com.caidaocloud</groupId>
        <version>1.0.0-SNAPSHOT</version>
        <artifactId>caidaocloud-parent</artifactId>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <mybatis-plus.version>3.5.1</mybatis-plus.version>
        <totallylazy.version>2.286</totallylazy.version>
        <guava.version>20.0</guava.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>metadata-sdk</artifactId>
            <version>2.0.2-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>