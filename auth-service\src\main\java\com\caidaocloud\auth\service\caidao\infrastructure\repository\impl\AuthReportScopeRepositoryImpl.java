package com.caidaocloud.auth.service.caidao.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleScopeDo;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthRoleScopeRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.AuthReportScopeDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthReportScopePo;
import com.caidaocloud.util.FastjsonUtil;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AuthReportScopeRepositoryImpl implements IAuthRoleScopeRepository {
    @Autowired
    private AuthReportScopeDao authReportScopeDao;

    @Override
    public void flushAll(Long roleId, List<AuthRoleScopeDo> scopeList) {
        authReportScopeDao.delete(new LambdaQueryWrapper<AuthReportScopePo>().eq(AuthReportScopePo::getRoleId, roleId));
        FastjsonUtil.convertList(scopeList,
                AuthReportScopePo.class).forEach(it -> {
            authReportScopeDao.insert(it);
        });
    }

    @Override
    public void deleteByRoleIds(List<Long> roleIdList) {
        authReportScopeDao.delete(new LambdaQueryWrapper<AuthReportScopePo>().in(AuthReportScopePo::getRoleId, roleIdList));
    }

    @Override
    public List<AuthRoleScopeDo> load(Long roleId) {
        val scopes = authReportScopeDao.selectList(new LambdaQueryWrapper<AuthReportScopePo>().eq(AuthReportScopePo::getRoleId, roleId));
        return FastjsonUtil.convertList(scopes, AuthRoleScopeDo.class);
    }

    @Override
    public List<AuthRoleScopeDo> list(List<Long> roleIds) {
        val scopes = authReportScopeDao.selectList(new LambdaQueryWrapper<AuthReportScopePo>()
                .in(AuthReportScopePo::getRoleId, roleIds));
        return FastjsonUtil.convertList(scopes, AuthRoleScopeDo.class);
    }
}