package com.caidaocloud.auth.service.caidao.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 通过开放平台开通的租户实体 Po
 * <AUTHOR>
 * @date 2021-11-17
 */
@Data
@TableName("oauth_open_tenant")
@Accessors(chain = true)
public class OAuthOpenTenantPo {
    @TableId(type = IdType.AUTO)
    private String id;

    private String clientId;

    private String corpKey;

    private Long belongId;

    private Long createTime;
}
