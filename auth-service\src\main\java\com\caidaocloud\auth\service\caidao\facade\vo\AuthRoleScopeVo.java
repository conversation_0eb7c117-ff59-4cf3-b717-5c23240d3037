package com.caidaocloud.auth.service.caidao.facade.vo;

import com.caidaocloud.auth.service.caidao.infrastructure.enums.AuthRoleScopeComparator;
import com.caidaocloud.auth.core.enums.AuthRoleScopeRestriction;
import com.caidaocloud.auth.core.enums.AuthRoleScopeTargetType;
import lombok.Data;

@Data
public class AuthRoleScopeVo {

    private Long roleId;

    private AuthRoleScopeTargetType targetType = AuthRoleScopeTargetType.STANDARD;

    private String targets;

    private String filterProperty;

    private AuthRoleScopeComparator comparator = AuthRoleScopeComparator.IN;

    private AuthRoleScopeRestriction restriction;

    private String values;

    private String simpleValues;

    private int index;
}
