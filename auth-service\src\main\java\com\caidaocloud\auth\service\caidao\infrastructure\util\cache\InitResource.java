package com.caidaocloud.auth.service.caidao.infrastructure.util.cache;

import com.caidaocloud.auth.service.caidao.domain.entity.OAuthClientDo;
import com.caidaocloud.auth.service.caidao.domain.repository.OAuthClientRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.impl.AuthResourceRepositoryImpl;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Sets;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.util.HashSet;

import java.util.List;

import java.util.Set;
import java.util.stream.Collectors;

import static com.caidaocloud.auth.service.caidao.application.service.OAuthClientDetailService.OAUTH2_CLIENT_KEY;

/**
 * 初始化resource到缓存
 *
 * <AUTHOR>
 * @date 2022/5/17
 **/
@Component
@Slf4j
public class InitResource implements ApplicationRunner {

    public static Set<String> platformAdminUrl = Sets.newHashSet(
            "/api/maintenance/v1/module/sql",
            "/api/maintenance/v1/module/entity",
            "/api/maintenance/v1/tenant",
            "/api/maintenance/v1/script/sql/script",
            "/api/maintenance/v1/script/entity/script",
            "/api/masterData/emp/v2/detail",
            "/api/maintenance/v1/tenant/scrip",
            "/api/maintenance/v1/tenant/user",
            "/api/maintenance/v1/tenant/dict"
    );

    public static Set<String> adminUrl = Sets.newHashSet(
            "/api/auth/v1/subject/role/query-page",
            "/api/auth/v1/subject/role",
            "/api/auth/v1/subject/role/all",
            "/api/auth/v1/subject/resource/scope/detail",
            "/api/auth/v1/subject/resource/url/all",
            "/api/auth/v1/subject/authorize",
            "/api/auth/v1/subject/importData",
            "/api/auth/v1/subject/getImportPercentage",
            "/api/auth/v1/subject/downloadErrorImportInfo",
            "/api/auth/v1/resource/list",
            "/api/auth/v1/resource",
            "/api/auth/v1/resource/saas",
            "/api/auth/v1/role",
            "/api/auth/v1/role/copy",
            "/api/auth/v1/role/query-page",
            "/api/auth/v1/tenant/init",
            "/api/user/base/v2/page",
            "/api/user/base/v2",
            "/api/msg/emailconfig/v1/detail",
            "/api/msg/emailconfig/v1/sendTestEmail",
            "/api/msg/emailconfig/v1/save",
            "/api/msg/config/v1/list",
            "/api/msg/config/v1/condition",
            "/api/msg/config/v1/noticeTypeSelectList",
            "/api/msg/config/v1/noticeTargetSelectList",
            "/api/msg/template/v1/all",
            "/api/msg/config/v1/copyTargetSelectList",
            "/api/msg/config/v1/save",
            "/api/msg/config/v1/detail",
            "/api/msg/config/v1/condition",
            "/api/msg/config/v1/noticeTypeSelectList",
            "/api/msg/config/v1/noticeTargetSelectList",
            "/api/msg/template/v1/all",
            "/api/msg/config/v1/copyTargetSelectList",
            "/api/msg/config/v1/detail",
            "/api/msg/config/v1/condition",
            "/api/msg/config/v1/noticeTypeSelectList",
            "/api/msg/config/v1/noticeTargetSelectList",
            "/api/msg/template/v1/all",
            "/api/msg/config/v1/copyTargetSelectList",
            "/api/msg/config/v1/update",
            "/api/msg/config/v1/copy",
            "/api/msg/config/v1/enable",
            "/api/msg/config/v1/disable",
            "/api/msg/config/v1/delete",
            "/api/msg/template/v1/list",
            "/api/msg/placeholder/v1/selectList",
            "/api/msg/template/v1/save",
            "/api/msg/placeholder/v1/selectList",
            "/api/msg/template/v1/update",
            "/api/msg/template/v1/remove",
            "/api/msg/template/v1/list",
            "/api/msg/placeholder/v1/selectList",
            "/api/msg/template/v1/save",
            "/api/msg/placeholder/v1/selectList",
            "/api/msg/template/v1/update",
            "/api/msg/template/v1/remove",
            "/api/msg/template/v1/list",
            "/api/msg/placeholder/v1/selectList",
            "/api/msg/template/v1/save",
            "/api/msg/placeholder/v1/selectList",
            "/api/msg/template/v1/update",
            "/api/msg/template/v1/remove",
            "/api/msg/template/v1/list",
            "/api/msg/placeholder/v1/selectList",
            "/api/msg/template/v1/save",
            "/api/msg/placeholder/v1/selectList",
            "/api/msg/template/v1/update",
            "/api/msg/template/v1/remove",
            "/api/hr/v1/termination/config",
            "/api/hr/v1/termination/config/update",
            "/api/hr/v1/termination/config/enable",
            "/api/hr/v1/termination/config/list",
            "/api/maintenance/v1/module/manage",
            "/api/maintenance/v1/module/delete",
            "/api/maintenance/v1/common/config",
            "/api/maintenance/v1/common/config/post"

    );

    public static Set<String> configUrl = Sets.newHashSet(
            "/api/user/base/v2/detail",
            "/api/user/base/v2/detail",
            "/api/hrpaas/v1/web/template/filterCode",
            "/api/hrpaas/v1/file/download",
            "/api/auth/v1/role/query-page",
            "/api/hr/emp/work/v1/detail",
            "/api/hr/emp/privateinfo/v1/detail"
    );

    @Autowired
    private CacheService cacheService;

    private final String NEED_GATEWAY_CHECK = "needGatewayCheck";

    private final String ADMIN_URL = "ADMIN_ONLY_URL";

    @Resource
    private AuthResourceRepositoryImpl authResourceRepository;

    @Resource
    private OAuthClientRepository oAuthClientRepository;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 初始化资源到缓存
     */
    public void initResourceToCache() {
        Set<String> urlSet;
        try {
            urlSet = Sequences.sequence(authResourceRepository.selectUrlOfFilterGateway())
                    .filter(e -> StringUtils.isNotBlank(e)).stream().collect(Collectors.toSet());
        }
        catch (Exception e) {
            urlSet = new HashSet<>();
            log.error("Failed to get url from db,{}", e.getMessage(), e);
        }

        urlSet.addAll(adminUrl);
        urlSet.addAll(platformAdminUrl);
        log.info("init urlSet={}", FastjsonUtil.toJson(urlSet));
        if (!CollectionUtils.isEmpty(urlSet)) {
            cacheService.cacheSet(NEED_GATEWAY_CHECK, urlSet);
        }
    }

    private void initAdminResource(){
        log.info("-----load cache before, data={}", cacheService.getSet(ADMIN_URL));
        cacheService.cacheSet(ADMIN_URL, adminUrl);
        log.info("-----load cache after, data={}", cacheService.getSet(ADMIN_URL));
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("init resource to cache...");
        initResourceToCache();
        log.info("init admin resource to cache...");
        initAdminResource();
        log.info("init OAuth resource to cache...");
        initOAuthClient();
    }

    private void initOAuthClient() {
        try {
            List<OAuthClientDo> clientList = oAuthClientRepository.selectListAll();
            clientList.stream().forEach(oauthClient -> {
                String oauthClientKey = String.format(OAUTH2_CLIENT_KEY, oauthClient.getClientId());
                cacheService.cacheValue(oauthClientKey, FastjsonUtil.toJson(oauthClient));
            });
        }
        catch (Exception e) {
            log.error("init oauth client failed", e);
        }

    }

}
