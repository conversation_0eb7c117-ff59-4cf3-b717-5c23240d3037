package com.caidaocloud.auth.service.caidao.infrastructure.config.converter;

import com.caidaocloud.auth.service.caidao.application.handler.IHandlerType;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.security.dto.SimpleSession;
import com.caidaocloud.security.dto.TokenDto;
import com.caidaocloud.security.session.SessionUtil;
import com.caidaocloud.security.token.TokenGenerator;
import com.caidaocloud.security.token.TokenVerify;
import com.caidaocloud.util.SpringUtil;
import com.google.common.base.Splitter;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 才到自己的 jwt token 处理流程
 * 需要存储数据到 sessionService
 *
 * <AUTHOR>
 * @date 2021-11-16
 */
@Slf4j
@Component
public class AuthJwtAccessTokenConverter extends JwtAccessTokenConverter {
    @Resource
    private CacheService cacheService;

    /**
     * accessToken 编码
     *
     * @return
     */
    @Override
    protected String encode(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
        Map<String, Object> userInfoMap = null;
        // accessToken 身份信息为空，转换 accessToken 失败
        PreCheck.preCheckArgument(null == accessToken || null == (userInfoMap = accessToken.getAdditionalInformation()),
                "accessToken is empty, convert access token err");

        // 处理 handlerType 逻辑
        doHandlerType(authentication, userInfoMap);

        Object userid = userInfoMap.get("userid"), belongId = userInfoMap.get("belongId"), corpId = userInfoMap.get("corpId");
        // accessToken 中缺少身份信息
        PreCheck.preCheckArgument(null == userid || null == belongId, "Identity information is missing in accesstoken");

        String tenantId = belongId.toString(), userId = userid.toString();

        UserInfo userInfo = new UserInfo();
        userInfo.setEmpid(0);
        userInfo.setUserid(Integer.parseInt(userId));
        userInfo.setTenantId(belongId.toString());
        userInfo.setStaffId(0L);
        userInfo.setCorpid(corpId == null ? 0 : Integer.parseInt(corpId.toString()));
        userInfo.setBelongOrgId(Integer.parseInt(tenantId));
        sessionRefresh(userInfo, accessToken.getAdditionalInformation());

        String token = TokenGenerator.getToken(userId, tenantId, 2);
        return token;
    }

    @Override
    protected Map<String, Object> decode(String token) {
        TokenDto tokenDataModel = TokenVerify.getTokenDataModel(token);
        final Map<String, Object> additionalInfo = new HashMap<>(10);
        additionalInfo.put("userid", tokenDataModel.getUserId());
        additionalInfo.put("empid", 0);
        additionalInfo.put("tenantId", tokenDataModel.getTenantId());
        additionalInfo.put("belongId", tokenDataModel.getTenantId());

        return additionalInfo;
    }

    private synchronized void sessionRefresh(UserInfo userInfo, Map<String, Object> additionalInformation) {
        String key = String.format("t-user-session-%s:%s", userInfo.getTenantId(), userInfo.getUserid());
        SimpleSession simpleSession = new SimpleSession();
        simpleSession.setAttribute(SessionUtil.AUTH_USER_INFO, userInfo);
        List<String> authList = null;
        String authUrl = null;
        if (Objects.nonNull(additionalInformation) &&
                StringUtils.isNotBlank(authUrl = additionalInformation.getOrDefault("authUrl", "").toString())) {
            authList = Splitter.on(",").splitToList(authUrl);
        } else {
            authList = Lists.list();
        }
        simpleSession.setAttribute(SessionUtil.AUTH_SECURITY_INFO, authList);
        cacheService.cacheObject(key, simpleSession, 3600L);
    }

    private void doHandlerType(OAuth2Authentication authentication, Map<String, Object> targetMap) {
        if (null == authentication) {
            log.warn("OAuth2Authentication is empty, doHandlerType exec end.");
            return;
        }

        OAuth2Request request = authentication.getOAuth2Request();
        if (null == request) {
            log.warn("OAuth2Request is empty, doHandlerType exec end.");
            return;
        }

        Map<String, String> params = request.getRequestParameters();
        if (null == params || params.isEmpty()) {
            return;
        }

        String handlerType = params.get("handlerType");
        IHandlerType authHandlerType = SpringUtil.getBean(handlerType);
        if (null == authHandlerType) {
            return;
        }

        authHandlerType.executeHandlerType(params, targetMap);
    }
}
