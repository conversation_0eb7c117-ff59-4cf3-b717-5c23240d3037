package com.caidaocloud.auth.service.caidao.domain.service;

import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleGroupDo;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthGroupRuleRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthGroupRulePo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/10
 **/
@Slf4j
@Service
public class AuthGroupRuleDomainService {

    @Resource
    private IAuthGroupRuleRepository authGroupRuleRepository;

    public void saveOrUpdateRoleGroup(AuthRoleGroupDo authRoleGroupDo) {
        authGroupRuleRepository.saveOrUpdateRoleGroup(authRoleGroupDo);
    }


    public AuthGroupRulePo getGroupRuleById(Long id) {
        return authGroupRuleRepository.getGroupRuleById(id);
    }



    public int deleteGroupRule(List<Long> idList) {

        return authGroupRuleRepository.deleteGroupRule(idList);
    }

    public PageResult<AuthGroupRulePo> pageList(QueryPageBean queryPageBean) {
        AuthRoleGroupDo authRoleGroupDo = new AuthRoleGroupDo();
        return authRoleGroupDo.getPage(queryPageBean);

    }

    public List<AuthGroupRulePo> getGroupRuleByIds(List<Long> ids) {

        return authGroupRuleRepository.getGroupRuleByIds(ids);
    }
}
