package com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.UserBaseInfoPo;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface UserBaseInfoDao extends BaseMapper<UserBaseInfoPo> {


    @Select("<script>" +
            "SELECT user_id, emp_id, account, status,tenant_id FROM user_base_info WHERE status = 1 " +
            "<if test='accounts != null and accounts.size() > 0'> " +
            " and account IN" +
            " <foreach item='item' index='index' collection='accounts' open='(' separator=',' close=')'>" +
            "  #{item} " +
            " </foreach>" +
            "</if>" +
            "</script>")
    List<UserBaseInfoPo> selectUserBaseInfoByAccounts(List<String> accounts);
}
