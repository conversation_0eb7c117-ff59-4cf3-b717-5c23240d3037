package com.caidaocloud.auth.service.caidao.infrastructure.repository.po;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleDo;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.AdapterDeviceEnum;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum;
import com.caidaocloud.util.FastjsonUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 角色和权限映射po
 *
 * <AUTHOR>
 * @date 2022/5/21
 **/
@Data
public class AuthRoleAndPermissionPo implements Serializable {

    private Long id;

    private String name;

    private String i18nName;;

    private String code;

    @TableField(value = "role_id")
    private Long roleId;

    @TableField(value = "role_type")
    private Integer roleType;

    @TableField(value = "is_enabled")
    private Boolean isEnabled;

    private String remark;

    @JSONField(serialize = false)
    private String device;

    @TableField(value = "permisson_id")
    private Long permissonId;

    @TableField(value = "resource_code")
    private String resourceCode;

    @TableField(value = "parent_code")
    private String parentCode;

    private ResourceCategoryEnum category;

    @TableField(value = "data_scope_detail")
    private String dataScopeDetail;

    public AuthRoleDo toEntity() {
        AuthRoleDo authRoleDo = FastjsonUtil.convertObject(this, AuthRoleDo.class);
        if (StringUtils.isNotBlank(this.device)) {
            authRoleDo.setDevice(FastjsonUtil.toList(this.device, AdapterDeviceEnum.class));
        }
        return authRoleDo;
    }

}
