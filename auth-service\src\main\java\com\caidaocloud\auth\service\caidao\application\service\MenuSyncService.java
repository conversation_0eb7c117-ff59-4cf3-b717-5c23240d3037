package com.caidaocloud.auth.service.caidao.application.service;

import com.caidaocloud.auth.service.caidao.application.dto.AuthResourceDto;
import com.caidaocloud.auth.service.caidao.application.dto.bi.MenuDto;
import com.caidaocloud.auth.service.caidao.application.factory.AuthResourceFactory;
import com.caidaocloud.auth.service.caidao.application.feign.BiFegin;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthResourceDo;
import com.caidaocloud.auth.service.caidao.infrastructure.constant.CodeConstant;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜单同步
 *
 * <AUTHOR>
 * @date 2023/7/19
 **/
@Slf4j
@Service
public class MenuSyncService {
    @Resource
    private BiFegin biFegin;
    @Autowired
    private AuthResourceService authResourceService;

    /**
     * 同步bi菜单
     */
    public void syncBiMenu() {
        var userInfo = SecurityUserUtil.getSecurityUserInfo();
        if (userInfo == null || StringUtils.isBlank(userInfo.getTenantId())) {
            throw ServerException.globalException(ErrorMessage.fromCode("USER_NO_LOG_IN"));
        }
        Result<List<MenuDto>> result = null;
        try {
            result = biFegin.allMenus(userInfo.getTenantId());
            if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
                log.info("return syncBiMenu method, tenantId={} result={}", userInfo.getTenantId(), FastjsonUtil.toJson(result.getData()));
                return;
            }
        } catch (Exception e) {
            log.error(String.format("bi service occur error, tenantId=%s", userInfo.getTenantId()), e);
            throw new ServerException("bi service occur error");
        }
        var menuList = result.getData().stream().filter(e -> "APP".equals(e.getType())).collect(Collectors.toList());
        menuList = menuList == null ? Lists.newArrayList() : menuList;
        if (CollectionUtils.isEmpty(menuList)) {
            log.info("menus of bi were empty, tenantId={}", userInfo.getTenantId());
            return;
        }
        var resrouceCodeList = menuList.stream()
                .map(e -> String.format(CodeConstant.RESOURCE_PREFIX, e.getPublishedBy())).collect(Collectors.toList());
        var resourceCodeSequence = authResourceService.loadResources(resrouceCodeList);
        if (resourceCodeSequence.isEmpty()) {
            var resourceDtoList = menuList.stream()
                    .map(e -> AuthResourceFactory.build(e, userInfo.getTenantId()))
                    .collect(Collectors.toList());
            authResourceService.createList(resourceDtoList, false);
            return;
        }
        var existCodeMap = resourceCodeSequence.stream().collect(Collectors.toMap(e -> e.getCode(), e -> e));
        List<AuthResourceDto> addList = Lists.newArrayList();
        List<AuthResourceDto> updateList = Lists.newArrayList();
        for (MenuDto menuDto : menuList) {
            var authResourceDto = AuthResourceFactory.build(menuDto, userInfo.getTenantId());
            if (existCodeMap.containsKey(authResourceDto.getCode())) {
                updateList.add(authResourceDto);
                existCodeMap.remove(authResourceDto.getCode());
            } else {
                addList.add(authResourceDto);
            }
        }
        if (!CollectionUtils.isEmpty(addList)) {
            authResourceService.createList(addList, false);
        }
        if (!CollectionUtils.isEmpty(updateList)) {
            var authResourceDos = FastjsonUtil.convertList(updateList, AuthResourceDo.class);
            for (AuthResourceDo authResourceDo : authResourceDos) {
                authResourceDo.updateByCode(false);
            }
        }
        if (!existCodeMap.isEmpty()) {
            existCodeMap.entrySet().stream().map(e -> e.getValue().getCode()).filter(e -> StringUtils.isNotBlank(e))
                    .forEach(e -> authResourceService.remove(e));
        }
    }
}
