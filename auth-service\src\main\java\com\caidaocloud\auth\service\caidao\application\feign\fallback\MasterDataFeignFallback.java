package com.caidaocloud.auth.service.caidao.application.feign.fallback;

import com.caidaocloud.auth.service.caidao.application.dto.masterdata.SysEmpInfoDto;
import com.caidaocloud.auth.service.caidao.application.feign.MasterDataFeign;
import com.caidaocloud.web.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/12
 **/
public class MasterDataFeignFallback implements MasterDataFeign {

    @Override
    public Result<List<SysEmpInfoDto>> getEmpInfoByEmpIds(String empIds) {
        return Result.fail("getEmpInfoByEmpIds fail");
    }

}
