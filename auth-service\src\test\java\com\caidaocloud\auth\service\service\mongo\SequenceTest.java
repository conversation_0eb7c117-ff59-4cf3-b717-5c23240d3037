package com.caidaocloud.auth.service.service.mongo;

import com.caidaocloud.auth.service.caidao.domain.entity.AuthRolePermissionDo;
import com.caidaocloud.auth.service.caidao.facade.vo.AuthRolePermissionVo;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;

import java.util.ArrayList;

public class SequenceTest {

    public static void main(String[] args) {
        ArrayList<Integer> integers = Lists.newArrayList(1, 2, 3);
        String join = Joiner.on("-").join(integers);
        System.out.println(join);
/*        String str = "[\"ALL\",\"PC\",\"H5\",\"APP\"]";
        List<AdapterDeviceEnum> adapterDeviceEnums = FastjsonUtil.toList(str, AdapterDeviceEnum.class);
        System.out.println(adapterDeviceEnums);

        AuthRoleAndPermissionPo en = new AuthRoleAndPermissionPo();
        en.setId(1l);
        en.setName("超级管理员");
        en.setRoleType(0);
        AuthRoleDo authRoleDo = FastjsonUtil.convertObject(en, AuthRoleDo.class);
        System.out.println(authRoleDo);*/

        //AuthRolePermissionVo
        AuthRolePermissionDo authRolePermissionDo = new AuthRolePermissionDo();
        authRolePermissionDo.setResourceCode("aaa");
        AuthRolePermissionVo authRolePermissionVo = FastjsonUtil.convertObject(authRolePermissionDo, AuthRolePermissionVo.class);
        System.out.println(authRolePermissionVo);

        AuthRolePermissionVo vo = new AuthRolePermissionVo();
        vo.setCode("bbb");
        AuthRolePermissionDo authRolePermissionDo1 = FastjsonUtil.convertObject(vo, AuthRolePermissionDo.class);
        System.out.println(authRolePermissionDo1);
    }

}
