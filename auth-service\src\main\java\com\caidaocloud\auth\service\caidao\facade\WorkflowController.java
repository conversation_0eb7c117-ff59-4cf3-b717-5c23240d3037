package com.caidaocloud.auth.service.caidao.facade;

import com.caidaocloud.auth.service.caidao.application.dto.subject.AuthSubjectPageByRolesDto;
import com.caidaocloud.auth.service.caidao.application.service.AuthGroupRuleSubjectService;
import com.caidaocloud.auth.service.caidao.facade.vo.workflow.WfApproverVo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/api/auth/v1/workflow")
@Slf4j
public class WorkflowController {
    @Autowired
    private AuthGroupRuleSubjectService authGroupRuleSubjectService;

    @ApiOperation(value = "角色查看用户分页")
    @GetMapping("/role/subjects")
    @ResponseBody
    public Result<String> querySubjectsOfRole(@RequestParam("applicantId") String applicantId,
                                              @RequestParam("initiatorId") String initiatorId,
                                              @RequestParam("code") String code,
                                              @RequestParam("value") String value) {
        if (StringUtils.isBlank(value)) {
            throw new ServerException("value is empty");
        }
        var roleIdList = Arrays.stream(value.split(","))
                .map(e -> Long.parseLong(e)).collect(Collectors.toList());
        var subjectList = authGroupRuleSubjectService.getSubjectsOfRole(roleIdList);
        if (!CollectionUtils.isEmpty(subjectList)) {
            var approverList = subjectList.stream().map(e -> {
                return new WfApproverVo(String.valueOf(e.getEmpId()), e.getUserName());
            }).collect(Collectors.toList());
            return Result.ok(FastjsonUtil.toJson(approverList));
        }
        return Result.ok("");
    }

    @ApiOperation(value = "多个角色查看用户分页")
    @GetMapping("/roles/subjects")
    @ResponseBody
    public Result<List<WfApproverVo>> querySubjectsOfRole(@RequestBody AuthSubjectPageByRolesDto dto) {
        List<Long> roleIds = dto.getRoleIds();
        if (CollectionUtils.isEmpty(roleIds)) {
            throw new ServerException("value is empty");
        }
        var subjectList = authGroupRuleSubjectService.getSubjectsOfRole(roleIds);
        if (!CollectionUtils.isEmpty(subjectList)) {
            var approverList = subjectList.stream().map(e -> new WfApproverVo(String.valueOf(e.getEmpId()), e.getUserName())).distinct().collect(Collectors.toList());
            return Result.ok(approverList);
        }
        return Result.ok(Lists.list());
    }
}