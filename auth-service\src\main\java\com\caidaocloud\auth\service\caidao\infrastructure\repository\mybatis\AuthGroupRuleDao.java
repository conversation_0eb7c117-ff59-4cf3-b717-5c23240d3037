package com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthGroupRulePo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/9
 **/
public interface AuthGroupRuleDao extends BaseMapper<AuthGroupRulePo> {

    /**
     * 获取角色默认角色组的ruleid
     *
     * @param roleIds
     * @return
     */
    @Select("<script>" +
            " SELECT t1.id FROM auth_group_rule t1 INNER JOIN auth_role_group_rule t2 ON t1.id = t2.group_rule_id " +
            " WHERE t1.deleted = 0 " +
            " <if test='roleIds != null and roleIds.size() > 0'>" +
            "   AND t2.role_id in " +
            "   <foreach item='item' index='index' collection='roleIds' open='(' separator=',' close=')'>" +
            "       #{item} " +
            "   </foreach>" +
            " </if>" +
            "</script>")
    List<Long> getRoleDefaultGroupRuleId(@Param("roleIds") List<Long> roleIds);

}