package com.caidaocloud.auth.service.caidao.domain.service;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthGroupRuleSubjectImportDo;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@Service
public class AuthGroupRuleSubjectImportDomainService {

    public List<AuthGroupRuleSubjectImportDo> getImportDoFromExcel(InputStream inputStream) {
        if (null == inputStream) {
            return new ArrayList<AuthGroupRuleSubjectImportDo>();
        }
        ImportParams params = new ImportParams();
        params.setTitleRows(0);
        params.setHeadRows(1);
        List<AuthGroupRuleSubjectImportDo> list = null;
        try {
            list = ExcelImportUtil.importExcel(inputStream, AuthGroupRuleSubjectImportDo.class, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }
}
