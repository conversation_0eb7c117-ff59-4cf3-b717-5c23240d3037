package com.caidaocloud.auth.core.util;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.util.WebUtil;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 多语言工具类
 */
public final class LangUtil {
    /**
     * 获取多语言提示
     */
    public static String getMsg(int msgCode){
        return MessageHandler.getExceptionMessage(msgCode, WebUtil.getRequest());
    }

    /**
     * 带占位符的多语言
     */
    public static String getFormatMsg(int msgCode, Object... args){
        String format = getMsg(msgCode);
        return String.format(format, args);
    }

    public static String getDefaultValue(Map<String, Object> i18nMap) {
        if (i18nMap == null || i18nMap.isEmpty()) {
            return "";
        }
        return Objects.toString(i18nMap.getOrDefault("default", ""));
    }

    /**
     * @param defaultValue
     * @param i18nMap
     * @return
     */
    public static String getI18nValue(String defaultValue, Map<String, Object> i18nMap) {
        if (i18nMap == null || i18nMap.isEmpty()) {
            return FastjsonUtil.toJson(Maps.immutableEntry("default", Objects.toString(defaultValue, "")));
        } else {
            return FastjsonUtil.toJson(i18nMap);
        }
    }

    public static String getLocalLanguage() {
        Locale locale = getLocale();
        return locale == null ? "" : getLocale().getLanguage();
    }

    public static Locale getLocale() {
        HttpServletRequest request = WebUtil.getRequest();
        Locale locale = null;
        if (request != null) {
            String acceptLanguage = request.getHeader("Accept-Language");
            locale = Optional.ofNullable(acceptLanguage).map(o1 -> {
                if (o1.contains(",") || o1.contains(";")) {
                    List<Locale.LanguageRange> list = Locale.LanguageRange.parse(acceptLanguage);
                    Optional<Locale.LanguageRange> range = list.stream()
                            .filter(r -> !r.getRange().startsWith("*"))
                            .max(Comparator.comparingDouble(Locale.LanguageRange::getWeight));

                    return range.map(r -> {
                        String rangeValue = r.getRange();
                        String[] parts = rangeValue.split("-");
                        String language = parts[0];
                        String country = parts.length > 1 ? parts[1] : "";
                        return new Locale(language, country);
                    }).orElse(Locale.getDefault());
                } else {
                    return Locale.forLanguageTag(acceptLanguage);
                }
            }).orElse(Locale.CHINESE);
        }
        return locale;
    }

    public static boolean chineseLocale() {
        Locale locale = getLocale();
        return locale != null && (StringUtil.isNotEmpty(locale.getLanguage()) && locale.getLanguage().startsWith("zh"));
    }

    public static String parseI18nValue(String defaultValue, String i18nValue) {
        Locale locale = getLocale();
        Map<String, Object> i18nMap = FastjsonUtil.toObject(i18nValue, new TypeReference<Map<String, Object>>(){});
        if (locale != null && i18nMap != null) {
            final String languageKey = StringUtils.isEmpty(locale.getCountry()) ?
                    locale.getLanguage() : String.format("%s-%s", locale.getLanguage(), locale.getCountry());
            String i18nDefault = Objects.toString(i18nMap.getOrDefault("default", ""));
            return i18nMap.containsKey(languageKey) ? Objects.toString(i18nMap.get(languageKey)) : i18nDefault;
        }
        return defaultValue;
    }

    public static String getLangMsg(String key) {
        Locale locale = getLocale();
        return MessageHandler.getMessage(key, locale);
    }

    public static String getCurrentLangVal(Map<String, String> i18nMap) {
        return getCurrentLangVal(i18nMap, getLocale());
    }

    public static String getCurrentLangVal(Map<String, String> i18nMap, Locale locale) {
        String currentLang = locale.getLanguage();
        String langDetail = currentLang;
        String country = locale.getCountry();
        if (StringUtils.isNotEmpty(country)) {
            langDetail = String.format("%s-%s", currentLang, country);
        }

        if (i18nMap.containsKey(currentLang)) {
            return i18nMap.get(currentLang);
        } else {
            return i18nMap.containsKey(langDetail) ? i18nMap.get(langDetail) : i18nMap.get("default");
        }
    }

}