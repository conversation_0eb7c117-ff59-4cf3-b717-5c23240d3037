package com.caidaocloud.auth.service;

import com.caidaocloud.auth.service.external.domain.service.AppSecurityDomainService;
import com.caidaocloud.auth.service.external.infrastructure.enums.ExternalAuthEnum;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Maps;
import lombok.var;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.*;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@SpringBootTest(classes = AuthApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class SsoTest {
    @Autowired
    private RestTemplate restTemplate;
    @Value("${caidaocloud.ats.domain:}")
    private String atsDomain;
    @Value("${caidaocloud.ats.apiDomain:}")
    private String atsApiDomain;
    @Value("${caidaocloud.ats.redirect:/index}")
    private String redirectUri;
    @Value("${caidaocloud.ats.tokenUri:/prod-api/openapi/accessTokenOnApp}")
    private String toeknUri;
    @Autowired
    private AppSecurityDomainService appSecurityDomainService;

    @Before
    public void before() {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        userInfo.setUserId(0L);
        userInfo.setEmpId(0L);
        userInfo.setIsAdmin(false);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }

    @Test
    public void test1() {
        var appSecurity = appSecurityDomainService.getAppSecurity("11", ExternalAuthEnum.ATS);
        var url = new StringBuilder();
        var domain = this.atsApiDomain;
        url.append(domain);
        if (!toeknUri.startsWith("/")) {
            url.append("/");
        }
        url.append(toeknUri);
        var headers = new HttpHeaders();
        headers.set("Accept", "application/json");
        HashMap<String, String> body = Maps.newHashMap();
        body.put("account", "<EMAIL>");
        body.put("appKey", appSecurity.getAppKey());
        body.put("appSecret", appSecurity.getAppSecret());
        HttpEntity httpEntity = new HttpEntity(body, headers);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url.toString(), HttpMethod.POST, httpEntity, String.class);
        if (responseEntity.getStatusCode() == HttpStatus.OK) {
            Map<String, Object> map = FastjsonUtil.toObject(responseEntity.getBody(), Map.class);
            if (map.containsKey("code") && Integer.valueOf(map.get("code").toString()).intValue() == 200) {
                String data = String.valueOf(map.get("data"));
                System.out.println(data);
                Map<String, Object> result = FastjsonUtil.toObject(data, Map.class);
                if (result.containsKey("tokenKey")) {
                    String tokenKey = result.get("tokenKey").toString();
                    System.out.println(tokenKey);
                }
            }
        }
    }
}
