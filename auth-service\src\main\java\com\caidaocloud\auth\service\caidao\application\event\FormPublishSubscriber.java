package com.caidaocloud.auth.service.caidao.application.event;

import javax.annotation.Resource;

import com.caidaocloud.auth.core.dto.AuthRoleScopeTargetDetail;
import com.caidaocloud.auth.core.dto.AuthRoleScopeTargetRegisterDto;
import com.caidaocloud.auth.core.enums.AuthRoleScopeRestriction;
import com.caidaocloud.auth.core.enums.AuthRoleScopeTargetType;
import com.caidaocloud.auth.service.caidao.application.factory.FormAuthScopeFactory;
import com.caidaocloud.auth.service.caidao.application.service.AuthRoleScopeTargetRegisterService;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleScopeTargetDo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.paas.common.dto.FormDefDto;
import com.caidaocloud.hrpaas.paas.common.event.FormPublishEvent;
import com.caidaocloud.hrpaas.paas.common.feign.FormFeignClient;
import com.caidaocloud.msg.handler.MessageHandler;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import static com.caidaocloud.auth.service.caidao.infrastructure.constant.CodeConstant.FORM_AUTH_SCOPE_PREFIX;

@Component
@Slf4j
public class FormPublishSubscriber implements MessageHandler<FormPublishEvent> {

	@Resource
	private FormFeignClient formFeignClient;
	@Resource
	private AuthRoleScopeTargetRegisterService authRoleScopeTargetRegisterService;

	@Override
	public String topic() {
		return FormPublishEvent.topic;
	}

	@Override
	public void handle(FormPublishEvent message) throws Exception {
		try {
			String tenantId = message.getTenantId();
			SecurityUserInfo userInfo = new SecurityUserInfo();
			userInfo.setTenantId(tenantId);
			userInfo.setUserId(0L);
			userInfo.setEmpId(0L);
			SecurityUserUtil.setSecurityUserInfo(userInfo);

			FormDefDto defDto = loadFomrDef(message.getFormId());
			if (defDto == null) {
				log.warn("form def not found, formId:{}", message.getFormId());
				throw new ServerException("form def not found");
			}
			if ("EMP".equals(defDto.getTarget())) {
				log.info("绑定员工表单，开始注册数据权限, formId:{}", message.getFormId());
				registerAuthScope(defDto);
				log.info("注册数据权限成功, formId:{}", message.getFormId());
			}
		}finally {
			SecurityUserUtil.removeSecurityUserInfo();
		}
	}

	private void registerAuthScope(FormDefDto defDto) {
		AuthRoleScopeTargetRegisterDto dto = FormAuthScopeFactory.create(defDto.getName(), defDto.getId());
		authRoleScopeTargetRegisterService.register(dto);
	}

	private FormDefDto loadFomrDef(String formId) {
		return formFeignClient.getFormDefById(formId).getData();
	}

}