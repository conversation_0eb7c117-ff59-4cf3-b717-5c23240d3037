package com.caidaocloud.auth.service.service.mongo;

import com.caidaocloud.auth.service.caidao.infrastructure.entity.ConditionTree;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AuthRoleTest {
    public static void main(String[] args) {
        String empJson = "[{\"empType.dict.value\":\"1488308438114575\",\"company\":\"1612032205332348\",\"organize\":\"1578059113363469\",\"workno\":\"CIlufangge\",\"empId\":\"1609609199949828\"},{\"empType.dict.value\":\"1488308438114575\",\"organize\":\"1607955892467717\",\"workno\":\"林一3\",\"empId\":\"1607952595007489\"},{\"empType.dict.value\":\"1488308438114550\",\"post\":\"1596473839262430\",\"organize\":\"1578059113363469\",\"workno\":\"CI32666\",\"empId\":\"1607242330724358\"},{\"empType.dict.value\":\"1488308438114551\",\"post\":\"1574691940571202\",\"organize\":\"1572549923108870\",\"workno\":\"CI100046\",\"empId\":\"1581708331866116\"},{\"empType.dict.value\":\"1488308438114550\",\"post\":\"1573938314934307\",\"company\":\"1572546151577605\",\"organize\":\"1572587500845099\",\"workno\":\"CI100042\",\"empId\":\"1578255642400770\"},{\"empType.dict.value\":\"1488308438114550\",\"post\":\"1585443387169478\",\"company\":\"1717583420184596\",\"organize\":\"1579484530898980\",\"workno\":\"CI31641\",\"empId\":\"1602923036473345\"},{\"empType.dict.value\":\"1488308438114550\",\"organize\":\"1574610083977224\",\"workno\":\"CI400005\",\"empId\":\"1601487795943428\"},{\"empType.dict.value\":\"1488308438114575\",\"post\":\"1586677279268300\",\"organize\":\"1586637364836367\",\"workno\":\"CI400004\",\"empId\":\"1599420097189889\"},{\"empType.dict.value\":\"1488308438114550\",\"organize\":\"1572559090169887\",\"workno\":\"CI400003\",\"empId\":\"1598026582366213\"},{\"empType.dict.value\":\"1488308438114550\",\"post\":\"1586678033636814\",\"company\":\"1573799831525410\",\"organize\":\"1578059113363469\",\"workno\":\"CI100041\",\"empId\":\"1578060090800129\"},{\"post\":\"1586677279268300\",\"organize\":\"1586637364836367\",\"workno\":\"CI200070\",\"empId\":\"1586694397294622\"},{\"empType.dict.value\":\"1488308438114576\",\"post\":\"1574691940571202\",\"organize\":\"1572549923108870\",\"workno\":\"CI200068\",\"empId\":\"1586622025160732\"},{\"empType.dict.value\":\"1488308438114550\",\"company\":\"1572530321446915\",\"organize\":\"1585334968162507\",\"workno\":\"CI100074\",\"empId\":\"1585534102378515\"},{\"empType.dict.value\":\"1488308438114550\",\"post\":\"1579480148842529\",\"company\":\"1572530321446915\",\"organize\":\"1578059113363469\",\"workno\":\"CI100076\",\"empId\":\"1585855291136021\"},{\"empType.dict.value\":\"1488308438114550\",\"post\":\"1583030000334972\",\"company\":\"1573799831525410\",\"organize\":\"1583215276562445\",\"workno\":\"CI100073\",\"empId\":\"1585442741958674\"},{\"empType.dict.value\":\"1488308438114550\",\"post\":\"1585443387169478\",\"company\":\"1573799831525410\",\"organize\":\"1579484530898980\",\"workno\":\"CI100077\",\"empId\":\"1585877293013014\"},{\"empType.dict.value\":\"1488308438114550\",\"company\":\"1573799831525410\",\"organize\":\"1585334968162507\",\"workno\":\"CI100072\",\"empId\":\"1585434732025873\"},{\"empType.dict.value\":\"1488308438114550\",\"company\":\"1573799831525410\",\"organize\":\"1583215276562445\",\"workno\":\"CI100070\",\"empId\":\"1585411429816335\"},{\"post\":\"1579480148842529\",\"organize\":\"1578059113363469\",\"workno\":\"CI0013\",\"empId\":\"1584501781772291\"},{\"post\":\"1579480148842529\",\"organize\":\"1578059113363469\",\"workno\":\"CI0004\",\"empId\":\"1582542296430596\"},{\"empType.dict.value\":\"1488308438114550\",\"company\":\"1572530321446915\",\"organize\":\"1574610083977224\",\"workno\":\"CI50005\",\"empId\":\"1574617296738305\"},{\"empType.dict.value\":\"1488308438114550\",\"company\":\"1572530321446915\",\"organize\":\"1572536374220804\",\"workno\":\"CI500002\",\"empId\":\"1572558897362947\"}]";
        List<Map> workInfoVoList = FastjsonUtil.toList(empJson, Map.class);

        String abcJson = "{\"id\": \"1697619268583_621\", \"children\": [{\"id\": \"1697619298867_186\", \"type\": \"single\", \"condition\": {\"name\": \"empType.dict.value\", \"value\": \"1488308438114575\", \"symbol\": \"EQ\", \"simpleValue\": \"1488308438114575\", \"componentType\": \"DICT_SELECTOR\"}}], \"relation\": \"and\"}";
        ConditionTree exp = FastjsonUtil.toObject(abcJson, ConditionTree.class);
        List<Long> empIdList = Lists.newArrayList();
        for (Map<String, String> empMap : workInfoVoList) {
            //.dict.value 去掉
            ConditionTree expression = FastjsonUtil.convertObject(exp, ConditionTree.class);
            if (expression != null && expression.match(empMap)) {
                empIdList.add(Long.parseLong(empMap.get("empId")));
            }
        }
        System.out.println(empIdList.size());
    }

    private static void test1(){
        String json  = "[{\"empType.dict.value\":\"1488308438114551\",\"post\":\"1653314330851391\",\"company\":\"1612033006477181\",\"organize\":\"1582415152732182\",\"workno\":\"CI32224\",\"cityWork\":\"150000/null/150100/null\",\"empId\":\"1622233880213510\"},{\"empType.dict.value\":\"1488308438114550\",\"post\":\"1586741182996134\",\"company\":\"1612027461917447\",\"organize\":\"1586740545740453\",\"workno\":\"CI24519\",\"cityWork\":\"130000/null/130200/null\",\"empId\":\"1643978671962113\"}]";
        List<Map> workInfoVoList = FastjsonUtil.toList(json, Map.class);
        System.out.println("synRuleSubject workInfoVoList:" + FastjsonUtil.toJson(workInfoVoList));
        String abcJson = "{\"id\": \"1697693345741_559\", \"children\": [{\"id\": \"1697694182081_334\", \"type\": \"single\", \"condition\": {\"name\": \"cityWork\", \"value\": [\"130000\", \"130200\"], \"symbol\": \"EQ\", \"simpleValue\": \"{\\\"province\\\":\\\"130000\\\",\\\"area\\\":\\\"130200\\\"}\", \"componentType\": \"ADDRESS\"}}], \"relation\": \"and\"}";
        ConditionTree exp = FastjsonUtil.toObject(abcJson, ConditionTree.class);
        List<Long> empIdList = Lists.newArrayList();
        for (Map<String, String> empMap : workInfoVoList) {
            //.dict.value 去掉
            ConditionTree expression = FastjsonUtil.convertObject(exp, ConditionTree.class);
            Map<String, String> result = mapConvert(empMap);
            if (expression != null && expression.match(result)) {
                empIdList.add(Long.parseLong(result.get("empId")));
            }
        }
        System.out.println(empIdList.size());
    }

    private static Map<String, String> mapConvert(Map<String, String> empMap) {
        Map<String, String> result = new HashMap<>();
        // empType.dict.value
        for (Map.Entry<String, String> entry : empMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (key.contains(".dict.value")) {
                result.put(key.substring(0, key.indexOf(".")), value);
            } else {
                result.put(key, value);
            }
        }

        return result;
    }
}
