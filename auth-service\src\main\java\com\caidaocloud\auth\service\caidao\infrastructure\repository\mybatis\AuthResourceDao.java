package com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthResourcePo;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/6
 **/
public interface AuthResourceDao extends BaseMapper<AuthResourcePo> {

    /**
     * 获取用于gateway的url
     *
     * @return
     */
    @Select("SELECT url FROM auth_resource WHERE is_gateway_filter = 1")
    List<String> selectUrlOfFilterGateway();

}
