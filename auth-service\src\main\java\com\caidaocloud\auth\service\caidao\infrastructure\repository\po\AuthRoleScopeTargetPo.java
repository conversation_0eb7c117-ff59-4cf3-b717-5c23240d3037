package com.caidaocloud.auth.service.caidao.infrastructure.repository.po;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.auth.core.dto.AuthRoleScopeTargetDetail;
import com.caidaocloud.auth.core.enums.AuthRoleScopeTargetType;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2024/8/13
 */
@Data
@TableName("auth_role_scope_target")
public class AuthRoleScopeTargetPo {
	private Long id;
	private String code;
	private String name;
	private AuthRoleScopeTargetType type;
	private Long createTime;
	private String createBy;
}
