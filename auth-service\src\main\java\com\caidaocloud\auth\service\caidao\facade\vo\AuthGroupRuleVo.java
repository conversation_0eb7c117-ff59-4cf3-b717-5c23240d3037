package com.caidaocloud.auth.service.caidao.facade.vo;

import com.caidaocloud.auth.service.caidao.infrastructure.enums.AdapterDeviceEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/21
 **/
@Data
@ApiModel(value = "角色组详情vo")
public class AuthGroupRuleVo {

    @ApiModelProperty("角色id")
    private Long id;

    @ApiModelProperty("角色名称")
    private String name;

    @ApiModelProperty("角色code")
    private String code;

    @ApiModelProperty("角色描述")
    private String remark;

    @ApiModelProperty("是否启用")
    private Boolean isEnabled;

    @ApiModelProperty("角色类型;0：系统角色 1：自定义角色")
    private Integer roleType;

    @ApiModelProperty("适用设备")
    private List<AdapterDeviceEnum> device;

    @ApiModelProperty("权限")
    private List<AuthRolePermissionVo> authRolePermissionList;

    private List<AuthRoleScopeVo> authRoleScopeList;

}
