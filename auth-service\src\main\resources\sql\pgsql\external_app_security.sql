create table if not exists user_center.external_app_security
(
    id          varchar(64) not null,
    tenant_id   varchar(64) not null,
    app_key     varchar(500) default '',
    app_secret  varchar(500) default '',
    type        varchar(50)  default '',
    create_time bigint,
    update_time bigint,
    deleted     smallint default 0,
    primary key (id)
);
COMMENT
ON TABLE user_center.external_app_security IS '外部密钥表';
COMMENT
ON COLUMN user_center.external_app_security.tenant_id IS '租户ID';
COMMENT
ON COLUMN user_center.external_app_security.app_key IS 'appkey';
COMMENT
ON COLUMN user_center.external_app_security.app_secret IS 'appsecret';
COMMENT
ON COLUMN user_center.external_app_security.type IS '类型';
COMMENT
ON COLUMN user_center.external_app_security.create_time IS '创建时间';
COMMENT
ON COLUMN user_center.external_app_security.update_time IS '更新时间';
COMMENT
ON COLUMN user_center.external_app_security.deleted IS '是否删除';