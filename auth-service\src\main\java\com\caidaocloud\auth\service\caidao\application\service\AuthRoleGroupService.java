package com.caidaocloud.auth.service.caidao.application.service;

import com.caidaocloud.auth.service.caidao.application.dto.AuthRoleGroupDto;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleGroupDo;
import com.caidaocloud.auth.service.caidao.domain.service.AuthRoleGroupDomainService;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 角色组
 *
 * <AUTHOR>
 * @date 2022/5/9
 **/
@Service
@Slf4j
public class AuthRoleGroupService {

    @Autowired
    private AuthRoleGroupDomainService authRoleGroupDomainService;

    /**
     * 保存或更新角色组
     *
     * @param authRoleGroupDto
     */
    public void saveOrUpdateRoleGroup(AuthRoleGroupDto authRoleGroupDto) {
        if (authRoleGroupDto == null) {
            throw new ServerException("parameter is null");
        }
        var authRoleGroupDo = FastjsonUtil.convertObject(authRoleGroupDto, AuthRoleGroupDo.class);
        authRoleGroupDomainService.saveOrUpdateRoleGroup(authRoleGroupDo);
    }

    public List<Long> getRoleDefaultGroupRuleId(List<Long> roleIds) {
        return authRoleGroupDomainService.getRoleDefaultGroupRuleId(roleIds);
    }

}
