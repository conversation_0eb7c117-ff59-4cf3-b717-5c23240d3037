package com.caidaocloud.auth.service.caidao.infrastructure.config.auth;

import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.security.oauth2.provider.error.OAuth2AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@Component
public class OAuthClientExceptionEntryPoint extends OAuth2AuthenticationEntryPoint {
    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e) throws IOException, ServletException {
        response.setStatus(401);
        response.setContentType("application/json;charset=utf-8");

        PrintWriter out = response.getWriter();
        out.println(FastjsonUtil.toJson(Result.fail(e.getMessage())));
        out.flush();
        out.close();
    }

    @Override
    protected ResponseEntity<OAuth2Exception> enhanceResponse(ResponseEntity<OAuth2Exception> response, Exception exception) {
        return super.enhanceResponse(response, exception);
    }
}
