package com.caidaocloud.auth.service.caidao.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthResourceDo;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthResourceRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.AuthResourceDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthResourcePo;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.service.IAuthResourceService;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.WebUtil;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;

@Slf4j
@Repository
public class AuthResourceRepositoryImpl implements IAuthResourceRepository {
    @Autowired
    private AuthResourceDao authResourceDao;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private IAuthResourceService authResourceService;

    @Override
    public List<AuthResourceDo> list(ResourceCategoryEnum resourceCategory, String parentCode) {
        var tenantId = sessionService.getTenantId();
        var lang = MessageHandler.getLocaleByRequest(WebUtil.getRequest()).getLanguage();
        LambdaQueryWrapper<AuthResourcePo> wrapper = new LambdaQueryWrapper<AuthResourcePo>()
                //.eq(AuthResourcePo::getLang, lang)
                .in(AuthResourcePo::getTenantId, tenantId, "0")
                .eq(AuthResourcePo::getDeleted, 0);
        if (null != resourceCategory) {
            wrapper.eq(AuthResourcePo::getCategory, resourceCategory);
        }
        if (StringUtils.isNotEmpty(parentCode)) {
            wrapper.in(AuthResourcePo::getParentCode, Lists.list(parentCode.split(",")));
        }
        var list = authResourceDao.selectList(wrapper);
        return FastjsonUtil.convertList(list, AuthResourceDo.class);
    }

    @Override
    public List<String> selectUrlOfFilterGateway() {
        return authResourceDao.selectUrlOfFilterGateway();
    }

    @Override
    public void remove(String code) {
        if (StringUtils.isBlank(code)) {
            throw new ServerException("code is empty");
        }
        var queryWrapper = new LambdaQueryWrapper<AuthResourcePo>()
                .eq(AuthResourcePo::getCode, code).eq(AuthResourcePo::getDeleted, 0);
        authResourceDao.delete(queryWrapper);
        var queryWrapper1 = new LambdaQueryWrapper<AuthResourcePo>()
                .eq(AuthResourcePo::getParentCode, code).eq(AuthResourcePo::getDeleted, 0);
        authResourceDao.delete(queryWrapper1);
    }

    @Override
    public List<AuthResourceDo> listByCode(String code) {
        var tenantId = sessionService.getTenantId();
        LambdaQueryWrapper<AuthResourcePo> wrapper = new LambdaQueryWrapper<AuthResourcePo>()
                .eq(AuthResourcePo::getCode, code).in(AuthResourcePo::getTenantId, tenantId, "0");
        var resultList = authResourceDao.selectList(wrapper);
        return FastjsonUtil.convertList(resultList, AuthResourceDo.class);
    }

    @Override
    public List<AuthResourceDo> listByUrl(String url) {
        var tenantId = sessionService.getTenantId();
        LambdaQueryWrapper<AuthResourcePo> wrapper = new LambdaQueryWrapper<AuthResourcePo>()
                .eq(AuthResourcePo::getUrl, url).in(AuthResourcePo::getTenantId, "0", tenantId);
        var resultList = authResourceDao.selectList(wrapper);
        resultList.sort(Comparator.comparing(AuthResourcePo::getTenantId).reversed());
        return FastjsonUtil.convertList(resultList, AuthResourceDo.class);
    }


    @Override
    public Long insert(AuthResourceDo authResourceDo) {
        authResourceDao.insert(FastjsonUtil.convertObject(authResourceDo, AuthResourcePo.class));
        return null;
    }

    @Override
    public void insertBatch(List<AuthResourceDo> list) {
        if (CollectionUtils.isEmpty(list)) {
            log.info("list is empty");
            return;
        }
        var authResourcePoList = FastjsonUtil.convertList(list, AuthResourcePo.class);
        authResourceService.saveBatch(authResourcePoList);
    }

    @Override
    public void updateByCode(AuthResourceDo authResourceDo) {
        authResourceDao.update(
                FastjsonUtil.convertObject(authResourceDo, AuthResourcePo.class),
                new LambdaQueryWrapper<AuthResourcePo>()
                        .eq(AuthResourcePo::getCode, authResourceDo.getCode())
                        .eq(AuthResourcePo::getTenantId, authResourceDo.getTenantId())
        );
    }

    @Override
    public AuthResourceDo loadByCode(String code) {
        var tenantId = sessionService.getTenantId();
        var lang = MessageHandler.getLocaleByRequest(WebUtil.getRequest()).getLanguage();
        LambdaQueryWrapper<AuthResourcePo> wrapper = new LambdaQueryWrapper<AuthResourcePo>()
                .eq(AuthResourcePo::getCode, code)
                //.eq(AuthResourcePo::getLang, lang)
                .in(AuthResourcePo::getTenantId, tenantId, "0");
        var one = authResourceDao.selectOne(wrapper);
        if (one == null) {
            return null;
        }
        return FastjsonUtil.convertObject(one, AuthResourceDo.class);
    }

    @Override
    public List<AuthResourceDo> loadByCodes(List<String> codeList) {
        if (CollectionUtils.isEmpty(codeList)) {
            return Lists.list();
        }
        var tenantId = sessionService.getTenantId();
        var wrapper = new LambdaQueryWrapper<AuthResourcePo>()
                .eq(AuthResourcePo::getDeleted, 0)
                .in(AuthResourcePo::getTenantId, tenantId, "0")
                .in(AuthResourcePo::getCode, codeList);
        var authResourcePoList = authResourceDao.selectList(wrapper);
        if (CollectionUtils.isEmpty(authResourcePoList)) {
            return Lists.list();
        }
        return FastjsonUtil.convertList(authResourcePoList, AuthResourceDo.class);
    }

    @Override
    public AuthResourceDo loadByCode(String code, String lang, String... tenantIds) {
        LambdaQueryWrapper<AuthResourcePo> wrapper = new LambdaQueryWrapper<AuthResourcePo>()
                .eq(AuthResourcePo::getCode, code)
                //.eq(AuthResourcePo::getLang, lang)
                .in(AuthResourcePo::getTenantId, tenantIds);
        var resultList = authResourceDao.selectList(wrapper);
        if (CollectionUtils.isEmpty(resultList)) {
            return null;
        }
        var optional = resultList.stream()
                .sorted(Comparator.comparing(AuthResourcePo::getCreateTime).reversed()).findFirst();
        return FastjsonUtil.convertObject(optional.get(), AuthResourceDo.class);
    }
}
