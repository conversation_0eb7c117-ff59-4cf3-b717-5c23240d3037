package com.caidaocloud.auth.service.caidao.application.service;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.auth.core.enums.AuthRoleScopeTargetType;
import com.caidaocloud.auth.core.util.LangUtil;
import com.caidaocloud.auth.core.util.ObjectConvertUtil;
import com.caidaocloud.auth.service.caidao.application.dto.AuthRoleDto;
import com.caidaocloud.auth.service.caidao.application.dto.AuthRoleGroupDto;
import com.caidaocloud.auth.service.caidao.application.dto.AuthRoleScopeDto;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleDo;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleScopeDo;
import com.caidaocloud.auth.service.caidao.domain.service.AuthRoleDomainService;
import com.caidaocloud.auth.service.caidao.domain.service.AuthRoleGroupDomainService;
import com.caidaocloud.auth.service.caidao.facade.vo.AuthRolePermissionVo;
import com.caidaocloud.auth.service.caidao.facade.vo.AuthRoleScopeVo;
import com.caidaocloud.auth.service.caidao.facade.vo.AuthRoleVo;
import com.caidaocloud.auth.service.caidao.facade.vo.RoleDetailVo;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.AuthRoleScopeEnum;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.AuthRolePermissionDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthRolePermissionPo;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import com.jarvis.cache.annotation.Cache;
import com.jarvis.cache.annotation.CacheDelete;
import com.jarvis.cache.annotation.CacheDeleteKey;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 角色
 *
 * <AUTHOR>
 * @date 2022/5/7
 **/
@Service
@Slf4j
public class AuthRoleService {
    @Resource
    private AuthRoleGroupService oAuthRoleGroupService;
    @Resource
    private AuthRoleDomainService authRoleDomainService;
    @Resource
    private AuthRoleGroupDomainService authRoleGroupDomainService;

    @Transactional(rollbackFor = Exception.class)
    @CacheDelete({@CacheDeleteKey(value = "'auth-role-' + #args[0].id")})
    public Long saveOrUpdateRole(AuthRoleDto authRoleDto) {
        authRoleDto.setName(String.valueOf(authRoleDto.getI18nName().get("default")));
        if (authRoleDto == null) {
            throw new ServerException("parameter is null");
        }
        if (null != authRoleDto.getId()) {
            if (isExistByRoleName(authRoleDto.getName(), authRoleDto.getId())) {
                throw ServerException.globalException(ErrorMessage.fromCode("AUTH_ROLE_NAME_EXISTED"));
            }
        } else {
            if (isExistByRoleName(authRoleDto.getName())) {
                throw ServerException.globalException(ErrorMessage.fromCode("AUTH_ROLE_NAME_EXISTED"));
            }
        }
        var authRoleDo = FastjsonUtil.convertObject(authRoleDto, AuthRoleDo.class);
        authRoleDo.setI18nName(LangUtil.getI18nValue(authRoleDo.getI18nName(),authRoleDto.getI18nName()));
        if (authRoleDto.getI18nName()!=null){
            authRoleDo.setName((String) authRoleDto.getI18nName().get("default"));
        }
        var roleId = authRoleDomainService.saveOrUpdateRole(authRoleDo);
        if (authRoleDto.getId() == null) {
            saveOrUpdateRoleGroup(roleId, authRoleDto.getName());
        } else {
            List<Long> roleDefaultGroupRuleId = oAuthRoleGroupService.getRoleDefaultGroupRuleId(Lists.newArrayList(roleId));
            if (null == roleDefaultGroupRuleId || roleDefaultGroupRuleId.isEmpty()) {
                saveOrUpdateRoleGroup(roleId, authRoleDto.getName());
            }
        }
        saveAuthRoleScope(roleId, authRoleDto.getAuthRoleScopeList(), authRoleDto.getReportRoleScopeList());
        return roleId;
    }

    private void saveOrUpdateRoleGroup(Long roleId, String name) {
        AuthRoleGroupDto authRoleGroupDto = new AuthRoleGroupDto();
        authRoleGroupDto.setName(name);
        authRoleGroupDto.setRoleIdList(Lists.newArrayList(roleId));
        authRoleGroupDto.setIsDefaultRoleGroup(true);
        oAuthRoleGroupService.saveOrUpdateRoleGroup(authRoleGroupDto);
    }

    private void saveAuthRoleScope(Long roleId, List<AuthRoleScopeDto> authRoleScopeList, List<AuthRoleScopeDto> reportRoleScopeList) {
        val scopeList = authRoleScopeList.stream().map(it -> {
            AuthRoleScopeDo scope = FastjsonUtil.convertObject(it, AuthRoleScopeDo.class);
            scope.setRoleId(roleId);
            return scope;
        }).collect(Collectors.toList());
        AuthRoleScopeDo.flushAll(roleId, scopeList, AuthRoleScopeEnum.DATA_SCOPE);
        val reportScopeList = reportRoleScopeList.stream().map(it -> {
            AuthRoleScopeDo scope = FastjsonUtil.convertObject(it, AuthRoleScopeDo.class);
            scope.setRoleId(roleId);
            return scope;
        }).collect(Collectors.toList());
        AuthRoleScopeDo.flushAll(roleId, reportScopeList, AuthRoleScopeEnum.REPORT_SCOPE);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteRole(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            throw new ServerException("roleIdList is null");
        }
        authRoleDomainService.deleteRole(roleIdList);
        authRoleGroupDomainService.removeByRoleIds(roleIdList);
        AuthRoleScopeDo.deleteByRoleIds(roleIdList, AuthRoleScopeEnum.DATA_SCOPE);
    }

    @Transactional(rollbackFor = Exception.class)
    public void copyRole(Long roleId) {
        if (roleId == null) {
            throw new ServerException("roleId is null");
        }
        val copyId = authRoleDomainService.copyRole(roleId);
        val role = getRoleByRoleId(copyId);
        AuthRoleGroupDto authRoleGroupDto = new AuthRoleGroupDto();
        authRoleGroupDto.setName(role.getName());
        authRoleGroupDto.setRoleIdList(Lists.newArrayList(copyId));
        authRoleGroupDto.setIsDefaultRoleGroup(true);
        oAuthRoleGroupService.saveOrUpdateRoleGroup(authRoleGroupDto);
        AuthRoleScopeDo.copy(roleId, copyId, AuthRoleScopeEnum.DATA_SCOPE);
    }

    public PageResult<AuthRoleVo> queryPageOfRole(QueryPageBean queryPageBean) {
        var pageOfRole = authRoleDomainService.getPageOfRole(queryPageBean);
//        var authRoleVos = FastjsonUtil.convertList(pageOfRole.getItems(), AuthRoleVo.class);
        List<AuthRoleVo> authRoleVos1 = ObjectConvertUtil.convertList(pageOfRole.getItems(), AuthRoleVo.class,
                (it, v1) -> {
                    v1.setI18nName(FastjsonUtil.toObject(it.getI18nName()==null?"":it.getI18nName(), Map.class));
                });

        var pageResult = new PageResult<AuthRoleVo>();
        BeanUtils.copyProperties(pageOfRole, pageResult, "items");
        pageResult.setItems(authRoleVos1);
        return pageResult;
    }

    public List<AuthRoleDo> getRoleByCode(List<String> codeList) {
        return authRoleDomainService.getAuthRoleByCode(codeList);
    }

    @Cache(expire = 300, key = "'auth-role-' + #args[0]")
    public RoleDetailVo getRoleByRoleId(Long roleId) {
        Option<AuthRoleDo> option = authRoleDomainService.getRoleByRoleId(roleId);
        if (option.isEmpty()) {
            return new RoleDetailVo();
        }
        AuthRoleDo authRoleDo = option.get();
        PreCheck.preCheckArgument(StringUtils.isEmpty(authRoleDo.getName()),ErrorMessage.fromCode("NAME_NOT_FOUND"));
        List<AuthRoleScopeDo> scopes = AuthRoleScopeDo.list(roleId, AuthRoleScopeEnum.DATA_SCOPE);
        String i18nNameStr = null;
        if (StringUtils.isNotBlank(authRoleDo.getI18nName())) {
            i18nNameStr = authRoleDo.getI18nName().replace("\\\"", "\"");
        }
        authRoleDo.setI18nName(null);
        var result = FastjsonUtil.convertObject(authRoleDo, RoleDetailVo.class);
        Map<String, Object> i18nName;
        if (i18nNameStr != null) {
            i18nName = FastjsonUtil.toObject(i18nNameStr, Map.class);
            if (!i18nNameStr.contains("default")) {
                i18nName.put("default",result.getName());
            }
        }else {
            i18nName=new HashMap<>();
            i18nName.put("default",result.getName());
        }
        result.setI18nName(i18nName);
        result.setAuthRoleScopeList(FastjsonUtil.convertList(scopes, AuthRoleScopeVo.class));
        List<AuthRoleScopeDo> reportScopes = AuthRoleScopeDo.list(roleId, AuthRoleScopeEnum.REPORT_SCOPE);
        result.setReportRoleScopeList(FastjsonUtil.convertList(reportScopes, AuthRoleScopeVo.class));
        result.sort();
        return result;
    }

    public List<AuthRolePermissionVo> getPermissionOfRole(Long roleId) {
        return FastjsonUtil.convertList(authRoleDomainService.getPermissionOfRole(roleId), AuthRolePermissionVo.class);
    }

    public boolean isExistByRoleName(String roleName) {
        if (StringUtils.isBlank(roleName)) {
            throw new ServerException("role name is empty");
        }
        return authRoleDomainService.isExistByRoleName(roleName);
    }

    public boolean isExistByRoleName(String roleName, Long excludeId) {
        if (StringUtils.isBlank(roleName)) {
            throw new ServerException("role name is empty");
        }
        return authRoleDomainService.isExistByRoleName(roleName, excludeId);
    }

    public List<KeyValue> getEnabledOfAllRole(String param) {
        var allRole = authRoleDomainService.getAllRole();
        var sequence = Sequences.sequence(allRole);
        if (sequence.isEmpty()) {
            return Lists.newArrayList();
        }
        List<KeyValue> keyValues;

        if (StringUtils.isEmpty(param)) {
            keyValues = sequence.map(e -> {
                var authRoleInfoVo = new KeyValue();
                //没有参数 直接全部
                authRoleInfoVo.setText(e.getName());
                authRoleInfoVo.setValue(e.getId());
                return authRoleInfoVo;
            }).toList();
        } else {
            keyValues = sequence.filter(q -> StringUtils.isNotEmpty(q.getName()) && q.getName().contains(param)).map(e -> {
                var authRoleInfoVo = new KeyValue();
                authRoleInfoVo.setText(e.getName());
                authRoleInfoVo.setValue(e.getId());
                return authRoleInfoVo;
            }).toList();
        }
        return keyValues;
    }

    public void moduleRefresh(Long roleId) {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        String empId = null == userInfo.getEmpId() ? "0" : userInfo.getEmpId().toString();
        long ct = System.currentTimeMillis();
        if (null != roleId) {
            roleRefresh(roleId, empId, userInfo.getTenantId(), ct);
            return;
        }

        QueryPageBean query = new QueryPageBean();
        query.setPageSize(50);
        query.setPageNo(1);
        doRoleRefresh(query, empId, userInfo.getTenantId(), ct);
    }

    public void doRoleRefresh(QueryPageBean query, String empId, String tenantId, Long ct) {
        var pageOfRole = authRoleDomainService.getPageOfRole(query);
        if (null == pageOfRole || null == pageOfRole.getItems() || pageOfRole.getItems().isEmpty()) {
            return;
        }
        pageOfRole.getItems().forEach(authRole -> {
            roleRefresh(authRole.getId(), empId, tenantId, ct);
        });
        if (pageOfRole.getItems().size() < 50) {
            return;
        }
        query.setPageNo(query.getPageNo() + 1);
        doRoleRefresh(query, empId, tenantId, ct);
    }

    public void roleRefresh(Long roleId, String empId, String tenantId, Long ct) {
        try {
            RoleDetailVo rdv = getRoleByRoleId(roleId);
            if (null == rdv || null == rdv.getId() || null == rdv.getAuthRolePermissionList()) {
                return;
            }
            Map<String, String> newMap = new HashMap<>();
            newMap.put("ORGANIZATION", "ORGANIZATION");
            newMap.put("EMPLOYEE", "EMPLOYEE");
            newMap.put("ATTENDANCE", "ATTENDANCE");
            newMap.put("PAYROLL", "PAYROLL");
            newMap.put("PORTAL", "PORTAL");
            newMap.put("HC", "HC");
            newMap.put("WORKFLOW", "WORKFLOW");
            newMap.put("ATS", "ATS");
            newMap.put("PERFORMANCE", "PERFORMANCE");
            newMap.put("BI", "BI");
            Map<String, String> cMap = rdv.getAuthRolePermissionList().stream().map(arp -> arp.getCode())
                    .collect(Collectors.toMap(e -> e, e -> e, (v1, v2) -> v1));
            rdv.getAuthRolePermissionList().forEach(arp -> {
                if (StringUtil.isNotEmpty(arp.getParentCode())
                        && !cMap.containsKey(arp.getParentCode()) && newMap.containsKey(arp.getParentCode())) {
                    // 追加父级模块保存
                    saveRolePermission(roleId, empId, tenantId, ct, arp.getParentCode());
                    newMap.remove(arp.getParentCode());
                    return;
                }
            });
            List<String> codeList = Lists.newArrayList("myPending", "myDone", "myInitiated", "WORKFLOW");
            codeList.forEach(rCode -> {
                if (!cMap.containsKey(rCode)) {
                    saveRolePermission(roleId, empId, tenantId, ct, rCode);
                }
            });
        } catch (Exception e) {
            log.error("add module role err,roleId={}, errMsg={}", roleId, e.getMessage(), e);
        }
    }

    private void saveRolePermission(Long roleId, String empId, String tenantId, Long ct, String rCode) {
        AuthRolePermissionPo arpPo = new AuthRolePermissionPo();
        arpPo.setCategory(ResourceCategoryEnum.MENU);
        arpPo.setResourceCode(rCode);
        arpPo.setCreateTime(ct);
        arpPo.setCreateBy(empId);
        arpPo.setRoleId(roleId);
        arpPo.setTenantId(tenantId);
        arpPo.setDeleted(false);
        arpPo.setUpdateBy(empId);
        arpPo.setUpdateTime(ct);
        SpringUtil.getBean(AuthRolePermissionDao.class).insert(arpPo);
    }
}
