package com.caidaocloud.auth.service.caidao.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleGroupDo;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthGroupRuleRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.AuthGroupRuleDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.AuthRoleGroupRuleDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthGroupRulePo;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthRoleGroupRulePo;
import com.caidaocloud.auth.service.caidao.infrastructure.util.TenantSelectPageUtil;
import com.caidaocloud.auth.service.caidao.infrastructure.util.UserContextUtil;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.base.Joiner;
import com.googlecode.totallylazy.Sequences;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/9
 **/
@Repository
public class AuthGroupRuleRepositoryImpl implements IAuthGroupRuleRepository {
    @Autowired
    private AuthGroupRuleDao authGroupRuleDao;
    @Resource
    private AuthRoleGroupRuleDao authRoleGroupRuleDao;

    @Override
    public void saveOrUpdateRoleGroup(AuthRoleGroupDo authRoleGroupDo) {
        if (authRoleGroupDo == null) {
            throw new ServerException("parameter is null");
        }
        if (StringUtils.isBlank(authRoleGroupDo.getName())) {
            throw new ServerException("name is null");
        }
        val userInfo = UserContextUtil.preCheckUser();
        if (userInfo == null) {
            throw new ServerException("not found user info");
        }
        val empId = userInfo.getStaffId() == null ? "0" : userInfo.getStaffId().toString();
        var currentTimeMillis = System.currentTimeMillis();
        var authGroupRulePo = FastjsonUtil.convertObject(authRoleGroupDo, AuthGroupRulePo.class);
        authGroupRulePo.setRoleId(Joiner.on(",").join(authRoleGroupDo.getRoleIdList()));
        authGroupRulePo.setTenantId(userInfo.getTenantId());
        if (authGroupRulePo.getId() == null) {
            authGroupRulePo.setCreateBy(empId);
            authGroupRulePo.setCreateTime(currentTimeMillis);
            authGroupRulePo.setDeleted(false);
            authGroupRuleDao.insert(authGroupRulePo);
            insertBatchAuthRoleGroupRule(authRoleGroupDo.getRoleIdList(), authGroupRulePo);
            return;
        }

        authGroupRulePo.setUpdateBy(empId);
        authGroupRulePo.setUpdateTime(currentTimeMillis);
        LambdaQueryWrapper<AuthRoleGroupRulePo> qw = new LambdaQueryWrapper<>();
        qw.eq(AuthRoleGroupRulePo::getTenantId, userInfo.getTenantId())
            .eq(AuthRoleGroupRulePo::getGroupRuleId, authGroupRulePo.getId());
        List<AuthRoleGroupRulePo> agrList = authRoleGroupRuleDao.selectList(qw);

        Map<Long, Long> collect = agrList.stream().collect(Collectors.toMap(e -> e.getRoleId(), e -> e.getId(), (v1, v2) -> v1));
        List<Long> insertRoleList = authRoleGroupDo.getRoleIdList().stream().filter(rId -> !collect.containsKey(rId)).collect(Collectors.toList());
        insertBatchAuthRoleGroupRule(insertRoleList, authGroupRulePo);

        LambdaQueryWrapper<AuthRoleGroupRulePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AuthRoleGroupRulePo::getTenantId, userInfo.getTenantId())
                .eq(AuthRoleGroupRulePo::getGroupRuleId, authGroupRulePo.getId())
                .notIn(AuthRoleGroupRulePo::getRoleId, authRoleGroupDo.getRoleIdList());
        authRoleGroupRuleDao.delete(queryWrapper);
        authGroupRuleDao.updateById(authGroupRulePo);
    }

    private void insertBatchAuthRoleGroupRule(List<Long> roleIdList, AuthGroupRulePo authGroupRulePo){
        long createTime = System.currentTimeMillis();
        List<AuthRoleGroupRulePo> collect = roleIdList.stream().map(rId -> {
            AuthRoleGroupRulePo rgr = new AuthRoleGroupRulePo();
            rgr.setCreateTime(createTime);
            rgr.setUpdateTime(createTime);
            rgr.setRoleId(rId);
            rgr.setUpdateBy(authGroupRulePo.getUpdateBy());
            rgr.setCreateBy(authGroupRulePo.getUpdateBy());
            rgr.setGroupRuleId(authGroupRulePo.getId());
            rgr.setDeleted(false);
            rgr.setTenantId(authGroupRulePo.getTenantId());
            return rgr;
        }).collect(Collectors.toList());
        if(!collect.isEmpty()){
            authRoleGroupRuleDao.insertBatchSomeColumn(collect);
        }
    }

    @Override
    public AuthGroupRulePo getGroupRuleById(Long id) {
        return authGroupRuleDao.selectById(id);
    }

    @Override
    public int deleteGroupRule(List<Long> idList) {

        return authGroupRuleDao.deleteBatchIds(idList);
    }

    @Override
    public PageResult<AuthGroupRulePo> getPage(QueryPageBean queryPageBean) {

        var page = new Page<AuthGroupRulePo>(queryPageBean.getPageNo(), queryPageBean.getPageSize());
        var queryWrapper = new LambdaQueryWrapper<AuthGroupRulePo>();
        if (StringUtils.isNotBlank(queryPageBean.getKeywords())) {
            queryWrapper.like(AuthGroupRulePo::getName, queryPageBean.getKeywords());
        }
        queryWrapper.eq(true, AuthGroupRulePo::getDeleted, 0);
        Page<AuthGroupRulePo> pageResult = TenantSelectPageUtil.selectPage(authGroupRuleDao, page, queryWrapper);
        return new PageResult<AuthGroupRulePo>(Sequences.sequence(pageResult.getRecords()).map(e -> e.toEntity()).toList(),
                (int) pageResult.getCurrent(), (int) pageResult.getSize(), (int) pageResult.getTotal());

    }

    @Override
    public List<AuthGroupRulePo> getGroupRuleByIds(List<Long> ids) {
        return authGroupRuleDao.selectBatchIds(ids);
    }
}
