package com.caidaocloud.auth.service.caidao.facade.vo;

import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AuthResourceVo {

    @ApiModelProperty("资源编码code")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("资源分类")
    private ResourceCategoryEnum category;

    @ApiModelProperty("父级code")
    private String parentCode;

}
