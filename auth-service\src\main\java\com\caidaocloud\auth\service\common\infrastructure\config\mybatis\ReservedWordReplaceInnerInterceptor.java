package com.caidaocloud.auth.service.common.infrastructure.config.mybatis;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

/**
 * 替换数据库关键字
 * <AUTHOR>
 * @date 2023/5/6
 */
public class ReservedWordReplaceInnerInterceptor implements InnerInterceptor {

	private static final Pattern pattern = Pattern.compile("\\{#(key:\\w+?)}");

	private String escapeSymbol;

	public ReservedWordReplaceInnerInterceptor() {
	}

	public ReservedWordReplaceInnerInterceptor(String escapeSymbol) {
		this.escapeSymbol = escapeSymbol;
	}

	@Override
	public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
		String sql = boundSql.getSql();
		List<String> find = findPlaceholder(sql);
		if (CollectionUtils.isNotEmpty(find)) {
			sql = replaceSql(sql, find, escapeSymbol);
			PluginUtils.mpBoundSql(boundSql).sql(sql);
		}
	}

	@Override
	public void beforePrepare(StatementHandler sh, Connection connection, Integer transactionTimeout) {
		PluginUtils.MPStatementHandler mpSh = PluginUtils.mpStatementHandler(sh);
		MappedStatement ms = mpSh.mappedStatement();
		SqlCommandType sct = ms.getSqlCommandType();
		if (sct == SqlCommandType.INSERT || sct == SqlCommandType.UPDATE || sct == SqlCommandType.DELETE) {
			PluginUtils.MPBoundSql mpBs = mpSh.mPBoundSql();
			String sql = mpBs.sql();
			List<String> find = findPlaceholder(sql);
			if (CollectionUtils.isNotEmpty(find)) {
				sql = replaceSql(sql, find, escapeSymbol);
			}
			mpBs.sql(sql);
		}
		System.out.println(mpSh.mPBoundSql().sql());
	}

	public String replaceSql(String sql, List<String> find, String escapeSymbol) {
		for (String placeholder : find) {
			String key = placeholder.substring(placeholder.indexOf(":") + 1, placeholder.length() - 1);
			String escapeKey = escapeSymbol + key + escapeSymbol;
			sql = sql.replace(placeholder, escapeKey);
			sql = sql.replaceAll("AS " + key, "AS " + escapeKey);
		}
		return sql;
	}

	private  List<String> findPlaceholder(String sql) {
		Matcher matcher = pattern.matcher(sql);
		List<String> list = new ArrayList<>();
		while (matcher.find()) {
			list.add(matcher.group());
		}
		return list;
	}
}
