package com.caidaocloud.auth.service.caidao.domain.repository;

import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleScopeDo;

import java.util.List;

public interface IAuthRoleScopeRepository {
    void flushAll(Long roleId, List<AuthRoleScopeDo> scopeList);

    void deleteByRoleIds(List<Long> roleIdList);

    List<AuthRoleScopeDo> load(Long copyFromId);

    List<AuthRoleScopeDo> list(List<Long> roleIds);
}
