CREATE TABLE IF NOT EXISTS auth_group_rule_$tenant_id
(
    id                    SERIAL NOT NULL,
    tenant_id             VARCHAR(50)  DEFAULT '',
    name                  VARCHAR(50)  DEFAULT '',
    remark                VARCHAR(255) DEFAULT '',
    last_refreshed        BIGINT       DEFAULT NULL,
    expression            JSON NULL,
    status                SMALLINT     DEFAULT 0,
    create_time           BIGINT NOT NULL,
    create_by             VARCHAR(50)  DEFAULT '',
    update_time           BIGINT       DEFAULT NULL,
    update_by             VARCHAR(50)  DEFAULT '',
    is_default_role_group SMALLINT     DEFAULT 0,
    deleted               SMALLINT     DEFAULT 0,
    PRIMARY KEY (id)
);

COMMENT
ON TABLE auth_group_rule_$tenant_id IS '用户分组规则';
COMMENT
ON COLUMN auth_group_rule_$tenant_id.tenant_id IS '租户id';
COMMENT
ON COLUMN auth_group_rule_$tenant_id.name IS '规则名称';
COMMENT
ON COLUMN auth_group_rule_$tenant_id.remark IS '角色组描述';
COMMENT
ON COLUMN auth_group_rule_$tenant_id.last_refreshed IS '最近刷新时间';
COMMENT
ON COLUMN auth_group_rule_$tenant_id.expression IS '规则表达式';
COMMENT
ON COLUMN auth_group_rule_$tenant_id.status IS '状态;0：启用 1：禁用';
COMMENT
ON COLUMN auth_group_rule_$tenant_id.create_time IS '创建时间';
COMMENT
ON COLUMN auth_group_rule_$tenant_id.create_by IS '创建者';
COMMENT
ON COLUMN auth_group_rule_$tenant_id.update_time IS '更新时间';
COMMENT
ON COLUMN auth_group_rule_$tenant_id.update_by IS '更新者';
COMMENT
ON COLUMN auth_group_rule_$tenant_id.is_default_role_group IS '是否是角色默认角色组';
COMMENT
ON COLUMN auth_group_rule_$tenant_id.deleted IS '是否删除';