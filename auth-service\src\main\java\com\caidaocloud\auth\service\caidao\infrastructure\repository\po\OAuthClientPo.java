package com.caidaocloud.auth.service.caidao.infrastructure.repository.po;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@TableName("oauth_client")
@Accessors(chain = true)
public class OAuthClientPo implements Serializable {
    @TableId
    private String clientId;

    private String resourceIds;

    private String clientSecret;

    private String scope;

    private String authorizedGrantTypes;

    private String webServerRedirectUri;

    private String authorities;

    private Integer accessTokenValidity;

    private Integer refreshTokenValidity;

    private String additionalInformation;

    private String autoapprove;

    private String tenantId;

    private Long belongId;

    /**
     * 默认为 0，启用，1 禁用，2 删除
     */
    private Integer status;

    private String authUrl;
}