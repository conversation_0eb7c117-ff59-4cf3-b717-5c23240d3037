package com.caidaocloud.auth.service.caidao.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.auth.core.enums.AuthRoleScopeRestriction;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2024/8/13
 */
@Data
@TableName("auth_role_scope_target_detail")
public class AuthRoleScopeTargetDetailPo {
	private Long id;
	private Long targetId;

	private AuthRoleScopeRestriction restriction;

	private String identifier;

	private String property;

	private boolean inToOr;
}
