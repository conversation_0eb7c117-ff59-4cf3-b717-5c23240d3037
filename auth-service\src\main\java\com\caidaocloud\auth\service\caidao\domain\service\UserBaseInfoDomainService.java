package com.caidaocloud.auth.service.caidao.domain.service;

import com.caidaocloud.auth.service.caidao.domain.entity.UserAccountBaseInfo;
import com.caidaocloud.auth.service.caidao.domain.entity.UserBaseInfoDo;
import com.caidaocloud.auth.service.caidao.domain.repository.IUserBaseInfoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserBaseInfoDomainService {
    @Autowired
    private IUserBaseInfoRepository userBaseInfoRepository;

    public List<UserBaseInfoDo> selectUserBaseInfoByAccounts(List<String> accounts){
        return userBaseInfoRepository.selectUserBaseInfoByAccounts(accounts);
    }

    public List<UserBaseInfoDo> selectUserBaseInfoByEmpIds(List<Long> empIds){
        return userBaseInfoRepository.selectUserBaseInfoByEmpIds(empIds);
    }

    public List<UserAccountBaseInfo> selectByAccounts(List<String> accounts){
        return userBaseInfoRepository.selectByAccounts(accounts);
    }
}
