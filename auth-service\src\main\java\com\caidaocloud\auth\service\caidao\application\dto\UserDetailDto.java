package com.caidaocloud.auth.service.caidao.application.dto;

import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.util.Collection;

@Data
public class UserDetailDto extends User {
    private String tenantId;
    private Long belongId;
    private String salt;

    public UserDetailDto(String tenantId, Long belongId, String username,
        String password, String salt, Collection<? extends GrantedAuthority> authorities) {
        super(username, password, authorities);
        this.tenantId = tenantId;
        this.belongId = belongId;
        this.salt = salt;
    }

}
