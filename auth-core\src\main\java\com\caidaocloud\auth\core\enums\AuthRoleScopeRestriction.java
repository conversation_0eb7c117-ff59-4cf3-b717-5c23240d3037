package com.caidaocloud.auth.core.enums;

import lombok.Getter;

@Getter
public enum AuthRoleScopeRestriction {

    // 查看指定合同公司
    SELECTED_COMPANY(ValueComponentEnum.COMPANY),
    // 查看本组织及下级组织
    MY_ORG_AND_BELONGINGS(ValueComponentEnum.NUMBER_INPUT),
    // 查看本组织
    MY_ORG(ValueComponentEnum.NUMBER_INPUT),
    // 查看指定组织
    SELECTED_ORG(ValueComponentEnum.ORG),
    // 查看指定组织及下级组织
    SELECTED_ORG_AND_BELONGINGS(ValueComponentEnum.ORG),
    // 查看所属兼岗组织
    MY_CONCURRENT_ORG(ValueComponentEnum.NONE),
    // 查看所属兼岗组织及下级组织
    MY_CONCURRENT_ORG_AND_BELONGINGS(ValueComponentEnum.NONE),
    // 按HRBP查看
    SELECTED_HRBP(ValueComponentEnum.NONE),
    // 按部门负责人查看
    SELECTED_LEADER(ValueComponentEnum.NONE),
    // 按部门负责人查看
    MY_LEADER_AND_SUB(ValueComponentEnum.NONE),
    // 直接下级
    DIRECT_SUBORDINATE(ValueComponentEnum.NONE),
    // 全部下级
    ALL_SUBORDINATE(ValueComponentEnum.NONE),
    // 按HRBP查看（含兼岗）
    SELECTED_HRBP_WITH_CONCURRENT(ValueComponentEnum.NONE),
    // 按部门负责人查看（含兼岗）
    SELECTED_LEADER_WITH_CONCURRENT(ValueComponentEnum.NONE),
    // 查看直接下级（含兼岗）
    DIRECT_SUBORDINATE_WITH_CONCURRENT(ValueComponentEnum.NONE),
    //ALL_SUBORDINATE_WITH_CONCURRENT(ValueComponentEnum.NONE),
    // 我创建的
    CREATED_BY_MYSELF(ValueComponentEnum.NONE),
    // 指定员工
    SELECTED_EMP(ValueComponentEnum.EMP_SELECTOR),
    SELECTED_WORKPLACE(ValueComponentEnum.WORKPLACE),
    SELECTED_EMP_TYPE(ValueComponentEnum.EMP_TYPE),
    SELECTED_PAYROLL_PLAN(ValueComponentEnum.PAYROLL_PLAN),
    SELECTED_BONUS_PLAN(ValueComponentEnum.BONUS_PLAN),
    SPECIFIED_ORG_CODE_PREFIX(ValueComponentEnum.STRING_INPUT),
    SELECTED_CONTRACT_TYPE(ValueComponentEnum.NONE),
    ALL(ValueComponentEnum.NONE),

    // 查看指定所属业务
    SELECT_ARCHIVE_FILE(ValueComponentEnum.ARCHIVE_FILE),

    NO_AUTH(ValueComponentEnum.NONE),
    //查看本人
    MYSELF(ValueComponentEnum.NONE),
    //查看指定岗位
    SELECTED_POST(ValueComponentEnum.POST),
    //查看指定基准岗位
    SELECTED_BENCH_POST(ValueComponentEnum.BENCH_POST),
    //查看指定成本中心
    SELECTED_COST_CENTER(ValueComponentEnum.COST_CENTER),
    //查看本班组
    MY_SCHEDULE_GROUP(ValueComponentEnum.NONE),
    //查看指定班组
    SELECTED_SCHEDULE_GROUP(ValueComponentEnum.NONE),
    //按班组管理员查看
    MY_SCHEDULE_GROUP_ADMIN(ValueComponentEnum.NONE),
    SELECTED_BUSINESS_LINE(ValueComponentEnum.NONE)
    ;

    private ValueComponentEnum component;

    AuthRoleScopeRestriction(ValueComponentEnum component) {
        this.component = component;
    }
}
