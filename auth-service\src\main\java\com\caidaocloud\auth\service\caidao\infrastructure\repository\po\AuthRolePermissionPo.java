package com.caidaocloud.auth.service.caidao.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/5/7
 **/
@Data
@TableName("auth_role_permission")
public class AuthRolePermissionPo implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 资源编码
     */
    private String resourceCode;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 父级编码
     */
    private String parentCode;

    /**
     * 是否删除
     */
    private Boolean deleted;

    private String dataScopeDetail;

    /**
     * 资源分类
     */
    private ResourceCategoryEnum category;

}
