package com.caidaocloud.auth.service.external.application.service;

import com.caidaocloud.auth.service.external.application.authenticate.ExternalAuthenticateBuilder;
import com.caidaocloud.auth.service.external.application.authenticate.IExternalAuthService;
import com.caidaocloud.auth.service.external.application.dto.AppSecurityDto;
import com.caidaocloud.auth.service.external.domain.entity.AppSecurityEntity;
import com.caidaocloud.auth.service.external.domain.service.AppSecurityDomainService;
import com.caidaocloud.auth.service.external.infrastructure.enums.ExternalAuthEnum;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/1/8
 **/
@Slf4j
@Service
public class ExternalAuthApplicationService {
    @Autowired
    private AppSecurityDomainService appSecurityDomainService;

    public Map<String, Object> sso(ExternalAuthEnum type, HttpServletRequest request, HttpServletResponse response) {
        IExternalAuthService service = ExternalAuthenticateBuilder.getService(type);
        if (service == null) {
            throw new ServerException("not found service");
        }
        return service.sso(request, response);
    }

    public void saveOrUpdateSecurity(AppSecurityDto appSecurityDto) {
        if (Objects.isNull(appSecurityDto) || Objects.isNull(appSecurityDto.getType())) {
            return;
        }
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        var appSecurity = appSecurityDomainService.getAppSecurity(userInfo.getTenantId(), appSecurityDto.getType());
        if (appSecurity == null) {
            appSecurity = new AppSecurityEntity();
        }
        appSecurity.setAppKey(appSecurityDto.getAppKey())
                .setTenantId(userInfo.getTenantId())
                .setAppSecret(appSecurityDto.getAppSecret())
                .setType(appSecurityDto.getType())
                .setExt(appSecurityDto.getExt());
        appSecurityDomainService.saveOrUpdateAppSecurity(appSecurity);
    }
}