package com.caidaocloud.auth.service.caidao.domain.entity;

import java.util.List;

import javax.swing.Spring;

import com.caidaocloud.auth.core.dto.AuthRoleScopeTargetDetail;
import com.caidaocloud.auth.core.enums.AuthRoleScopeTargetType;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthRoleScopeTargetRepository;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Lists;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2024/8/13
 */
@Data
public class AuthRoleScopeTargetDo {
	private Long id;
	private String code;
	private List<AuthRoleScopeTargetDetail> details;
	private String name;
	private AuthRoleScopeTargetType type;


	public void save(){
		SpringUtil.getBean(IAuthRoleScopeTargetRepository.class).save(this);
	}

	public static  List<AuthRoleScopeTargetDo> loadAll(){
		return SpringUtil.getBean(IAuthRoleScopeTargetRepository.class).loadAll();
	}
}
