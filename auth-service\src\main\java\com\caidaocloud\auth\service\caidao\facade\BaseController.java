package com.caidaocloud.auth.service.caidao.facade;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @date 2022/5/12
 **/
@ControllerAdvice
@Slf4j
public class BaseController {

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public Result<String> handlerException(Exception exception) {
        log.error("{} \n {}", exception.getMessage(), exception);
        if (!(exception instanceof ServerException)) {
            return Result.fail("server exception");
        }
        return Result.fail(exception.getMessage());
    }

}
