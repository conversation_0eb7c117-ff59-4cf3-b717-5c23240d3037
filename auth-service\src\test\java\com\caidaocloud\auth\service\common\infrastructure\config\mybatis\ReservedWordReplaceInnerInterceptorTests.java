package com.caidaocloud.auth.service.common.infrastructure.config.mybatis;

import java.util.Arrays;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 *
 * #author <PERSON>
 * #date 2023/5/6
 */
@Slf4j
public class ReservedWordReplaceInnerInterceptorTests {


	private ReservedWordReplaceInnerInterceptor interceptor = new ReservedWordReplaceInnerInterceptor("\"");

	@Test
    public void testReplace() {
		List<String> find = Arrays.asList(
				"{#key:foo}",
				"{#key:bar}"
		);
		String sql = "SELECT * FROM my_table WHERE col1 = {#key:foo} AND col2 = {#key:bar}";
		String expected = "SELECT * FROM my_table WHERE col1 = `foo` AND col2 = `bar`";
		String actual = interceptor.replaceSql(sql, find, "`");
		assertEquals(expected, actual);
		log.info("replace succeeded");
    }
}