package com.caidaocloud.auth.service.caidao.domain.repository;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.EnumSet;

import com.caidaocloud.auth.service.AuthApplication;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleDo;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.AdapterDeviceEnum;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.service.SessionServiceImpl;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.RequestHelper;
import com.googlecode.totallylazy.Option;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/5/5
 */
@SpringBootTest(classes = AuthApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
@Slf4j
public class IAuthRoleRepositoryTests {

	@Autowired
	private IAuthRoleRepository iAuthRoleRepository;

	private AuthRoleDo savedAuthRoleDo;
	private AuthRoleDo copiedAuthRoleDo;

	@Before
	public void setUp() throws Exception {
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("8");
		userInfo.setUserId(0L);
		userInfo.setEmpId(0L);
		SecurityUserUtil.setSecurityUserInfo(userInfo);

		RequestHelper.getRequest()
				.setAttribute("_caidaocloud_userinfo_request_threadlocal_", ObjectConverter.convert(userInfo, UserInfo.class));

// Get the instance of SessionServiceImpl which is already created by Spring bean
		SessionServiceImpl service = (SessionServiceImpl) SpringUtil.getBean(ISessionService.class);
		try {
			// Get the private field "threadLocalCache" from the SessionServiceImpl class
			Field field = SessionServiceImpl.class.getDeclaredField("threadLocalCache");
			// Set the accessibility of the field to true, as it is private
			field.setAccessible(true);
			// Set the value of the "threadLocalCache" field to true
			field.set(service, true);
		}
		catch (NoSuchFieldException | IllegalAccessException e) {
			e.printStackTrace();
		}
	}

	@Test
	public void testSaveOrUpdateRole() {
		// create an AuthRoleDo object and fill all attributes
		AuthRoleDo authRoleDo = new AuthRoleDo();
		authRoleDo.setName("Test Role");
		authRoleDo.setRemark("This is a test role");
		authRoleDo.setRoleType(1); //Fill roleType attribute with value 1
		EnumSet<AdapterDeviceEnum> enums = EnumSet.allOf(AdapterDeviceEnum.class);
		authRoleDo.setDevice(new ArrayList<>(enums));

		// save AuthRoleDo object using repository method and verify it is not null
		Long roleId = iAuthRoleRepository.saveOrUpdateRole(authRoleDo);
		authRoleDo.setId(roleId);
		Option<AuthRoleDo> option = iAuthRoleRepository.getRoleByRoleId(roleId);
		assertFalse(option.isEmpty());
		savedAuthRoleDo = option.get();

		// verify the attributes of savedAuthRoleDo matches the attributes of authRoleDo
		assertEquals(savedAuthRoleDo.getName(), authRoleDo.getName());
		assertEquals(savedAuthRoleDo.getRemark(), authRoleDo.getRemark());
		assertEquals(savedAuthRoleDo.getDevice(), authRoleDo.getDevice());
		assertEquals(savedAuthRoleDo.getRoleType(), authRoleDo.getRoleType()); //Add a check for roleType attribute
		// add all other attribute assertions here

		// Before the test end, call saveOrUpdate to verify update functionality
		authRoleDo.setRemark("This is an updated test role");
		iAuthRoleRepository.saveOrUpdateRole(authRoleDo);
		option = iAuthRoleRepository.getRoleByRoleId(roleId);
		assertFalse(option.isEmpty());
		savedAuthRoleDo = option.get();

		// verify updated attributes of savedAuthRoleDo matches the updated attributes of authRoleDo
		assertEquals(savedAuthRoleDo.getName(), authRoleDo.getName());
		assertEquals(savedAuthRoleDo.getRemark(), authRoleDo.getRemark());
		assertEquals(savedAuthRoleDo.getDevice(), authRoleDo.getDevice());
		assertEquals(savedAuthRoleDo.getRoleType(), authRoleDo.getRoleType()); //Add a check for roleType attribute
		// add all other attribute assertions here

	}

	// Create a new test method to test the copyRole API of IAuthRoleRepository
	@Test
	public void testCopyRole() {
		// create an AuthRoleDo object and fill all attributes
		AuthRoleDo authRoleDo = new AuthRoleDo();
		authRoleDo.setName("Test Copy Role");
		authRoleDo.setRemark("This is a test copy role");
		authRoleDo.setRoleType(1); //Fill roleType attribute with value 1
		EnumSet<AdapterDeviceEnum> enums = EnumSet.allOf(AdapterDeviceEnum.class);
		authRoleDo.setDevice(new ArrayList<>(enums));

		// save AuthRoleDo object using repository method and verify it is not null
		Long roleId = iAuthRoleRepository.saveOrUpdateRole(authRoleDo);
		authRoleDo.setId(roleId);

		// call copyRole API of IAuthRoleRepository and verify it is returning a valid roleId
		Long newRoleId = iAuthRoleRepository.copyRole(roleId);
		assertNotNull(newRoleId);

		// verify that the new role and original role have all the same attributes except for id and create time
		Option<AuthRoleDo> option1 = iAuthRoleRepository.getRoleByRoleId(roleId);
		assertFalse(option1.isEmpty());
		savedAuthRoleDo = option1.get();
		Option<AuthRoleDo> option2 = iAuthRoleRepository.getRoleByRoleId(newRoleId);
		assertFalse(option2.isEmpty());
		copiedAuthRoleDo = option2.get();
		assertTrue(copiedAuthRoleDo.getName().startsWith(savedAuthRoleDo.getName()));
		assertEquals(copiedAuthRoleDo.getRemark(), savedAuthRoleDo.getRemark());
		assertEquals(copiedAuthRoleDo.getDevice(), savedAuthRoleDo.getDevice());
		assertEquals(copiedAuthRoleDo.getRoleType(), savedAuthRoleDo.getRoleType());
		// add all other attribute assertions here

	}

	@Test
	public void testGetMethods() {
		// Test getting all roles with getRoleByRoleId API
		// Option<AuthRoleDo> roleOption = iAuthRoleRepository.getRoleByRoleId(savedAuthRoleDo.getId());
		// assertFalse(roleOption.isEmpty());

		// Test getting all roles with page, size, and roleId params with getPageByRoleId API
		QueryPageBean pageBean = new QueryPageBean();
		PageResult<AuthRoleDo> roleDos = iAuthRoleRepository.getPageOfRole(pageBean);
		assertFalse(roleDos.getItems().isEmpty());
		log.info("auth role page data={}", roleDos);
	}

	@After
	public void cleanup() {
		// After the test, clean up the test data
		// iAuthRoleRepository.deleteRole(Lists.list(savedAuthRoleDo.getId()));
		// iAuthRoleRepository.deleteRole(Lists.list(savedAuthRoleDo.getId(), copiedAuthRoleDo.getId()));
	}

}