package com.caidaocloud.auth.core.config;

import com.caidaocloud.auth.core.scope.AuthScopeContext;
import com.caidaocloud.auth.core.scope.handler.DefaultHandler;
import com.caidaocloud.auth.core.scope.handler.ScopeValueHandler;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 *
 * <AUTHOR>
 * @date 2024/1/8
 */
@Configuration
@EnableFeignClients(basePackages="com.caidaocloud.auth.core.feign")
public class AuthConfig {

	@Bean
	public AuthScopeContext authScopeContext(ScopeValueHandler scopeValueHandler) {
		return new AuthScopeContext(scopeValueHandler);
	}

	@Bean
	@ConditionalOnMissingBean
	public ScopeValueHandler scopeValueHandler(){
		return new DefaultHandler();
	}
}
