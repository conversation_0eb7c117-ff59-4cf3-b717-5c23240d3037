package com.caidaocloud.auth.service.caidao.domain.entity;

import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/17
 **/
@Data
public class AuthRolePermissionDo {

    private Long id;

    private Long roleId;

    private String resourceCode;

    private String parentCode;

    private String dataScopeDetail;

    private ResourceCategoryEnum category;

}
