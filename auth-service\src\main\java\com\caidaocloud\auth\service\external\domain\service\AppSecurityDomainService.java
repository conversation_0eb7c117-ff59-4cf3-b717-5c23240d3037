package com.caidaocloud.auth.service.external.domain.service;

import com.caidaocloud.auth.service.external.domain.entity.AppSecurityEntity;
import com.caidaocloud.auth.service.external.infrastructure.enums.ExternalAuthEnum;
import com.jarvis.cache.annotation.Cache;
import com.jarvis.cache.annotation.CacheDelete;
import com.jarvis.cache.annotation.CacheDeleteKey;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/1/11
 **/
@Service
public class AppSecurityDomainService {
    @Cache(key = "'external-app-key-' + #args[0] + #args[1]", expire = 60)
    public AppSecurityEntity getAppSecurity(String tenantId, ExternalAuthEnum type) {
        Optional<AppSecurityEntity> optional = AppSecurityEntity.getEntity(type);
        if (!optional.isPresent()) {
            return new AppSecurityEntity();
        }
        return optional.get();
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheDelete({@CacheDeleteKey(value = "'external-app-key-' + #args[0].tenantId + #args[0].type")})
    public void saveOrUpdateAppSecurity(AppSecurityEntity entity) {
        if (entity == null) {
            return;
        }
        entity.saveOrUpdate();
    }
}
