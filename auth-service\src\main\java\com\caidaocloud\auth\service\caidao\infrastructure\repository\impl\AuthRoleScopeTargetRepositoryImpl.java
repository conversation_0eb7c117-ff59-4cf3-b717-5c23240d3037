package com.caidaocloud.auth.service.caidao.infrastructure.repository.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.auth.core.dto.AuthRoleScopeTargetDetail;
import com.caidaocloud.auth.core.enums.AuthRoleScopeTarget;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleScopeTargetDo;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthRoleScopeTargetRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.AuthRoleScopeTargetDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.AuthRoleScopeTargetDetailDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthRoleScopeTargetDetailPo;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthRoleScopeTargetPo;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.googlecode.totallylazy.collections.ReadOnlyList;

import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/8/13
 */
@Repository
public class AuthRoleScopeTargetRepositoryImpl implements IAuthRoleScopeTargetRepository {
	@Resource
	private AuthRoleScopeTargetDao authRoleScopeTargetDao;
	@Resource
	private AuthRoleScopeTargetDetailDao authRoleScopeTargetDetailDao;

	@Override
	public void save(AuthRoleScopeTargetDo authRoleScopeTargetDo) {
		LambdaQueryWrapper<AuthRoleScopeTargetPo> queryWrapper = new QueryWrapper<AuthRoleScopeTargetPo>().lambda();
		queryWrapper.eq(AuthRoleScopeTargetPo::getCode, authRoleScopeTargetDo.getCode());
		AuthRoleScopeTargetPo exist = authRoleScopeTargetDao.selectOne(queryWrapper);
		AuthRoleScopeTargetPo po = ObjectConverter.convert(authRoleScopeTargetDo, AuthRoleScopeTargetPo.class);
		if (exist != null) {
			BeanUtil.copyWithNoValue(po, exist);
			authRoleScopeTargetDao.updateById(exist);
			po.setId(exist.getId());
		}
		else {
			po.setId(SnowUtil.createId());
			po.setCreateTime(System.currentTimeMillis());
			po.setCreateBy(String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
			authRoleScopeTargetDao.insert(po);
		}
		saveDetail(po.getId(), authRoleScopeTargetDo.getDetails());
	}

	private void saveDetail(Long id, List<AuthRoleScopeTargetDetail> details) {
		LambdaQueryWrapper<AuthRoleScopeTargetDetailPo> queryWrapper = new QueryWrapper<AuthRoleScopeTargetDetailPo>().lambda();
		queryWrapper.eq(AuthRoleScopeTargetDetailPo::getTargetId, id);
		authRoleScopeTargetDetailDao.delete(queryWrapper);
		details.forEach(detail -> {
			AuthRoleScopeTargetDetailPo po = ObjectConverter.convert(detail, AuthRoleScopeTargetDetailPo.class);
			po.setId(SnowUtil.createId());
			po.setTargetId(id);
			authRoleScopeTargetDetailDao.insert(po);
		});
	}

	@Override
	public List<AuthRoleScopeTargetDo> loadAll() {
		List<AuthRoleScopeTargetPo> allTarget = authRoleScopeTargetDao.selectList(new QueryWrapper<>());
		List<AuthRoleScopeTargetDetailPo> allTargetDetail = authRoleScopeTargetDetailDao.selectList(new QueryWrapper<>());

		List<AuthRoleScopeTargetDo> result = new ArrayList<>();
		Map<Long, AuthRoleScopeTargetDo> targetDoMap = new HashMap<>();

		for (AuthRoleScopeTargetPo po : allTarget) {
			AuthRoleScopeTargetDo targetDo = ObjectConverter.convert(po, AuthRoleScopeTargetDo.class);
			targetDo.setDetails(new ArrayList<>());
			targetDoMap.put(po.getId(), targetDo);
			result.add(targetDo);
		}

		for (AuthRoleScopeTargetDetailPo detailPo : allTargetDetail) {
			AuthRoleScopeTargetDo doObj = targetDoMap.get(detailPo.getTargetId());
			if (doObj != null) {
				AuthRoleScopeTargetDetail detail = ObjectConverter.convert(detailPo, AuthRoleScopeTargetDetail.class);
				doObj.getDetails().add(detail);
			}
		}

		return result;
	}
}
