package com.caidaocloud.auth.service.caidao.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleAndRuleDo;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleDo;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRolePermissionDo;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthRoleRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.SystemRoleEnum;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.AuthRoleDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.AuthRolePermissionDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthRoleAndPermissionPo;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthRoleAndRulePo;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthRolePermissionPo;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthRolePo;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.service.impl.AuthRolePermissionService;
import com.caidaocloud.auth.service.caidao.infrastructure.util.TenantSelectPageUtil;
import com.caidaocloud.auth.service.caidao.infrastructure.util.UserContextUtil;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/7
 **/
@Repository
@Slf4j
public class AuthRoleRepositoryImpl implements IAuthRoleRepository {
    @Autowired
    private AuthRoleDao authRoleDao;
    @Autowired
    private AuthRolePermissionDao authRolePermissionDao;
    @Autowired
    private AuthRolePermissionService authRolePermissionService;
    @Autowired
    private ISessionService sessionService;

    /**
     * 保存或更新角色
     *
     * @param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveOrUpdateRole(AuthRoleDo authRoleDo) {
        var authRolePo = FastjsonUtil.convertObject(authRoleDo, AuthRolePo.class);
        var currentTimeMillis = System.currentTimeMillis();
        val userInfo = UserContextUtil.preCheckUser();
        if (userInfo == null) {
            throw new ServerException("not found user info");
        }
        val empId = userInfo.getStaffId() == null ? "0" : userInfo.getStaffId().toString();
        if (authRolePo.getId() == null) {
            authRolePo.setCreateTime(currentTimeMillis);
            authRolePo.setCreateBy(empId);
            authRolePo.setTenantId(userInfo.getTenantId());
            authRolePo.setDeleted(false);
            authRoleDao.insert(authRolePo);
        } else {

            authRolePo.setUpdateTime(currentTimeMillis);
            authRolePo.setUpdateBy(empId);
            authRoleDao.updateById(authRolePo);
        }

        // config角色不能保存业务权限
        if (!Objects.equals(authRolePo.getCode(), SystemRoleEnum.CONFIG.code) && authRoleDo.getAuthRolePermissionList() != null && !CollectionUtils.isEmpty(authRoleDo.getAuthRolePermissionList())) {
            var newPermissionPoList = FastjsonUtil.convertList(authRoleDo.getAuthRolePermissionList(), AuthRolePermissionPo.class);
            var newPermissionIdSet = Sequences.sequence(newPermissionPoList).map(e -> e.getResourceCode()).toSet();

            var queryWrapper = new LambdaQueryWrapper<AuthRolePermissionPo>()
                    .eq(AuthRolePermissionPo::getRoleId, authRolePo.getId())
                    .eq(AuthRolePermissionPo::getDeleted, 0)
                    .eq(AuthRolePermissionPo::getTenantId, userInfo.getTenantId());
            var oldPermissionPoList = authRolePermissionDao.selectList(queryWrapper);
            if (!CollectionUtils.isEmpty(oldPermissionPoList)) {
                var sequence = Sequences.sequence(oldPermissionPoList)
                        .filter(e -> !newPermissionIdSet.contains(e.getResourceCode())).map(e -> e.getId());
                if (!sequence.isEmpty()) {
                    authRolePermissionDao.deleteBatchIds(sequence.toList());
                }
            }
            List<AuthRolePermissionPo> addList = Lists.newArrayList();
            List<AuthRolePermissionPo> updateList = Lists.newArrayList();
            for (AuthRolePermissionPo authRolePermissionPo : newPermissionPoList) {
                if (authRolePermissionPo.getId() == null) {
                    authRolePermissionPo.setCreateTime(currentTimeMillis);
                    authRolePermissionPo.setCreateBy(empId);
                    authRolePermissionPo.setRoleId(authRolePo.getId());
                    authRolePermissionPo.setTenantId(userInfo.getTenantId());
                    authRolePermissionPo.setDeleted(false);
                    addList.add(authRolePermissionPo);
                }
                else {
                    authRolePermissionPo.setUpdateTime(currentTimeMillis);
                    authRolePermissionPo.setUpdateBy(empId);
                    updateList.add(authRolePermissionPo);
                }
            }
            if (!CollectionUtils.isEmpty(addList)) {
                authRolePermissionService.saveBatch(addList);
            }
            if (!CollectionUtils.isEmpty(updateList)) {
                authRolePermissionService.updateBatchById(updateList);
            }
        }
        else {
            if (authRolePo.getId() != null) {
                var queryWrapper = new LambdaQueryWrapper<AuthRolePermissionPo>()
                        .eq(AuthRolePermissionPo::getRoleId, authRolePo.getId());
                authRolePermissionDao.delete(queryWrapper);
            }
        }
        return authRolePo.getId();
    }

    /**
     * 删除角色
     *
     * @param roleIdList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteRole(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            throw new ServerException("roleIdList is null");
        }
        var queryWrapper = new LambdaQueryWrapper<AuthRolePo>()
                .in(true, AuthRolePo::getId, roleIdList).eq(AuthRolePo::getDeleted, 0);
        List<AuthRolePo> authRolePos = authRoleDao.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(authRolePos)) {
            boolean empty = Sequences.sequence(authRolePos).filter(e -> e.getRoleType() == 0).isEmpty();
            if (!empty) {
                throw new ServerException("you don't delete system role");
            }
        }

        authRoleDao.deleteBatchIds(roleIdList);
        var queryWrapper2 = new LambdaQueryWrapper<AuthRolePermissionPo>()
                .in(true, AuthRolePermissionPo::getRoleId, roleIdList).eq(AuthRolePermissionPo::getDeleted, 0);
        authRolePermissionDao.delete(queryWrapper2);
    }

    /**
     * 复制角色
     *
     * @param roleId
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long copyRole(Long roleId) {
        if (roleId == null) {
            throw new ServerException("roleId is null");
        }
        var authRolePo = authRoleDao.selectById(roleId);
        if (authRolePo == null) {
            throw new ServerException("not found role");
        }
        val userInfo = sessionService.getUserInfo();
        if (userInfo == null) {
            throw new ServerException("not found this user information");
        }
        var currentTimeMillis = System.currentTimeMillis();
        val empId = userInfo.getStaffId() == null ? "0" : userInfo.getStaffId().toString();
        var copyRole = new AuthRolePo();
        BeanUtils.copyProperties(authRolePo, copyRole, "id", "createBy", "createTime", "updateTime", "updateBy", "deleted");
        for (int i = 1; i < 80; i++) {
            String name = StringUtils.trimToEmpty(copyRole.getName()) + "(copy" + i + ")";
            if (!isExistByRoleName(name)) {
                copyRole.setName(name);
                break;
            }
        }
        copyRole.setCreateTime(currentTimeMillis);
        copyRole.setUpdateTime(currentTimeMillis);
        copyRole.setCreateBy(empId);
        copyRole.setUpdateBy(empId);
        copyRole.setDeleted(false);
        copyRole.setCode(null);
        copyRole.setRoleType(1);
        authRoleDao.insert(copyRole);

        var queryWrapper = new LambdaQueryWrapper<AuthRolePermissionPo>();
        queryWrapper.eq(true, AuthRolePermissionPo::getRoleId, roleId).eq(AuthRolePermissionPo::getDeleted, 0);
        var permissionPoList = authRolePermissionDao.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(permissionPoList)) {
            ArrayList<AuthRolePermissionPo> list = Lists.newArrayList();
            for (AuthRolePermissionPo authRolePermissionPo : permissionPoList) {
                authRolePermissionPo.setId(null);
                authRolePermissionPo.setRoleId(copyRole.getId());
                authRolePermissionPo.setCreateTime(currentTimeMillis);
                authRolePermissionPo.setUpdateTime(currentTimeMillis);
                authRolePermissionPo.setCreateBy(empId);
                authRolePermissionPo.setUpdateBy(empId);
                list.add(authRolePermissionPo);
            }
            authRolePermissionDao.insertBatchSomeColumn(list);
        }
        return copyRole.getId();
    }

    @Override
    public boolean isExistByRoleName(String roleName) {
        var queryWrapper = new LambdaQueryWrapper<AuthRolePo>()
                .eq(AuthRolePo::getName, roleName);
        return authRoleDao.exists(queryWrapper);
    }

    @Override
    public boolean isExistByRoleName(String roleName, Long excludeId) {
        var queryWrapper = new LambdaQueryWrapper<AuthRolePo>()
                .eq(AuthRolePo::getName, roleName).ne(AuthRolePo::getId, excludeId);
        return authRoleDao.exists(queryWrapper);
    }

    /**
     * 角色分页
     *
     * @param queryPageBean
     * @return
     */
    @Override
    public PageResult<AuthRoleDo> getPageOfRole(QueryPageBean queryPageBean) {
        var page = new Page<AuthRolePo>(queryPageBean.getPageNo(), queryPageBean.getPageSize());
        var queryWrapper = new LambdaQueryWrapper<AuthRolePo>();
        if (StringUtils.isNotBlank(queryPageBean.getKeywords())) {
            queryWrapper.like(AuthRolePo::getName, queryPageBean.getKeywords());
        }
        queryWrapper.eq(true, AuthRolePo::getDeleted, 0);
        Page<AuthRolePo> pageResult = TenantSelectPageUtil.selectPage(authRoleDao, page, queryWrapper);
        return new PageResult<AuthRoleDo>(Sequences.sequence(pageResult.getRecords()).map(e -> e.toEntity()).toList(),
                (int) pageResult.getCurrent(), (int) pageResult.getSize(), (int) pageResult.getTotal());
    }

    @Override
    public List<AuthRoleDo> selectAuthRoleByCode(List<String> codeList) {
        if (CollectionUtils.isEmpty(codeList)) {
            throw new ServerException("parameter is null");
        }
        var queryWrapper = new LambdaQueryWrapper<AuthRolePo>()
                .in(AuthRolePo::getCode, codeList).eq(AuthRolePo::getDeleted, 0);
        List<AuthRolePo> authRolePos = authRoleDao.selectList(queryWrapper);
        return Sequences.sequence(authRolePos).map(e -> e.toEntity()).toList();
    }

    @Override
    public List<String> getResourceCodeListByRoleIdList(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Lists.newArrayList();
        }
        val wrapper = new LambdaQueryWrapper<AuthRolePermissionPo>()
                .in(AuthRolePermissionPo::getRoleId, roleIdList).eq(AuthRolePermissionPo::getDeleted, false);
        return authRolePermissionDao.selectList(wrapper).stream()
                .map(it -> it.getResourceCode()).collect(Collectors.toList());
    }

    @Override
    public Map<String, String> getResourceScopeDetailBySubjectId(List<Long> roleIdList, String parentCode) {
        val wrapper = new LambdaQueryWrapper<AuthRolePermissionPo>()
                .in(AuthRolePermissionPo::getRoleId, roleIdList).eq(AuthRolePermissionPo::getDeleted, false);
        Map<String, String> result = Maps.newHashMap();
        authRolePermissionDao.selectList(wrapper).stream()
                .filter(it -> parentCode.equals(it.getParentCode()))
                .forEach(it -> result.put(it.getResourceCode(), it.getDataScopeDetail()));
        return result;
    }

    @Override
    public List<String> getResourceCodeByRolesAndParentCode(List<Long> roleIdList, String parentCode) {
        var wrapper = new LambdaQueryWrapper<AuthRolePermissionPo>()
                .select(AuthRolePermissionPo::getResourceCode, AuthRolePermissionPo::getParentCode)
                .in(AuthRolePermissionPo::getRoleId, roleIdList)
                .eq(AuthRolePermissionPo::getDeleted, false);
        var permissionList = authRolePermissionDao.selectList(wrapper);
        if (CollectionUtils.isEmpty(permissionList)) {
            return Lists.newArrayList();
        }
        Sequence<AuthRolePermissionPo> sequence = Sequences.sequence(permissionList)
                .unique(AuthRolePermissionPo::getResourceCode);
        if (StringUtils.isNotBlank(parentCode)) {
            sequence = sequence.filter(e -> parentCode.equals(e.getParentCode()));
        }
        return sequence.map(e -> e.getResourceCode()).toList();
    }

    @Override
    public List<String> getResourceUrlListByRoleIdList(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Lists.newArrayList();
        }
        List<String> urlList = authRolePermissionDao.selectResourceUrlByRoleIds(roleIdList);
        return Sequences.sequence(urlList).stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<AuthRolePermissionDo> getPermissionOfRole(Long roleId) {
        if (roleId == null) {
            throw new ServerException("parameter is null");
        }
        var queryWrapper = new LambdaQueryWrapper<AuthRolePermissionPo>();
        queryWrapper.eq(true, AuthRolePermissionPo::getRoleId, roleId).eq(AuthRolePermissionPo::getDeleted, 0);
        var permissionPoList = authRolePermissionDao.selectList(queryWrapper);
        return Sequences.sequence(permissionPoList).map(e -> FastjsonUtil.convertObject(e, AuthRolePermissionDo.class)).toList();
    }

    @Override
    public List<AuthRoleDo> getRoleIdAndNameByRoleName(List<String> roleNameList) {
        if (CollectionUtils.isEmpty(roleNameList)) {
            return Lists.newArrayList();
        }
        var queryWrapper = new LambdaQueryWrapper<AuthRolePo>()
                .select(AuthRolePo::getId, AuthRolePo::getName, AuthRolePo::getCode, AuthRolePo::getRoleType)
                .in(AuthRolePo::getName, roleNameList);
        List<AuthRolePo> authRolePoList = authRoleDao.selectList(queryWrapper);
        return FastjsonUtil.convertList(authRolePoList, AuthRoleDo.class);
    }

    @Override
    public Option<AuthRoleDo> getRoleByRoleId(Long roleId) {
        if (roleId == null) {
            throw new ServerException("roleId is null");
        }
        List<AuthRoleAndPermissionPo> resultList = authRoleDao.getRoleById(roleId);
        Sequence<AuthRoleAndPermissionPo> sequence = Sequences.sequence(resultList);
        if (sequence.isEmpty()) {
            return Option.none();
        }
        AuthRoleDo authRoleDo = sequence.unique(AuthRoleAndPermissionPo::getId).map(e -> e.toEntity()).get(0);
        sequence.forEach(it -> it.setId(it.getPermissonId()));
        List<AuthRolePermissionDo> permissionDoList = FastjsonUtil.convertList(sequence.toList(), AuthRolePermissionDo.class);
        authRoleDo.setAuthRolePermissionList(permissionDoList);
        return Option.some(authRoleDo);
    }

    @Override
    public List<AuthRoleAndRuleDo> getRoleByRuleIds(List<Long> ruleIds) {
        if (CollectionUtils.isEmpty(ruleIds)) {
            return Lists.newArrayList();
        }
        List<AuthRoleAndRulePo> roleAndRuleList = authRoleDao.getRoleByRuleIds(ruleIds);
        return Sequences.sequence(roleAndRuleList).map(e -> {
            var entity = new AuthRoleAndRuleDo();
            BeanUtils.copyProperties(e, entity);
            return entity;
        }).toList();
    }

    @Override
    public Sequence<AuthRoleDo> getAllRole() {
        var queryWrapper = new LambdaQueryWrapper<AuthRolePo>()
                .eq(AuthRolePo::getDeleted, 0);
        List<AuthRolePo> authRolePoList = authRoleDao.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(authRolePoList)) {
            return Sequences.empty();
        }
        return Sequences.sequence(authRolePoList).map(e -> e.toEntity());
    }

    @Override
    public void deleteRolePermission(String code) {
        if (StringUtils.isBlank(code)) {
            throw new ServerException("code is empty");
        }
        var queryWrapper = new LambdaQueryWrapper<AuthRolePermissionPo>()
                .eq(AuthRolePermissionPo::getResourceCode, code).eq(AuthRolePermissionPo::getDeleted, 0);
        authRolePermissionDao.delete(queryWrapper);
        var queryWrapper1 = new LambdaQueryWrapper<AuthRolePermissionPo>()
                .eq(AuthRolePermissionPo::getParentCode, code).eq(AuthRolePermissionPo::getDeleted, 0);
        authRolePermissionDao.delete(queryWrapper1);
    }
}