package com.caidaocloud.auth.service.service.mongo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.AuthGroupRuleSubjectDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthGroupRuleSubjectPo;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.val;
import lombok.var;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;

@RunWith(SpringRunner.class)
@SpringBootTest
public class SubjectTest {

    @Autowired
    private AuthGroupRuleSubjectDao authGroupRuleSubjectDao;

    @Test
    public void test() {
        long currentTimeMillis = System.currentTimeMillis();
        val empId = "0";
        ArrayList<Long> subjectIdList = Lists.newArrayList(1528682957185757186L);
        var queryWrapper = new LambdaQueryWrapper<AuthGroupRuleSubjectPo>()
                .in(AuthGroupRuleSubjectPo::getSubjectId, subjectIdList)
                .eq(AuthGroupRuleSubjectPo::getRuleId, 1)
                .eq(AuthGroupRuleSubjectPo::getDeleted, 0);
        var authGroupRuleSubjectList = authGroupRuleSubjectDao.selectList(queryWrapper);
        var updateSequence = Sequences.sequence(authGroupRuleSubjectList)
                .filter(e -> subjectIdList.contains(e.getSubjectId()));
        if (!updateSequence.isEmpty()) {
            var queryWrapper2 = new LambdaQueryWrapper<AuthGroupRuleSubjectPo>()
                    .in(AuthGroupRuleSubjectPo::getId, updateSequence.map(AuthGroupRuleSubjectPo::getId).toList());
            var authGroupRuleSubjectPo = new AuthGroupRuleSubjectPo();
            authGroupRuleSubjectPo.setUpdateTime(currentTimeMillis);
            authGroupRuleSubjectPo.setUpdateBy(empId);
            authGroupRuleSubjectDao.update(authGroupRuleSubjectPo, queryWrapper2);
        }
    }

}
