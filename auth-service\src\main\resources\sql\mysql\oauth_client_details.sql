CREATE TABLE IF NOT EXISTS oauth_client_details
(
    id                     BIGINT       NOT NULL AUTO_INCREMENT COMMENT '唯一标识',
    client_id              VARCHAR(50)  NOT NULL,
    resource_ids           VARCHAR(100) ,
    client_secret          VARCHAR(50)  not null,
    scope                  VARCHAR(50)  NOT NULL,
    authorized_grant_types VARCHAR(100) NOT NULL,
    web_server_redirect_uri VARCHAR(100) ,
    authorities            varchar(50)  ,
    access_token_validity  int         DEFAULT NULL,
    refresh_token_validity int         DEFAULT NULL,
    additional_information text        DEFAULT NULL,
    autoapprove            varchar(50) DEFAULT NULL,
    tenant_id              varchar(50)  NOT NULL,
    belong_id              bigint      DEFAULT NULL,
    status                 int         DEFAULT NULL,
    PRIMARY KEY (id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

INSERT INTO `oauth_client_details` ( client_id, resource_ids, client_secret, scope, authorized_grant_types, web_server_redirect_uri, authorities, access_token_validity, refresh_token_validity, additional_information, autoapprove, tenant_id, belong_id,`status` )
VALUES
    ( '98bfcdca-3741-47d4-aeb3-6fbc4913f160', NULL, 'b9137006-e5f3-4e1d-a3dd-4af76a14a2c8', 'all', 'client_credentials,authorization_code,refresh_token', NULL, NULL, 3600, 120, NULL, 'true', 0, 0,0 );