package com.caidaocloud.auth.service.caidao.application.dto.subject;

import com.caidaocloud.dto.QueryPageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/11
 **/
@Data
@ApiModel(value = "角色组查看用户分页dto")
public class AuthGroupRuleSubjectPageDto {

    @ApiModelProperty("角色组id")
    private Long ruleId;

    @ApiModelProperty("分页对象")
    private QueryPageBean queryPageBean;

}
