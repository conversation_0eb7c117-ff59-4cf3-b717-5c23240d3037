package com.caidaocloud.auth.service.common.infrastructure.config.mybatis.typehandler;

import com.caidaocloud.auth.service.caidao.infrastructure.entity.ConditionTree;
import com.caidaocloud.auth.service.common.infrastructure.config.mybatis.type.JsonSerializeInterface;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.FastjsonUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * json类型处理
 *
 * <AUTHOR>
 * @date 2022/5/10
 **/
@MappedTypes({ConditionTree.class})
public class JsonTypeHandler<T extends JsonSerializeInterface> extends BaseTypeHandler<T> {

    private final Class<T> clazz;

    public JsonTypeHandler(Class<T> clazz) {
        if (clazz == null) {
            throw new ServerException("parameter is null");
        }
        this.clazz = clazz;
    }

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, T t, JdbcType jdbcType) throws SQLException {
        preparedStatement.setObject(i, FastjsonUtil.toJson(t));
    }

    @Override
    public T getNullableResult(ResultSet resultSet, String s) throws SQLException {
        Object object = resultSet.getObject(s);
        if (object == null) {
            return null;
        }
        return FastjsonUtil.toObject(object.toString(), clazz);
    }

    @Override
    public T getNullableResult(ResultSet resultSet, int i) throws SQLException {
        return null;
    }

    @Override
    public T getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        return null;
    }

}
