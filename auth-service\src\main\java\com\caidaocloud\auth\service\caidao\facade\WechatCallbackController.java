package com.caidaocloud.auth.service.caidao.facade;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 *
 * <AUTHOR>
 * @date 2024/11/22
 */
@RequestMapping("/api/auth/v1/wechat/callback")
public class WechatCallbackController {
	@GetMapping("data")
	public String dataCallback() {
		return "success";
	}

	@GetMapping("command")
	public String commandCallback() {
		return "success";
	}
}
