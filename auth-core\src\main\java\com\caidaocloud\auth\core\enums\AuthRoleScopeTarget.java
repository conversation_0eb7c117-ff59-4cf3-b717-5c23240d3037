package com.caidaocloud.auth.core.enums;

import com.caidaocloud.auth.core.dto.AuthRoleScopeTargetDetail;
import com.googlecode.totallylazy.Lists;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

import static com.caidaocloud.auth.core.enums.AuthRoleScopeRestriction.*;

@Getter
public enum AuthRoleScopeTarget {

    ORG(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.Org", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.hr.Org", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.hr.Org", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.hr.Org", "bid"),
            new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "entity.hr.Org", "bid"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG, "entity.hr.Org", "bid"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG_AND_BELONGINGS, "entity.hr.Org", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.hr.Org", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "entity.hr.Org", "bid"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.Org", "tenantId")
    )),
    POST(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.Post", "orgId", true),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.hr.Post", "orgId", true),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.hr.Post", "orgId", true),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.hr.Post", "orgId", true),
            new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "entity.hr.Post", "orgId", true),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG, "entity.hr.Post", "orgId", true),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG_AND_BELONGINGS, "entity.hr.Post", "orgId", true),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.hr.Post", "orgId", true),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "entity.hr.Post", "orgId", true),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.Post", "tenantId")
    )),

    COMPANY(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.hr.Company", "bid"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.Company", "tenantId")
    )),
    WORKPLACE(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.hr.Workplace", "companyId"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.Workplace", "tenantId")
    )),
    EMP(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.hr.EMP_SUB_WORK", "company"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_WORK", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_WORK", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.hr.EMP_SUB_WORK", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.hr.EMP_SUB_WORK", "organize"),
            new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "entity.hr.EMP_SUB_WORK", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG, "entity.hr.EMP_SUB_WORK", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_WORK", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.hr.EMP_SUB_WORK", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "entity.hr.EMP_SUB_WORK", "organize"),
new AuthRoleScopeTargetDetail(MY_LEADER_AND_SUB, "entity.hr.EMP_SUB_WORK", "organize"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE, "entity.hr.EMP_SUB_WORK", "empId"),
new AuthRoleScopeTargetDetail(ALL_SUBORDINATE, "entity.hr.EMP_SUB_WORK", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "entity.hr.EMP_SUB_WORK", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER_WITH_CONCURRENT, "entity.hr.EMP_SUB_WORK", "empId"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE_WITH_CONCURRENT, "entity.hr.EMP_SUB_WORK", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "entity.hr.EMP_SUB_WORK", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.hr.EMP_SUB_WORK", "workplace"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "entity.hr.EMP_SUB_WORK", "empType$dictValue"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.EMP_SUB_WORK", "tenantId"),
            new AuthRoleScopeTargetDetail(SELECTED_POST, "entity.hr.EMP_SUB_WORK", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_BENCH_POST, "entity.hr.EMP_SUB_WORK", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_COST_CENTER, "entity.hr.EMP_SUB_WORK", "empId"),
            new AuthRoleScopeTargetDetail(MYSELF,"entity.hr.EMP_SUB_WORK","empId")
    )),

    EMP_SUB_PRIVATE(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.hr.EMP_SUB_PRIVATE", "company"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_PRIVATE", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_PRIVATE", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.hr.EMP_SUB_PRIVATE", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.hr.EMP_SUB_PRIVATE", "organize"),
            new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "entity.hr.EMP_SUB_PRIVATE", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG, "entity.hr.EMP_SUB_PRIVATE", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_PRIVATE", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.hr.EMP_SUB_PRIVATE", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "entity.hr.EMP_SUB_PRIVATE", "organize"),
new AuthRoleScopeTargetDetail(MY_LEADER_AND_SUB, "entity.hr.EMP_SUB_PRIVATE", "organize"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE, "entity.hr.EMP_SUB_PRIVATE", "empId"),
new AuthRoleScopeTargetDetail(ALL_SUBORDINATE, "entity.hr.EMP_SUB_PRIVATE", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "entity.hr.EMP_SUB_PRIVATE", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER_WITH_CONCURRENT, "entity.hr.EMP_SUB_PRIVATE", "empId"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE_WITH_CONCURRENT, "entity.hr.EMP_SUB_PRIVATE", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "entity.hr.EMP_SUB_PRIVATE", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.hr.EMP_SUB_PRIVATE", "workplace"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "entity.hr.EMP_SUB_PRIVATE", "empType$dictValue"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.EMP_SUB_PRIVATE", "tenantId")
    )),

    EMP_SUB_OTHER_ORG(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.hr.EMP_SUB_OTHER_ORG", "company"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_OTHER_ORG", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_OTHER_ORG", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.hr.EMP_SUB_OTHER_ORG", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.hr.EMP_SUB_OTHER_ORG", "organize"),
            new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "entity.hr.EMP_SUB_OTHER_ORG", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG, "entity.hr.EMP_SUB_OTHER_ORG", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_OTHER_ORG", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.hr.EMP_SUB_OTHER_ORG", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "entity.hr.EMP_SUB_OTHER_ORG", "organize"),
new AuthRoleScopeTargetDetail(MY_LEADER_AND_SUB, "entity.hr.EMP_SUB_OTHER_ORG", "organize"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE, "entity.hr.EMP_SUB_OTHER_ORG", "empId"),
new AuthRoleScopeTargetDetail(ALL_SUBORDINATE, "entity.hr.EMP_SUB_OTHER_ORG", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "entity.hr.EMP_SUB_OTHER_ORG", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER_WITH_CONCURRENT, "entity.hr.EMP_SUB_OTHER_ORG", "empId"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE_WITH_CONCURRENT, "entity.hr.EMP_SUB_OTHER_ORG", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "entity.hr.EMP_SUB_OTHER_ORG", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.hr.EMP_SUB_OTHER_ORG", "workplace"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "entity.hr.EMP_SUB_OTHER_ORG", "empType$dictValue"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.EMP_SUB_OTHER_ORG", "tenantId")
    )),

    EMP_SUB_CONTRACT(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.hr.EMP_SUB_CONTRACT", "company"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_CONTRACT", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_CONTRACT", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.hr.EMP_SUB_CONTRACT", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.hr.EMP_SUB_CONTRACT", "organize"),
            new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "entity.hr.EMP_SUB_CONTRACT", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG, "entity.hr.EMP_SUB_CONTRACT", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_CONTRACT", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.hr.EMP_SUB_CONTRACT", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "entity.hr.EMP_SUB_CONTRACT", "organize"),
new AuthRoleScopeTargetDetail(MY_LEADER_AND_SUB, "entity.hr.EMP_SUB_CONTRACT", "organize"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE, "entity.hr.EMP_SUB_CONTRACT", "empId"),
new AuthRoleScopeTargetDetail(ALL_SUBORDINATE, "entity.hr.EMP_SUB_CONTRACT", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "entity.hr.EMP_SUB_CONTRACT", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER_WITH_CONCURRENT, "entity.hr.EMP_SUB_CONTRACT", "empId"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE_WITH_CONCURRENT, "entity.hr.EMP_SUB_CONTRACT", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "entity.hr.EMP_SUB_CONTRACT", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.hr.EMP_SUB_CONTRACT", "workplace"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "entity.hr.EMP_SUB_CONTRACT", "empType$dictValue"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.EMP_SUB_CONTRACT", "tenantId")
    )),

    EMP_SUB_WORK_OVERVIEW(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.hr.EMP_SUB_WORK_OVERVIEW", "company"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_WORK_OVERVIEW", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_WORK_OVERVIEW", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.hr.EMP_SUB_WORK_OVERVIEW", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.hr.EMP_SUB_WORK_OVERVIEW", "organize"),
            new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "entity.hr.EMP_SUB_WORK_OVERVIEW", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG, "entity.hr.EMP_SUB_WORK_OVERVIEW", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_WORK_OVERVIEW", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.hr.EMP_SUB_WORK_OVERVIEW", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "entity.hr.EMP_SUB_WORK_OVERVIEW", "organize"),
            new AuthRoleScopeTargetDetail(MY_LEADER_AND_SUB, "entity.hr.EMP_SUB_WORK_OVERVIEW", "organize"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE, "entity.hr.EMP_SUB_WORK_OVERVIEW", "empId"),
new AuthRoleScopeTargetDetail(ALL_SUBORDINATE, "entity.hr.EMP_SUB_WORK_OVERVIEW", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "entity.hr.EMP_SUB_WORK_OVERVIEW", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER_WITH_CONCURRENT, "entity.hr.EMP_SUB_WORK_OVERVIEW", "empId"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE_WITH_CONCURRENT, "entity.hr.EMP_SUB_WORK_OVERVIEW", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "entity.hr.EMP_SUB_WORK_OVERVIEW", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.hr.EMP_SUB_WORK_OVERVIEW", "workplace"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "entity.hr.EMP_SUB_WORK_OVERVIEW", "empType$dictValue"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.EMP_SUB_WORK_OVERVIEW", "tenantId")
    )),

    EMP_SUB_EDU(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.hr.EMP_SUB_EDU", "company"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_EDU", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_EDU", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.hr.EMP_SUB_EDU", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.hr.EMP_SUB_EDU", "organize"),
            new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "entity.hr.EMP_SUB_EDU", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG, "entity.hr.EMP_SUB_EDU", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_EDU", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.hr.EMP_SUB_EDU", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "entity.hr.EMP_SUB_EDU", "organize"),
new AuthRoleScopeTargetDetail(MY_LEADER_AND_SUB, "entity.hr.EMP_SUB_EDU", "organize"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE, "entity.hr.EMP_SUB_EDU", "empId"),
new AuthRoleScopeTargetDetail(ALL_SUBORDINATE, "entity.hr.EMP_SUB_EDU", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "entity.hr.EMP_SUB_EDU", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER_WITH_CONCURRENT, "entity.hr.EMP_SUB_EDU", "empId"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE_WITH_CONCURRENT, "entity.hr.EMP_SUB_EDU", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "entity.hr.EMP_SUB_EDU", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.hr.EMP_SUB_EDU", "workplace"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "entity.hr.EMP_SUB_EDU", "empType$dictValue"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.EMP_SUB_EDU", "tenantId")
    )),

    EMP_SUB_FAMILY(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.hr.EMP_SUB_FAMILY", "company"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_FAMILY", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_FAMILY", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.hr.EMP_SUB_FAMILY", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.hr.EMP_SUB_FAMILY", "organize"),
            new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "entity.hr.EMP_SUB_FAMILY", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG, "entity.hr.EMP_SUB_FAMILY", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_FAMILY", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.hr.EMP_SUB_FAMILY", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "entity.hr.EMP_SUB_FAMILY", "organize"),
new AuthRoleScopeTargetDetail(MY_LEADER_AND_SUB, "entity.hr.EMP_SUB_FAMILY", "organize"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE, "entity.hr.EMP_SUB_FAMILY", "empId"),
new AuthRoleScopeTargetDetail(ALL_SUBORDINATE, "entity.hr.EMP_SUB_FAMILY", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "entity.hr.EMP_SUB_FAMILY", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER_WITH_CONCURRENT, "entity.hr.EMP_SUB_FAMILY", "empId"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE_WITH_CONCURRENT, "entity.hr.EMP_SUB_FAMILY", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "entity.hr.EMP_SUB_FAMILY", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.hr.EMP_SUB_FAMILY", "workplace"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "entity.hr.EMP_SUB_FAMILY", "empType$dictValue"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.EMP_SUB_FAMILY", "tenantId")
    )),

    EMP_SUB_REWARD(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.hr.EMP_SUB_REWARD", "company"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_REWARD", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_REWARD", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.hr.EMP_SUB_REWARD", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.hr.EMP_SUB_REWARD", "organize"),
            new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "entity.hr.EMP_SUB_REWARD", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG, "entity.hr.EMP_SUB_REWARD", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_REWARD", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.hr.EMP_SUB_REWARD", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "entity.hr.EMP_SUB_REWARD", "organize"),
new AuthRoleScopeTargetDetail(MY_LEADER_AND_SUB, "entity.hr.EMP_SUB_REWARD", "organize"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE, "entity.hr.EMP_SUB_REWARD", "empId"),
new AuthRoleScopeTargetDetail(ALL_SUBORDINATE, "entity.hr.EMP_SUB_REWARD", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "entity.hr.EMP_SUB_REWARD", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER_WITH_CONCURRENT, "entity.hr.EMP_SUB_REWARD", "empId"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE_WITH_CONCURRENT, "entity.hr.EMP_SUB_REWARD", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "entity.hr.EMP_SUB_REWARD", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.hr.EMP_SUB_REWARD", "workplace"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "entity.hr.EMP_SUB_REWARD", "empType$dictValue"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.EMP_SUB_REWARD", "tenantId")
    )),

    EMP_SUB_ATTACH(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.hr.EMP_SUB_ATTACH", "company"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_ATTACH", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_ATTACH", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.hr.EMP_SUB_ATTACH", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.hr.EMP_SUB_ATTACH", "organize"),
            new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "entity.hr.EMP_SUB_ATTACH", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG, "entity.hr.EMP_SUB_ATTACH", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_ATTACH", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.hr.EMP_SUB_ATTACH", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "entity.hr.EMP_SUB_ATTACH", "organize"),
new AuthRoleScopeTargetDetail(MY_LEADER_AND_SUB, "entity.hr.EMP_SUB_ATTACH", "organize"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE, "entity.hr.EMP_SUB_ATTACH", "empId"),
new AuthRoleScopeTargetDetail(ALL_SUBORDINATE, "entity.hr.EMP_SUB_ATTACH", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "entity.hr.EMP_SUB_ATTACH", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER_WITH_CONCURRENT, "entity.hr.EMP_SUB_ATTACH", "empId"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE_WITH_CONCURRENT, "entity.hr.EMP_SUB_ATTACH", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "entity.hr.EMP_SUB_ATTACH", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.hr.EMP_SUB_ATTACH", "workplace"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "entity.hr.EMP_SUB_ATTACH", "empType$dictValue"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.EMP_SUB_ATTACH", "tenantId")
    )),

    EMP_SUB_SALARY(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.hr.EMP_SUB_SALARY", "company"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_SALARY", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_SALARY", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.hr.EMP_SUB_SALARY", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.hr.EMP_SUB_SALARY", "organize"),
            new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "entity.hr.EMP_SUB_SALARY", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG, "entity.hr.EMP_SUB_SALARY", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG_AND_BELONGINGS, "entity.hr.EMP_SUB_SALARY", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.hr.EMP_SUB_SALARY", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "entity.hr.EMP_SUB_SALARY", "organize"),
new AuthRoleScopeTargetDetail(MY_LEADER_AND_SUB, "entity.hr.EMP_SUB_SALARY", "organize"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE, "entity.hr.EMP_SUB_SALARY", "empId"),
new AuthRoleScopeTargetDetail(ALL_SUBORDINATE, "entity.hr.EMP_SUB_SALARY", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "entity.hr.EMP_SUB_SALARY", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER_WITH_CONCURRENT, "entity.hr.EMP_SUB_SALARY", "empId"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE_WITH_CONCURRENT, "entity.hr.EMP_SUB_SALARY", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "entity.hr.EMP_SUB_SALARY", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.hr.EMP_SUB_SALARY", "workplace"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "entity.hr.EMP_SUB_SALARY", "empType$dictValue"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.EMP_SUB_SALARY", "tenantId")
    )),
    ESIGN_SIGN(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.esign.EmpSignContract", "company"),
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.esign.ElectronContract", "company"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.esign.EmpSignContract", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.esign.ElectronContract", "organize"),
            //new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "", ""),
            new AuthRoleScopeTargetDetail(CREATED_BY_MYSELF, "entity.esign.EmpSignContract", "createBy"),
            new AuthRoleScopeTargetDetail(CREATED_BY_MYSELF, "entity.esign.ElectronContract", "createBy"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.esign.EmpSignContract", "workplace"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.esign.ElectronContract", "workplace"),
            new AuthRoleScopeTargetDetail(ALL, "entity.esign.ElectronContract", "tenantId"),
            new AuthRoleScopeTargetDetail(ALL, "entity.esign.EmpSignContract", "tenantId")
    )),
    ESIGN_CERT(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.esign.EmpCertificateRecord", "company"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.esign.EmpCertificateRecord", "organize"),
            //new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "", ""),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.esign.EmpCertificateRecord", "workplace"),
            new AuthRoleScopeTargetDetail(ALL, "entity.esign.EmpCertificateRecord", "tenantId")
    )),
    CONTRACT(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.hr.LastContract", "company"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.hr.LastContract", "organize"),
            //new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "", ""),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.Contract", "organize"),
            new AuthRoleScopeTargetDetail(CREATED_BY_MYSELF, "entity.hr.LastContract", "createBy"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.hr.LastContract", "workplace"),
            new AuthRoleScopeTargetDetail(ALL_SUBORDINATE, "entity.hr.LastContract", "owner$empId"),
            new AuthRoleScopeTargetDetail(SELECTED_CONTRACT_TYPE, "entity.hr.Contract", "contractSettingType$dictValue"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.Contract", "tenantId"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.LastContract", "tenantId")
    )),
    CONTRACT_RECORD(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.hr.Contract", "company"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.hr.Contract", "organize"),
            //new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "entity.hr.Contract", ""),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.hr.Contract", "workplace"),
            new AuthRoleScopeTargetDetail(ALL_SUBORDINATE, "entity.hr.Contract", "owner$empId"),
            new AuthRoleScopeTargetDetail(SELECTED_CONTRACT_TYPE, "entity.hr.Contract", "contractSettingType$dictValue"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.Contract", "tenantId")
    )),
    CONTRACT_SETTING(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.hr.ContractTypeSet", "company", true),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.ContractTypeSet", "tenantId")
            //new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.hr.ContractTypeSet", ""),
            //new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "", ""),
            //new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.hr.ContractTypeSet", "")
    )),
    // 离职交接
    TERMINATION_HANDOVER(
            Lists.list(
                new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.Handover", "orgId")
            )
    ),
    TERMINATION(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.hr.TerminationApply", "company"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.hr.TerminationApply", "orgId"),
            //new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "", ""),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.TerminationApply", "orgId"),
            new AuthRoleScopeTargetDetail(CREATED_BY_MYSELF, "entity.hr.TerminationApply", "createBy"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.hr.TerminationApply", "workplace"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "entity.hr.TerminationApply", "empType$dictValue"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.TerminationApply", "tenantId")
    )),
    TRANSFER(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "ENTITY_HR_EMP_TRANSFER", ""),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ENTITY_HR_EMP_TRANSFER", ""),
            //new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "", ""),
            new AuthRoleScopeTargetDetail(CREATED_BY_MYSELF, "ENTITY_HR_EMP_TRANSFER", ""),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ENTITY_HR_EMP_TRANSFER", ""),
            new AuthRoleScopeTargetDetail(ALL, "ENTITY_HR_EMP_TRANSFER", "")
    )),
    //待转正目前默认取值员工信息数据范围，单独配置不生效，暂时不需要单独增加这个范围
//    CONFIRMATION_TODO(Lists.list(
//            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "ENTITY_HR_EMP_CONFIRMATION", ""),
//            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ENTITY_HR_EMP_CONFIRMATION", ""),
//            new AuthRoleScopeTargetDetail(MY_ORG, "ENTITY_HR_EMP_CONFIRMATION", ""),
//            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ENTITY_HR_EMP_CONFIRMATION", ""),
//            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ENTITY_HR_EMP_CONFIRMATION", ""),
//            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ENTITY_HR_EMP_CONFIRMATION", ""),
//            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "ENTITY_HR_EMP_CONFIRMATION", ""),
//            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE, "ENTITY_HR_EMP_CONFIRMATION", ""),
//            new AuthRoleScopeTargetDetail(CREATED_BY_MYSELF, "ENTITY_HR_EMP_CONFIRMATION", ""),
//            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ENTITY_HR_EMP_CONFIRMATION", ""),
//            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ENTITY_HR_EMP_CONFIRMATION", ""),
//            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ENTITY_HR_EMP_CONFIRMATION", "")
//    )),
    CONFIRMATION_RECORD(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "ENTITY_HR_EMP_CONFIRMATION", ""),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ENTITY_HR_EMP_CONFIRMATION", ""),
            new AuthRoleScopeTargetDetail(MY_ORG, "ENTITY_HR_EMP_CONFIRMATION", ""),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ENTITY_HR_EMP_CONFIRMATION", ""),
            new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "ENTITY_HR_EMP_CONFIRMATION", ""),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ENTITY_HR_EMP_CONFIRMATION", ""),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ENTITY_HR_EMP_CONFIRMATION", ""),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "ENTITY_HR_EMP_CONFIRMATION", ""),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE, "ENTITY_HR_EMP_CONFIRMATION", ""),
            new AuthRoleScopeTargetDetail(CREATED_BY_MYSELF, "ENTITY_HR_EMP_CONFIRMATION", ""),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ENTITY_HR_EMP_CONFIRMATION", ""),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ENTITY_HR_EMP_CONFIRMATION", ""),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ENTITY_HR_EMP_CONFIRMATION", ""),
            new AuthRoleScopeTargetDetail(ALL, "ENTITY_HR_EMP_CONFIRMATION", "")
    )),
    ONBOARDING_FLOW(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.onboarding.EmpEntryProcess", "company"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.onboarding.EmpEntryProcess", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.onboarding.EmpEntryProcess", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.onboarding.EmpEntryProcess", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.onboarding.EmpEntryProcess", "organize"),
            new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "entity.onboarding.EmpEntryProcess", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG, "entity.onboarding.EmpEntryProcess", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG_AND_BELONGINGS, "entity.onboarding.EmpEntryProcess", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.onboarding.EmpEntryProcess", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "entity.onboarding.EmpEntryProcess", "organize"),
            new AuthRoleScopeTargetDetail(MY_LEADER_AND_SUB, "entity.onboarding.EmpEntryProcess", "organize"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE, "entity.onboarding.EmpEntryProcess", "empId"),
            new AuthRoleScopeTargetDetail(ALL_SUBORDINATE, "entity.onboarding.EmpEntryProcess", "empId"),
            //new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "", ""),
            //new AuthRoleScopeTargetDetail(SELECTED_LEADER_WITH_CONCURRENT, "", ""),
            //new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE_WITH_CONCURRENT, "entity.onboarding.EmpEntryProcess", "empId"),
            //new AuthRoleScopeTargetDetail(ALL_SUBORDINATE_WITH_CONCURRENT, "", ""),
            new AuthRoleScopeTargetDetail(CREATED_BY_MYSELF, "entity.onboarding.EmpEntryProcess", "createBy"),
            //new AuthRoleScopeTargetDetail(SELECTED_EMP, "entity.onboarding.EmpEntryProcess", ""),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.onboarding.EmpEntryProcess", "workplace"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "entity.onboarding.EmpEntryProcess", "empType$dictValue"),
            new AuthRoleScopeTargetDetail(ALL, "entity.onboarding.EmpEntryProcess", "tenantId")
    )),
    ATTENDANCE_DAILY_STATISTIC(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_DAILY_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_DAILY_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_DAILY_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_DAILY_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_DAILY_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_POST, "ATTENDANCE_DAILY_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_BENCH_POST, "ATTENDANCE_DAILY_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_COST_CENTER, "ATTENDANCE_DAILY_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_DAILY_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_DAILY_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_DAILY_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_DAILY_STATISTIC", "empId")
    )),
    ATTENDANCE_MONTH_STATISTIC(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_MONTH_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_MONTH_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_MONTH_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_MONTH_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_MONTH_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_POST, "ATTENDANCE_MONTH_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_BENCH_POST, "ATTENDANCE_MONTH_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_COST_CENTER, "ATTENDANCE_MONTH_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_MONTH_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_MONTH_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_MONTH_STATISTIC", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_MONTH_STATISTIC", "empId")
    )),

    //考勤明细
    ATTENDANCE_STATISTICS_DETAIL(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_STATISTICS_DETAIL", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_STATISTICS_DETAIL", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_STATISTICS_DETAIL", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_STATISTICS_DETAIL", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "ATTENDANCE_STATISTICS_DETAIL", "bid"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE, "ATTENDANCE_STATISTICS_DETAIL", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_STATISTICS_DETAIL", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_POST, "ATTENDANCE_STATISTICS_DETAIL", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_BENCH_POST, "ATTENDANCE_STATISTICS_DETAIL", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_COST_CENTER, "ATTENDANCE_STATISTICS_DETAIL", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_STATISTICS_DETAIL", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_STATISTICS_DETAIL", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_STATISTICS_DETAIL", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_STATISTICS_DETAIL", "empId")
    )),

    //考勤汇总
    ATTENDANCE_STATISTICS_SUMMARY(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_STATISTICS_SUMMARY", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_STATISTICS_SUMMARY", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_STATISTICS_SUMMARY", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_STATISTICS_SUMMARY", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "ATTENDANCE_STATISTICS_SUMMARY", "bid"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE, "ATTENDANCE_STATISTICS_SUMMARY", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_STATISTICS_SUMMARY", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_POST, "ATTENDANCE_STATISTICS_SUMMARY", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_BENCH_POST, "ATTENDANCE_STATISTICS_SUMMARY", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_COST_CENTER, "ATTENDANCE_STATISTICS_SUMMARY", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_STATISTICS_SUMMARY", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_STATISTICS_SUMMARY", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_STATISTICS_SUMMARY", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_STATISTICS_SUMMARY", "empId")
    )),

    ATTENDANCE_CALENDAR(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_POST, "ATTENDANCE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_BENCH_POST, "ATTENDANCE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_COST_CENTER, "ATTENDANCE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_CALENDAR", "empId")
    )),
    ATTENDANCE_TRAVEL_LIST(Lists.list(//外出记录
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_TRAVEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_TRAVEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_TRAVEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_TRAVEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_TRAVEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_TRAVEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_TRAVEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_TRAVEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_TRAVEL_LIST", "empId")
    )),
    ATTENDANCE_BATCH_TRAVEL_LIST(Lists.list(// 批量出差
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_BATCH_TRAVEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_BATCH_TRAVEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_BATCH_TRAVEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_BATCH_TRAVEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_BATCH_TRAVEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_BATCH_TRAVEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_BATCH_TRAVEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_BATCH_TRAVEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_BATCH_TRAVEL_LIST", "empId")
    )),
    ATTENDANCE_BATCH_LEAVE_LIST(Lists.list(// 批量休假
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_BATCH_LEAVE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_BATCH_LEAVE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_BATCH_LEAVE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_BATCH_LEAVE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_BATCH_LEAVE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_BATCH_LEAVE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_BATCH_LEAVE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_BATCH_LEAVE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_BATCH_LEAVE_LIST", "empId")
    )),
    ATTENDANCE_BATCH_OVERTIME_LIST(Lists.list(// 批量加班
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_BATCH_OVERTIME_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_BATCH_OVERTIME_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_BATCH_OVERTIME_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_BATCH_OVERTIME_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_BATCH_OVERTIME_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_BATCH_OVERTIME_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_BATCH_OVERTIME_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_BATCH_OVERTIME_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_BATCH_OVERTIME_LIST", "empId")
    )),
    ATTENDANCE_BATCH_ANALYSEADJUST_LIST(Lists.list(// 批量考勤异常申请
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_BATCH_ANALYSEADJUST_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_BATCH_ANALYSEADJUST_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_BATCH_ANALYSEADJUST_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_BATCH_ANALYSEADJUST_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_BATCH_ANALYSEADJUST_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_BATCH_ANALYSEADJUST_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_BATCH_ANALYSEADJUST_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_BATCH_ANALYSEADJUST_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_BATCH_ANALYSEADJUST_LIST", "empId")
    )),
    ATTENDANCE_TRAVEL_REGISTER(Lists.list(//外勤签到
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_TRAVEL_REGISTER", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_TRAVEL_REGISTER", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_TRAVEL_REGISTER", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_TRAVEL_REGISTER", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_TRAVEL_REGISTER", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_TRAVEL_REGISTER", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_TRAVEL_REGISTER", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_TRAVEL_REGISTER", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_TRAVEL_REGISTER", "empId")
    )),
    ATTENDANCE_REGISTER_DAILY(Lists.list(//日常打卡
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_REGISTER_DAILY", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_REGISTER_DAILY", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_REGISTER_DAILY", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_REGISTER_DAILY", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_REGISTER_DAILY", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_REGISTER_DAILY", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_REGISTER_DAILY", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_REGISTER_DAILY", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_REGISTER_DAILY", "empId")
    )),
    ATTENDANCE_REGISTER_BDK(Lists.list(//补打卡
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_REGISTER_BDK", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_REGISTER_BDK", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_REGISTER_BDK", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_REGISTER_BDK", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_REGISTER_BDK", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_REGISTER_BDK", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_REGISTER_BDK", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_REGISTER_BDK", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_REGISTER_BDK", "empId")
    )),
    ATTENDANCE_REGISTER_ALL(Lists.list(//所有打卡
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_REGISTER_ALL", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_REGISTER_ALL", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_REGISTER_ALL", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_REGISTER_ALL", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_REGISTER_ALL", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_REGISTER_ALL", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_REGISTER_ALL", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_REGISTER_ALL", "empId")
    )),
    ATTENDANCE_LEAVE_LIST(Lists.list(//休假
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_LEAVE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_LEAVE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_LEAVE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_LEAVE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_LEAVE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_LEAVE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_LEAVE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_LEAVE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_LEAVE_LIST", "empId")
    )),
    ATTENDANCE_LEAVE_CANCEL_LIST(Lists.list(//销假
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_LEAVE_CANCEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_LEAVE_CANCEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_LEAVE_CANCEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_LEAVE_CANCEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_LEAVE_CANCEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_LEAVE_CANCEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_LEAVE_CANCEL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_LEAVE_CANCEL_LIST", "bid")
    )),
    ATTENDANCE_LEAVE_QUOTA(Lists.list(//假期余额
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_LEAVE_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_LEAVE_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_LEAVE_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_LEAVE_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_LEAVE_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_LEAVE_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_LEAVE_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_LEAVE_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_LEAVE_QUOTA", "empId")
    )),
    ATTENDANCE_ANNUAL_QUOTA(Lists.list(//年假明细
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_ANNUAL_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_ANNUAL_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_ANNUAL_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_ANNUAL_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_ANNUAL_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_ANNUAL_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_ANNUAL_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_ANNUAL_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_ANNUAL_QUOTA", "empId")
    )),
    ATTENDANCE_COMPENSATORY_QUOTA(Lists.list(//调休明细
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_COMPENSATORY_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_COMPENSATORY_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_COMPENSATORY_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_COMPENSATORY_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_COMPENSATORY_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_COMPENSATORY_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_COMPENSATORY_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_COMPENSATORY_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_COMPENSATORY_QUOTA", "empId")
    )),
    ATTENDANCE_FIXED_QUOTA(Lists.list(//固定额度明细
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_FIXED_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_FIXED_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_FIXED_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_FIXED_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_FIXED_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_FIXED_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_FIXED_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_FIXED_QUOTA", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_FIXED_QUOTA", "empId")
    )),
    ATTENDANCE_OVERTIME_LIST(Lists.list(//加班
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_OVERTIME_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_OVERTIME_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_OVERTIME_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_OVERTIME_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_OVERTIME_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_OVERTIME_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_OVERTIME_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_OVERTIME_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_OVERTIME_LIST", "empId")
    )),
    ATTENDANCE_FILE_GROUP(Lists.list(//考勤方案
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_FILE_GROUP", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_FILE_GROUP", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_FILE_GROUP", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_FILE_GROUP", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_FILE_GROUP", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_FILE_GROUP", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_FILE_GROUP", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_FILE_GROUP", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_FILE_GROUP", "empId")
    )),
    ATTENDANCE_FILE_CLOCK_PLAN(Lists.list(//打卡方案
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_FILE_CLOCK_PLAN", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_FILE_CLOCK_PLAN", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_FILE_CLOCK_PLAN", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_FILE_CLOCK_PLAN", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_FILE_CLOCK_PLAN", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_FILE_CLOCK_PLAN", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_FILE_CLOCK_PLAN", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_FILE_CLOCK_PLAN", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_FILE_CLOCK_PLAN", "empId")
    )),
    ATTENDANCE_FILE_CALENDAR(Lists.list(//员工日历
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_FILE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_FILE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_FILE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_FILE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_FILE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_FILE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_FILE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_FILE_CALENDAR", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_FILE_CALENDAR", "empId")
    )),
    ATTENDANCE_SHIFT_LIST(Lists.list(//排班明细
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_SHIFT_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_SHIFT_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_SHIFT_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_SHIFT_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_SHIFT_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_SHIFT_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_SHIFT_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_SHIFT_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_SHIFT_LIST", "empId")
    )),
    ATTENDANCE_SHIFT_CHANGE_LIST(Lists.list(//调整明细
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_SHIFT_CHANGE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_SHIFT_CHANGE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_SHIFT_CHANGE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_SHIFT_CHANGE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_SHIFT_CHANGE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_SHIFT_CHANGE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_SHIFT_CHANGE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_SHIFT_CHANGE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_SHIFT_CHANGE_LIST", "empId")
    )),
    ATTENDANCE_SHIFT_APPLY_LIST(Lists.list(//调班记录
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_SHIFT_APPLY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_SHIFT_APPLY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_SHIFT_APPLY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_SHIFT_APPLY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_SHIFT_APPLY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_SHIFT_APPLY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_SHIFT_APPLY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_SHIFT_APPLY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_SHIFT_APPLY_LIST", "empId")
    )),
    ATTENDANCE_COMPENSATORY_APPLY_LIST(Lists.list(//调休付现
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_COMPENSATORY_APPLY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_COMPENSATORY_APPLY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_COMPENSATORY_APPLY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_COMPENSATORY_APPLY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_COMPENSATORY_APPLY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_COMPENSATORY_APPLY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_COMPENSATORY_APPLY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_COMPENSATORY_APPLY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_COMPENSATORY_APPLY_LIST", "empId")
    )),
    ATTENDANCE_TRAVEL_COMPENSATORY_LIST(Lists.list(//外出调休
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_TRAVEL_COMPENSATORY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_TRAVEL_COMPENSATORY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_TRAVEL_COMPENSATORY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_TRAVEL_COMPENSATORY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_TRAVEL_COMPENSATORY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_TRAVEL_COMPENSATORY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_TRAVEL_COMPENSATORY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_TRAVEL_COMPENSATORY_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_TRAVEL_COMPENSATORY_LIST", "empId")
    )),
    ATTENDANCE_DAY_ANALYSE_ABNORMAL_LIST(Lists.list(//考勤异常汇总
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_DAY_ANALYSE_ABNORMAL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_DAY_ANALYSE_ABNORMAL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_DAY_ANALYSE_ABNORMAL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_DAY_ANALYSE_ABNORMAL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_DAY_ANALYSE_ABNORMAL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_DAY_ANALYSE_ABNORMAL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_DAY_ANALYSE_ABNORMAL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_DAY_ANALYSE_ABNORMAL_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_DAY_ANALYSE_ABNORMAL_LIST", "empId")
    )),
    ATTENDANCE_OVERTIME_REVOKE(Lists.list(//加班撤销
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_OVERTIME_REVOKE", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_OVERTIME_REVOKE", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_OVERTIME_REVOKE", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_OVERTIME_REVOKE", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_OVERTIME_REVOKE", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_OVERTIME_REVOKE", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_OVERTIME_REVOKE", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_OVERTIME_REVOKE", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_OVERTIME_REVOKE", "empId")
    )),
    ATTENDANCE_OVERTIME_ABOLISH(Lists.list(//加班废止
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_OVERTIME_ABOLISH", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_OVERTIME_ABOLISH", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_OVERTIME_ABOLISH", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_OVERTIME_ABOLISH", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_OVERTIME_ABOLISH", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_OVERTIME_ABOLISH", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_OVERTIME_ABOLISH", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_OVERTIME_ABOLISH", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_OVERTIME_ABOLISH", "empId")
    )),
    ATTENDANCE_TRAVEL_REVOKE(Lists.list(//外出撤销
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_TRAVEL_REVOKE", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_TRAVEL_REVOKE", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_TRAVEL_REVOKE", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_TRAVEL_REVOKE", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_TRAVEL_REVOKE", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_TRAVEL_REVOKE", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_TRAVEL_REVOKE", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_TRAVEL_REVOKE", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_TRAVEL_REVOKE", "empId")
    )),
    ATTENDANCE_TRAVEL_ABOLISH(Lists.list(//外出废止
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_TRAVEL_ABOLISH", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_TRAVEL_ABOLISH", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_TRAVEL_ABOLISH", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_TRAVEL_ABOLISH", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_TRAVEL_ABOLISH", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_TRAVEL_ABOLISH", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_TRAVEL_ABOLISH", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_TRAVEL_ABOLISH", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_TRAVEL_ABOLISH", "empId")
    )),
    ATTENDANCE_LEAVE_EXTENSION(Lists.list(//假期延期
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_LEAVE_EXTENSION", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_LEAVE_EXTENSION", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_LEAVE_EXTENSION", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_LEAVE_EXTENSION", "bid"),
            new AuthRoleScopeTargetDetail(MYSELF, "ATTENDANCE_LEAVE_EXTENSION", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_LEAVE_EXTENSION", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_LEAVE_EXTENSION", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_LEAVE_EXTENSION", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_LEAVE_EXTENSION", "empId")
    )),
    ATTENDANCE_OT_LEFT_DURATION(Lists.list(//加班结余
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "ATTENDANCE_OT_LEFT_DURATION", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_OT_LEFT_DURATION", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "ATTENDANCE_OT_LEFT_DURATION", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "ATTENDANCE_OT_LEFT_DURATION", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "ATTENDANCE_OT_LEFT_DURATION", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "ATTENDANCE_OT_LEFT_DURATION", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "ATTENDANCE_OT_LEFT_DURATION", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "ATTENDANCE_OT_LEFT_DURATION", "empId")
    )),
    // 薪资档案
    PAYROLL_ARCHIVES(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "PAYROLL_ARCHIVES", "orgId"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "PAYROLL_ARCHIVES", "orgId"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "PAYROLL_ARCHIVES", "orgId"),
            new AuthRoleScopeTargetDetail(MY_ORG, "PAYROLL_ARCHIVES", "orgId")
    )),
    // 薪资数据收集
    PAYROLL_DATA_COLLECTION(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "PAYROLL_DATA_COLLECTION", "orgId"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "PAYROLL_DATA_COLLECTION", "orgId"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "PAYROLL_DATA_COLLECTION", "orgId"),
            new AuthRoleScopeTargetDetail(MY_ORG, "PAYROLL_DATA_COLLECTION", "orgId")
    )),
    // 薪资方案
    PAYROLL_PLAN(Lists.list(
            new AuthRoleScopeTargetDetail(CREATED_BY_MYSELF, "PAYROLL_PLAN", "createBy"),
            new AuthRoleScopeTargetDetail(SELECTED_PAYROLL_PLAN, "PAYROLL_PLAN", "bid")
    )),
    // 薪资计算
    PAYROLL_CALCULATION(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "PAYROLL_CALCULATION", "orgId"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "PAYROLL_CALCULATION", "orgId"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "PAYROLL_CALCULATION", "orgId"),
            new AuthRoleScopeTargetDetail(MY_ORG, "PAYROLL_CALCULATION", "orgId")
    )),
    // 薪资报表
    PAYROLL_REPORT(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "PAYROLL_REPORT", "orgId"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "PAYROLL_REPORT", "orgId"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "PAYROLL_REPORT", "orgId"),
            new AuthRoleScopeTargetDetail(MY_ORG, "PAYROLL_REPORT", "orgId")
    )),
    // 社保增减人员
    SOCIAL_DATA_MAINTENANCE(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "SOCIAL_DATA_MAINTENANCE", "orgId"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "SOCIAL_DATA_MAINTENANCE", "orgId"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "SOCIAL_DATA_MAINTENANCE", "orgId"),
            new AuthRoleScopeTargetDetail(MY_ORG, "SOCIAL_DATA_MAINTENANCE", "orgId")
    )),
    // 社保报表
    SOCIAL_REPORT(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "SOCIAL_REPORT", "orgId"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "SOCIAL_REPORT", "orgId"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "SOCIAL_REPORT", "orgId"),
            new AuthRoleScopeTargetDetail(MY_ORG, "SOCIAL_REPORT", "orgId")
    )),
    // 社保数据收集
    SOCIAL_DATA_COLLECTION(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "SOCIAL_DATA_COLLECTION", "orgId"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "SOCIAL_DATA_COLLECTION", "orgId"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "SOCIAL_DATA_COLLECTION", "orgId"),
            new AuthRoleScopeTargetDetail(MY_ORG, "SOCIAL_DATA_COLLECTION", "orgId")
    )),
    // 社保账单
    SOCIAL_BILL(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "SOCIAL_BILL", "orgId"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "SOCIAL_BILL", "orgId"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "SOCIAL_BILL", "orgId"),
            new AuthRoleScopeTargetDetail(MY_ORG, "SOCIAL_BILL", "orgId")
    )),
    // 奖金方案
    BONUS_PLAN(Lists.list(
            // new AuthRoleScopeTargetDetail(CREATED_BY_MYSELF, "entity.payroll.BonusSchema", "createBy"),
            new AuthRoleScopeTargetDetail(SELECTED_BONUS_PLAN, "entity.bonus.BonusSchema", "bid")
            // new AuthRoleScopeTargetDetail(CREATED_BY_MYSELF, "entity.payroll.BonusAccount", "createBy"),
            // new AuthRoleScopeTargetDetail(SELECTED_BONUS_PLAN, "entity.payroll.BonusAccount", "schemaId"),
            // new AuthRoleScopeTargetDetail(ALL, "entity.payroll.BonusAccount", "tenantId")
    )),
    // 奖金计算
    BONUS_CALCULATION(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_BONUS_PLAN, "entity.bonus.BonusLedger", "schemaId"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.EmpWorkInfo", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.hr.EmpWorkInfo", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.hr.EmpWorkInfo", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.hr.EmpWorkInfo", "organize"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.EmpWorkInfo", "tenantId")
    )),
    // 奖金报表
    BONUS_REPORT(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_BONUS_PLAN, "entity.bonus.BonusReportEmp", "schemaId"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.EmpWorkInfo", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.hr.EmpWorkInfo", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.hr.EmpWorkInfo", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.hr.EmpWorkInfo", "organize"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.EmpWorkInfo", "tenantId")
    )),
    //合同管理（续签意向确认）
    CONTRACT_CONFIRM_INTENTION(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_CONTRACT_TYPE, "entity.hr.LastContract", "contractSettingType$dictValue"),
            new AuthRoleScopeTargetDetail(ALL, "entity.hr.LastContract", "tenantId")
    )),

    //证书人员管理
    EMP_CERTIFICATE(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "entity.certificate.certificateAndEmp", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER_WITH_CONCURRENT, "entity.certificate.certificateAndEmp", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.certificate.certificateAndEmp", "company"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE_WITH_CONCURRENT, "entity.certificate.certificateAndEmp", "empId"),
            new AuthRoleScopeTargetDetail(ALL_SUBORDINATE, "entity.certificate.certificateAndEmp", "empId"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(SELECTED_CONTRACT_TYPE, "entity.certificate.certificateAndEmp", "contractSettingType$dictValue"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "entity.certificate.certificateAndEmp", "empType$dictValue"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.certificate.certificateAndEmp", "workPlace"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "entity.certificate.certificateAndEmp", "empId"),
            new AuthRoleScopeTargetDetail(ALL, "entity.certificate.certificateAndEmp", "tenantId"),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG_AND_BELONGINGS, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(CREATED_BY_MYSELF, "entity.certificate.certificateAndEmp", "createBy")
    )),
    //资格人员管理
    QUA_CERTIFICATE(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_EMP, "entity.certificate.certificateAndEmp", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER_WITH_CONCURRENT, "entity.certificate.certificateAndEmp", "empId"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "entity.certificate.certificateAndEmp", "company"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE_WITH_CONCURRENT, "entity.certificate.certificateAndEmp", "empId"),
            new AuthRoleScopeTargetDetail(ALL_SUBORDINATE, "entity.certificate.certificateAndEmp", "empId"),
            new AuthRoleScopeTargetDetail(DIRECT_SUBORDINATE, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(SELECTED_CONTRACT_TYPE, "entity.certificate.certificateAndEmp", "contractSettingType$dictValue"),
            new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "entity.certificate.certificateAndEmp", "empType$dictValue"),
            new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "entity.certificate.certificateAndEmp", "workPlace"),
            new AuthRoleScopeTargetDetail(SELECTED_HRBP_WITH_CONCURRENT, "entity.certificate.certificateAndEmp", "empId"),
            new AuthRoleScopeTargetDetail(ALL, "entity.certificate.certificateAndEmp", "tenantId"),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG_AND_BELONGINGS, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "entity.certificate.certificateAndEmp", "organizeId"),
            new AuthRoleScopeTargetDetail(CREATED_BY_MYSELF, "entity.certificate.certificateAndEmp", "createBy")
    )),
    //证书项目
    CERTIFICATE_PROJECT(Lists.list(
            new AuthRoleScopeTargetDetail(ALL, "entity.certificate.CertificateProject", "tenantId"),
            new AuthRoleScopeTargetDetail(MY_ORG, "entity.certificate.CertificateProject", "organize"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.certificate.CertificateProject", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.certificate.CertificateProject", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.certificate.CertificateProject", "organize"),
            new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "entity.certificate.CertificateProject", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG, "entity.certificate.CertificateProject", "organize"),
            new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG_AND_BELONGINGS, "entity.certificate.CertificateProject", "organize"),
            new AuthRoleScopeTargetDetail(SELECTED_LEADER, "entity.certificate.CertificateProject", "organize"),
            new AuthRoleScopeTargetDetail(CREATED_BY_MYSELF, "entity.certificate.CertificateProject", "createBy")
    )),
    BUSINESS_LINE(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_BUSINESS_LINE, "BUSINESS_LINE", "id")
    )),

    // 档案管理
    ARCHIVE_FILE(Lists.list(
            new AuthRoleScopeTargetDetail(SELECT_ARCHIVE_FILE, "archive.file", ""),
                    new AuthRoleScopeTargetDetail(SELECTED_COMPANY, "archive.file", ""),
                    new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "archive.file", ""),
                    new AuthRoleScopeTargetDetail(MY_ORG, "archive.file", ""),
                    new AuthRoleScopeTargetDetail(SELECTED_ORG, "archive.file", ""),
                    new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "archive.file", ""),
                    new AuthRoleScopeTargetDetail(MY_CONCURRENT_ORG_AND_BELONGINGS, "archive.file", ""),
                    new AuthRoleScopeTargetDetail(SELECTED_EMP, "archive.file", ""),
                    new AuthRoleScopeTargetDetail(SELECTED_WORKPLACE, "archive.file", ""),
                    new AuthRoleScopeTargetDetail(SELECTED_EMP_TYPE, "archive.file", ""),
                    new AuthRoleScopeTargetDetail(SPECIFIED_ORG_CODE_PREFIX, "archive.file", "")
    )),
    //续签意向审批记录
    CONTRACT_CONFIRM_RECORD(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_CONTRACT_TYPE, "entity.hr.ContinueLetter", "contractSettingType$dictValue")
    )),
    //工序管理 - 工序管理
    PROCESS_MANAGEMENT(Lists.list(
            new AuthRoleScopeTargetDetail(MY_ORG, "PROCESS_MANAGEMENT", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "PROCESS_MANAGEMENT", "bid")
    )),
    //工序派工-工序管理-产品管理
    PROCESS_MANAGEMENT_PRODUCT_MANAGEMENT(Lists.list(
            new AuthRoleScopeTargetDetail(MY_ORG, "PROCESS_MANAGEMENT_PRODUCT_MANAGEMENT", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "PROCESS_MANAGEMENT_PRODUCT_MANAGEMENT", "bid")
    )),
    //班组设置-新增/编辑
    SHIFT_GROUP_EDIT(Lists.list(
            new AuthRoleScopeTargetDetail(MY_ORG, "SHIFT_GROUP_EDIT", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "SHIFT_GROUP_EDIT", "bid")
    )),
    //班组设置-班组成员
    SHIFT_GROUP_MEMBERS(Lists.list(
            new AuthRoleScopeTargetDetail(MY_ORG, "SHIFT_GROUP_MEMBERS", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "SHIFT_GROUP_MEMBERS", "bid")
    )),
    //班组设置-班组管理
    SHIFT_GROUP_MANAGEMENT(Lists.list(
            new AuthRoleScopeTargetDetail(MY_ORG, "SHIFT_GROUP_MANAGEMENT", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "SHIFT_GROUP_MANAGEMENT", "bid")
    )),
    //工序派工-排产派工-全部订单
    SCHEDULING_DISPATCH_ALL_ORDERS(Lists.list(
            new AuthRoleScopeTargetDetail(MY_ORG, "SCHEDULING_DISPATCH_ALL_ORDERS", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "SCHEDULING_DISPATCH_ALL_ORDERS", "bid")
    )),
    //工序派工-排产派工-当天订单
    SCHEDULING_DISPATCH_TODAY_ORDERS(Lists.list(
            new AuthRoleScopeTargetDetail(MY_ORG, "SCHEDULING_DISPATCH_TODAY_ORDERS", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "SCHEDULING_DISPATCH_TODAY_ORDERS", "bid")
    )),
    //工序派工-排产派工-排班及工序状态
    WFM_SCHEDULING_AND_PROCESS_STATUS(Lists.list(
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP, "WFM_SCHEDULING_AND_PROCESS_STATUS", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_SCHEDULE_GROUP, "WFM_SCHEDULING_AND_PROCESS_STATUS", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "WFM_SCHEDULING_AND_PROCESS_STATUS", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "WFM_SCHEDULING_AND_PROCESS_STATUS", "bid")
    )),
    //工序派工-排产派工-全部订单-排产-派工-新增员工
    DISPATCH_WORK(Lists.list(
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP, "DISPATCH_WORK", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_SCHEDULE_GROUP, "DISPATCH_WORK", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "DISPATCH_WORK", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "DISPATCH_WORK", "bid"),
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP_ADMIN, "DISPATCH_WORK", "bid")
    )),
    //按员工排班
    EMP_SCHEDULE_LIST(Lists.list(
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP, "EMP_SCHEDULE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_SCHEDULE_GROUP, "EMP_SCHEDULE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP_ADMIN, "EMP_SCHEDULE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "EMP_SCHEDULE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "EMP_SCHEDULE_LIST", "bid")
    )),
    //实际排班
    EMP_ACTUAL_SCHEDULE_LIST(Lists.list(
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP, "EMP_ACTUAL_SCHEDULE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_SCHEDULE_GROUP, "EMP_ACTUAL_SCHEDULE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP_ADMIN, "EMP_ACTUAL_SCHEDULE_LIST", "bid")
    )),
    //工时结算
    ATTENDANCE_WORK_HOUR_SETTLEMENT(Lists.list(
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "ATTENDANCE_WORK_HOUR_SETTLEMENT", "bid")
    )),
    //排产
    PRODUCTION_SCHEDULING(Lists.list(
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP, "PRODUCTION_SCHEDULING", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_SCHEDULE_GROUP, "PRODUCTION_SCHEDULING", "bid"),
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP_ADMIN, "PRODUCTION_SCHEDULING", "bid")
    )),
    //绩效
    PERFORMANCE_LIST(Lists.list(
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP, "PERFORMANCE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_SCHEDULE_GROUP, "PERFORMANCE_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP_ADMIN, "PERFORMANCE_LIST", "bid")
    )),
    //工时管理-工时统计-每日统计
    WFM_WORKING_HOUR_DAY(Lists.list(
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP, "WFM_WORKING_HOUR_DAY", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_SCHEDULE_GROUP, "WFM_WORKING_HOUR_DAY", "bid"),
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP_ADMIN, "WFM_WORKING_HOUR_DAY", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "WFM_WORKING_HOUR_DAY", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "WFM_WORKING_HOUR_DAY", "bid")
    )),
    //工时管理-工时统计-月度统计
    WFM_WORKING_HOUR_MONTH(Lists.list(
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP, "WFM_WORKING_HOUR_MONTH", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_SCHEDULE_GROUP, "WFM_WORKING_HOUR_MONTH", "bid"),
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP_ADMIN, "WFM_WORKING_HOUR_MONTH", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "WFM_WORKING_HOUR_MONTH", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "WFM_WORKING_HOUR_MONTH", "bid")
    )),
    //工时管理-工时统计-计薪工序汇总
    WFM_WORKING_HOUR_STATISTICS_PROCESS(Lists.list(
            new AuthRoleScopeTargetDetail(MY_ORG, "WFM_WORKING_HOUR_STATISTICS_PROCESS", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "WFM_WORKING_HOUR_STATISTICS_PROCESS", "bid")
    )),
    //工时管理-异常工时-异常申请
    WFM_ABNORMAL_WORKING_HOUR_APPLY(Lists.list(
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP, "WFM_ABNORMAL_WORKING_HOUR_APPLY", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_SCHEDULE_GROUP, "WFM_ABNORMAL_WORKING_HOUR_APPLY", "bid"),
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP_ADMIN, "WFM_ABNORMAL_WORKING_HOUR_APPLY", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "WFM_ABNORMAL_WORKING_HOUR_APPLY", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "WFM_ABNORMAL_WORKING_HOUR_APPLY", "bid")
    )),
    //工时管理-异常工时-申请记录
    WFM_ABNORMAL_WORKING_HOUR_RECORD(Lists.list(
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP, "WFM_ABNORMAL_WORKING_HOUR_RECORD", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_SCHEDULE_GROUP, "WFM_ABNORMAL_WORKING_HOUR_RECORD", "bid"),
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP_ADMIN, "WFM_ABNORMAL_WORKING_HOUR_RECORD", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "WFM_ABNORMAL_WORKING_HOUR_RECORD", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "WFM_ABNORMAL_WORKING_HOUR_RECORD", "bid")
    )),
    //工时管理-异常工时-员工异常工时分页
    WFM_EMP_ABNORMAL_WORKING_HOUR_PAGE(Lists.list(
            new AuthRoleScopeTargetDetail(SELECTED_ORG, "WFM_EMP_ABNORMAL_WORKING_HOUR_PAGE", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "WFM_EMP_ABNORMAL_WORKING_HOUR_PAGE", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG, "WFM_EMP_ABNORMAL_WORKING_HOUR_PAGE", "bid"),
            new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "WFM_EMP_ABNORMAL_WORKING_HOUR_PAGE", "bid")
    )),
    //H5-工序列表
    H5_WFM_WORKING_PROCESS_LIST(Lists.list(
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP, "H5_WFM_WORKING_PROCESS_LIST", "bid"),
            new AuthRoleScopeTargetDetail(SELECTED_SCHEDULE_GROUP, "H5_WFM_WORKING_PROCESS_LIST", "bid"),
            new AuthRoleScopeTargetDetail(MY_SCHEDULE_GROUP_ADMIN, "H5_WFM_WORKING_PROCESS_LIST", "bid")
    )),
    POST_ALLOWANCE(
            Lists.list(
                    new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.hr.PostAllowance", "orgId")
            )
    ),
    POST_ALLOWANCE_APPLY(
            Lists.list(
                    new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.hr.EmpWorkInfo", "organize"),
                    new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.EmpWorkInfo", "organize"),
                    new AuthRoleScopeTargetDetail(MY_ORG, "entity.hr.EmpWorkInfo", "organize"),
                    new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.hr.EmpWorkInfo", "organize")
            )
    ),
    WFM_ABNORMAL_WORKING_HOUR_APPLY_SELECT_EMP(
            Lists.list(
                    new AuthRoleScopeTargetDetail(MY_ORG, "WFM_ABNORMAL_WORKING_HOUR_APPLY_SELECT_EMP", "bid"),
                    new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "WFM_ABNORMAL_WORKING_HOUR_APPLY_SELECT_EMP", "bid")
            )
    ),
    SKILL_ALLOWANCE_APPLY(
            Lists.list(
                    new AuthRoleScopeTargetDetail(MY_ORG, "entity.hr.EmpWorkInfo", "organize"),
                    new AuthRoleScopeTargetDetail(MY_ORG_AND_BELONGINGS, "entity.hr.EmpWorkInfo", "organize"),
                    new AuthRoleScopeTargetDetail(SELECTED_ORG, "entity.hr.EmpWorkInfo", "organize"),
                    new AuthRoleScopeTargetDetail(SELECTED_ORG_AND_BELONGINGS, "entity.hr.EmpWorkInfo", "organize")
            )
    );

    private List<AuthRoleScopeTargetDetail> targetDetails;

    AuthRoleScopeTarget(List<AuthRoleScopeTargetDetail> targetDetails){
        this.targetDetails = targetDetails;
    }

    public static boolean container(String value) {
        if (StringUtils.isBlank(value)) {
            return false;
        }
        for (AuthRoleScopeTarget authRoleScopeTarget : AuthRoleScopeTarget.values()) {
            if (authRoleScopeTarget.name().equals(value)) {
                return true;
            }
        }
        return false;
    }
}
