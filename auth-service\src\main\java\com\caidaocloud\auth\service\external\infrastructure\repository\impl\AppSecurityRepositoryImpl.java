package com.caidaocloud.auth.service.external.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.caidaocloud.auth.service.external.domain.entity.AppSecurityEntity;
import com.caidaocloud.auth.service.external.domain.repository.IAppSecurityRepository;
import com.caidaocloud.auth.service.external.infrastructure.enums.ExternalAuthEnum;
import com.caidaocloud.auth.service.external.infrastructure.repository.mybatis.AppSecurityDao;
import com.caidaocloud.auth.service.external.infrastructure.repository.po.AppSecurityPo;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/1/11
 **/
@Repository
public class AppSecurityRepositoryImpl implements IAppSecurityRepository {
    @Autowired
    private AppSecurityDao appSecurityDao;

    @Override
    public void save(AppSecurityEntity entity) {
        if (entity == null) {
            return;
        }
        long currentTimeMillis = System.currentTimeMillis();
        AppSecurityPo appSecurityPo = entity.convertToPo()
                .setCreateTime(currentTimeMillis)
                .setUpdateTime(currentTimeMillis);
        if (StringUtils.isBlank(appSecurityPo.getId())) {
            appSecurityPo.setId(SnowUtil.nextId());
        }
        appSecurityDao.insert(appSecurityPo);
    }

    @Override
    public void update(AppSecurityEntity entity) {
        if (entity == null) {
            return;
        }
        long currentTimeMillis = System.currentTimeMillis();
        AppSecurityPo appSecurityPo = entity.convertToPo()
                .setUpdateTime(currentTimeMillis);
        appSecurityDao.updateById(appSecurityPo);
    }

    @Override
    public Optional<AppSecurityEntity> findAppSecurity(ExternalAuthEnum type) {
        if (Objects.isNull(type)) {
            return Optional.empty();
        }
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        var queryWrapper = new LambdaQueryWrapper<AppSecurityPo>()
                .eq(AppSecurityPo::getTenantId, userInfo.getTenantId())
                .eq(AppSecurityPo::getDeleted, false)
                .eq(AppSecurityPo::getType, type);
        AppSecurityPo appSecurityPo = appSecurityDao.selectOne(queryWrapper);
        if (appSecurityPo == null) {
            return Optional.empty();
        }
        return Optional.of(appSecurityPo.convertEntity());
    }
}