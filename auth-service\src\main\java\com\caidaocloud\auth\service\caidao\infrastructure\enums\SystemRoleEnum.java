 package com.caidaocloud.auth.service.caidao.infrastructure.enums;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import com.caidaocloud.auth.service.caidao.infrastructure.util.cache.InitResource;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.lang3.StringUtils;

/**
 * 系统默认角色
 *
 * <AUTHOR>
 * @date 2022/5/11
 **/
public enum SystemRoleEnum {

    ADMIN("ADMIN", "超级管理员", "administrator"){
        @Override
        public    Collection<String> staticAuthUrl() {
            return InitResource.adminUrl;
        }
    },
    CONFIG("CONFIG", "配置管理员", "config"){
        @Override
        public    Collection<String> staticAuthUrl() {
            List<String> list = Sequences.sequence(InitResource.adminUrl).toList();
            list.addAll(InitResource.configUrl);
            return list;
        }
    };

    public String code;

    public String chName;

    public String enName;

    SystemRoleEnum(String code, String chName, String enName) {
        this.code = code;
        this.chName = chName;
        this.enName = enName;
    }

    public static List<SystemRoleEnum> toList() {
        SystemRoleEnum[] values = SystemRoleEnum.values();
        return Arrays.asList(values);
    }

    public static Optional<SystemRoleEnum> getCurrentUserRole() {
        String role = SecurityUserUtil.getSecurityUserInfo().getRole();
        if (StringUtils.isBlank(role)) {
            return Optional.empty();
        }
        for (SystemRoleEnum value : SystemRoleEnum.values()) {
            if (role.contains(value.code)) {
                return Optional.of(value);
            }
        }
        return Optional.empty();
    }

    public static SystemRoleEnum getByCode(String code) {
        for (SystemRoleEnum value : SystemRoleEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public   abstract Collection<String> staticAuthUrl();
}
