package com.caidaocloud.auth.service.caidao.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleGroupDo;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthRoleGroupRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.AuthGroupRuleDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.AuthRoleGroupRuleDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthGroupRulePo;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthRoleGroupRulePo;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.service.AuthRoleGroupRuleService;
import com.caidaocloud.auth.service.caidao.infrastructure.util.UserContextUtil;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/9
 **/
@Repository
public class AuthRoleGroupRepositoryImpl implements IAuthRoleGroupRepository {
    @Autowired
    private AuthGroupRuleDao authGroupRuleDao;
    @Autowired
    private AuthRoleGroupRuleDao authRoleGroupRuleDao;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private AuthRoleGroupRuleService authRoleGroupRuleService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdateRoleGroup(AuthRoleGroupDo authRoleGroupDo) {
        if (authRoleGroupDo == null) {
            throw new ServerException("parameter is null");
        }
        if (StringUtils.isBlank(authRoleGroupDo.getName())) {
            throw new ServerException("name is null");
        }
        val userInfo = UserContextUtil.preCheckUser();
        if (userInfo == null) {
            throw new ServerException("not found user info");
        }
        val empId = userInfo.getStaffId() == null ? "0" : userInfo.getStaffId().toString();
        var currentTimeMillis = System.currentTimeMillis();
        var authGroupRulePo = FastjsonUtil.convertObject(authRoleGroupDo, AuthGroupRulePo.class);
        if (authGroupRulePo.getId() == null) {
            authGroupRulePo.setTenantId(userInfo.getTenantId());
            authGroupRulePo.setCreateBy(empId);
            authGroupRulePo.setCreateTime(currentTimeMillis);
            authGroupRulePo.setDeleted(false);
            authGroupRuleDao.insert(authGroupRulePo);
        } else {
            authGroupRulePo.setUpdateBy(empId);
            authGroupRulePo.setUpdateTime(currentTimeMillis);
            authGroupRuleDao.updateById(authGroupRulePo);
        }

        if (CollectionUtils.isEmpty(authRoleGroupDo.getRoleIdList())) {
            return;
        }
        ArrayList<AuthRoleGroupRulePo> authRoleGroupRuleList = Lists.newArrayList();
        if (authRoleGroupDo.getId() == null) {
            for (Long roleId : authRoleGroupDo.getRoleIdList()) {
                var authRoleGroupRulePo = createAuthRoleGroupRulePo(authGroupRulePo.getId(), roleId, userInfo.getTenantId(), currentTimeMillis, empId);
                authRoleGroupRuleList.add(authRoleGroupRulePo);
            }
        } else {
            var queryWrapper = new LambdaQueryWrapper<AuthRoleGroupRulePo>();
            queryWrapper.eq(true, AuthRoleGroupRulePo::getGroupRuleId, authGroupRulePo.getId()).eq(AuthRoleGroupRulePo::getDeleted, 0);
            List<AuthRoleGroupRulePo> authRoleGroupRules = authRoleGroupRuleDao.selectList(queryWrapper);
            val hasRoleIdSet = Sequences.sequence(authRoleGroupRules).map(AuthRoleGroupRulePo::getRoleId).toSet();
            for (Long roleId : authRoleGroupDo.getRoleIdList()) {
                if (!hasRoleIdSet.contains(roleId)) {
                    var authRoleGroupRulePo = createAuthRoleGroupRulePo(authGroupRulePo.getId(), roleId, userInfo.getTenantId(), currentTimeMillis, empId);
                    authRoleGroupRulePo.setDeleted(false);
                    authRoleGroupRuleList.add(authRoleGroupRulePo);
                }
            }
            var roleGroupRuleIdList = Sequences.sequence(authRoleGroupRules)
                    .filter(e -> !authRoleGroupDo.getRoleIdList().contains(e.getRoleId()))
                    .map(AuthRoleGroupRulePo::getId).toList();
            if (!CollectionUtils.isEmpty(roleGroupRuleIdList)) {
                authRoleGroupRuleDao.deleteBatchIds(roleGroupRuleIdList);
            }
        }

        if (!CollectionUtils.isEmpty(authRoleGroupRuleList)) {
            authRoleGroupRuleDao.insertBatchSomeColumn(authRoleGroupRuleList);
        }
    }

    private AuthRoleGroupRulePo createAuthRoleGroupRulePo(Long ruleId, Long roleId, String tenantId, Long currentTime, String empId) {
        var authRoleGroupRulePo = new AuthRoleGroupRulePo();
        authRoleGroupRulePo.setGroupRuleId(ruleId);
        authRoleGroupRulePo.setRoleId(roleId);
        authRoleGroupRulePo.setTenantId(tenantId);
        authRoleGroupRulePo.setCreateTime(currentTime);
        authRoleGroupRulePo.setDeleted(false);
        authRoleGroupRulePo.setCreateBy(empId);
        return authRoleGroupRulePo;
    }

    @Override
    public List<Long> getRoleDefaultGroupRuleId(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            throw new ServerException("parameter is null");
        }
        return authGroupRuleDao.getRoleDefaultGroupRuleId(roleIds);
    }

    @Override
    public List<Long> getRoleIdListByRuleIdList(List<Long> ruleIdList) {
        val queryWrapper = new LambdaQueryWrapper<AuthRoleGroupRulePo>()
                .in(AuthRoleGroupRulePo::getGroupRuleId, ruleIdList).eq(AuthRoleGroupRulePo::getDeleted, false);
        return authRoleGroupRuleDao.selectList(queryWrapper).stream()
                .map(it -> it.getRoleId()).collect(Collectors.toList());
    }

    @Override
    public List<AuthRoleGroupDo> getAuthDefaulfRuleIdByRoleId(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Lists.newArrayList();
        }
        List<AuthRoleGroupRulePo> list = authRoleGroupRuleDao.getRoleDefauldRuleId(roleIdList);
        return Sequences.sequence(list).map(e -> {
            var entity = new AuthRoleGroupDo();
            entity.setRuleId(e.getGroupRuleId());
            entity.setRoleIdList(Lists.newArrayList(e.getRoleId()));
            return entity;
        }).toList();
    }

    @Override
    public void removeByRoleIds(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return;
        }
        var queryWrapper = new LambdaQueryWrapper<AuthRoleGroupRulePo>()
                .in(AuthRoleGroupRulePo::getRoleId, roleIdList);
        authRoleGroupRuleService.remove(queryWrapper);
    }
}
