package com.caidaocloud.auth.service.caidao.domain.repository;

import java.util.List;
import java.util.Map;

import com.caidaocloud.auth.service.caidao.application.dto.hr.EmpSearchColumnsDto;
import com.caidaocloud.auth.service.caidao.application.dto.masterdata.EmpNodeVo;
import com.caidaocloud.auth.service.caidao.application.dto.masterdata.EmpSearchDto;
import com.caidaocloud.auth.service.caidao.application.dto.masterdata.EmpWorkInfoVo;
import com.caidaocloud.auth.service.caidao.application.dto.masterdata.SysEmpInfoDto;
import com.caidaocloud.dto.PageResult;

import org.springframework.web.bind.annotation.RequestBody;

/**
 *
 * <AUTHOR>
 * @date 2023/5/25
 */
public interface ISysEmpRepositoy {


	List<SysEmpInfoDto> getEmpInfoByEmpIds(String empIds);

	PageResult<EmpNodeVo> queryEmpPage(@RequestBody EmpSearchDto dto);


	PageResult<EmpWorkInfoVo> queryEmpColumnsPage(@RequestBody EmpSearchDto dto);


	PageResult<Map<String, String>> searchEmpColumnsPage(@RequestBody EmpSearchColumnsDto dto);


}
