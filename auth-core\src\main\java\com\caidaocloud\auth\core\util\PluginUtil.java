package com.caidaocloud.auth.core.util;

import com.caidaocloud.auth.core.scope.mybatis.SimpleSqlSource;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;

/**
 *
 * <AUTHOR>
 * @date 2024/1/4
 */
public class PluginUtil {
	public static MappedStatement replaceSql2MappedStatement(MappedStatement ms, BoundSql newSql) {
		MappedStatement.Builder builder = new MappedStatement.Builder(ms.getConfiguration(), ms
				.getId(), new SimpleSqlSource(newSql), ms.getSqlCommandType());
		builder.resource(ms.getResource());
		builder.fetchSize(ms.getFetchSize());
		builder.statementType(ms.getStatementType());
		builder.keyGenerator(ms.getKeyGenerator());
		builder.timeout(ms.getTimeout());
		builder.parameterMap(ms.getParameterMap());
		builder.resultMaps(ms.getResultMaps());
		builder.resultSetType(ms.getResultSetType());
		builder.cache(ms.getCache());
		builder.flushCacheRequired(ms.isFlushCacheRequired());
		builder.useCache(ms.isUseCache());
		return builder.build();
	}
}
