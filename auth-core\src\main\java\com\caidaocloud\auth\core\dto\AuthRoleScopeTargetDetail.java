package com.caidaocloud.auth.core.dto;

import com.caidaocloud.auth.core.enums.AuthRoleScopeRestriction;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
@NoArgsConstructor
public class AuthRoleScopeTargetDetail {

    private AuthRoleScopeRestriction restriction;

    private String identifier;

    private String property;

    private boolean inToOr;

    public AuthRoleScopeTargetDetail(
            AuthRoleScopeRestriction restriction, String identifier,
            String property, boolean inToOr) {
        this.restriction = restriction;
        this.identifier = identifier;
        this.property = property;
        this.inToOr = inToOr;
    }

    public AuthRoleScopeTargetDetail(
            AuthRoleScopeRestriction restriction, String identifier,
            String property) {
        this(restriction, identifier, property, false);
    }


    public boolean checkAuthScope(String identifier, AuthRoleScopeRestriction restriction){
        return checkIdentifierMapping(identifier, this.identifier) && this.restriction.equals(restriction);
    }

    private static boolean checkIdentifierMapping(String queryIdentifier, String scopeSetIdentifier) {
        if (StringUtils.equals(queryIdentifier, scopeSetIdentifier)) {
            return true;
        }
        return "entity.hr.EmpWorkInfo".equals(queryIdentifier)
                && scopeSetIdentifier.startsWith("entity.hr.EMP_SUB");
    }

}
