package com.caidaocloud.auth.service.caidao.domain.repository;

import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleGroupDo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/9
 **/
public interface IAuthRoleGroupRepository {

    /**
     * 保存或更新角色组
     *
     * @param authRoleGroupDo
     */
    void saveOrUpdateRoleGroup(AuthRoleGroupDo authRoleGroupDo);

    /**
     * 获取角色默认角色组的ruleid
     *
     * @param roleId
     * @return
     */
    List<Long> getRoleDefaultGroupRuleId(List<Long> roleIds);

    List<Long> getRoleIdListByRuleIdList(List<Long> ruleIdList);

    /**
     * 查询角色默认角色组
     *
     * @param roleIdList
     * @return
     */
    List<AuthRoleGroupDo> getAuthDefaulfRuleIdByRoleId(List<Long> roleIdList);

    /**
     * 删除
     *
     * @param roleIdList
     */
    void removeByRoleIds(List<Long> roleIdList);
}
