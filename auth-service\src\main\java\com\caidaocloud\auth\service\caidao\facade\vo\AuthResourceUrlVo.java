package com.caidaocloud.auth.service.caidao.facade.vo;

import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * created by: FoAng
 * create time: 29/5/2024 11:21 上午
 */
@Data
public class AuthResourceUrlVo implements Serializable {

    @ApiModelProperty("请求url地址")
    private String url;

    @ApiModelProperty("菜单名称")
    private String name;

    @ApiModelProperty("父级菜单名称")
    private String parentName;

    @ApiModelProperty("菜单类型")
    private ResourceCategoryEnum category;

    @ApiModelProperty("资源操作类型")
    private String resourceAction;

}
