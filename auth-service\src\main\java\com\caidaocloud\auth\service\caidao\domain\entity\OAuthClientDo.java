package com.caidaocloud.auth.service.caidao.domain.entity;

import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2022/12/20
 */
@Data
public class OAuthClientDo {
	private String clientId;

	private String resourceIds;

	private String clientSecret;

	private String scope;

	private String authorizedGrantTypes = "";

	private String webServerRedirectUri;

	private String authorities;

	private Integer accessTokenValidity;

	private Integer refreshTokenValidity;

	private String additionalInformation;

	private String autoapprove;

	private String tenantId;

	private Long belongId;

	/**
	 * 默认为 0，启用，1 禁用，2 删除
	 */
	private Integer status;
	/**
	 * 权限接口，多个逗号分割
	 */
	private String authUrl;
}
