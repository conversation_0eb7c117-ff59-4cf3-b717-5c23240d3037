package com.caidaocloud.auth.core.enums;

import org.apache.commons.lang3.StringUtils;

public enum AuthRoleScopeTargetType {

	DB, PAAS, STANDARD, FORM;


	public static AuthRoleScopeTargetType findTargetType(String target) {
		return findTargetType(target, null);
	}

	public static AuthRoleScopeTargetType findTargetType(String target, AuthRoleScopeTargetType targetType) {
		if (StringUtils.startsWith(target, "FORM_")) {
			return FORM;
		}
		return targetType;
	}
}
