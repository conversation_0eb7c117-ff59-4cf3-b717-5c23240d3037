package com.caidaocloud.auth.service.caidao.application.dto.masterdata;

import java.math.BigDecimal;

import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.JobGradeRange;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/5/25
 */
@Data
@ApiModel("员工信息")
public class EmpWorkInfoVo {
	private String bid;

	/**
	 * 员工ID
	 */
	private String empId;

	/**
	 * 员工工号
	 */
	private String workno;

	/**
	 * 员工姓名
	 */
	private String name;

	/**
	 * 员工英文名
	 */
	private String enName;

	/**
	 * 入职日期
	 */
	private Long hireDate;

	/**
	 * 员工状态
	 */
	private EnumSimple empStatus;

	/**
	 * 员工头像
	 */
	private Attachment photo;

	/**
	 * 直接上级
	 */
	private EmpSimple leadEmpId;

	/**
	 * 所属组织Id
	 */
	private String organize;

	/**
	 * 所属组织
	 */
	private String organizeTxt;

	/**
	 * 职级职等
	 */
	private JobGradeRange jobGrade;

	/**
	 * 关联的职务ID
	 */
	private String job;

	/**
	 * 岗位ID
	 */
	private String post;
	/**
	 * 岗位ID
	 */
	private String postTxt;

	/**
	 * 试用期期限
	 */
	private EnumSimple probation;

	/**
	 * 转正日期
	 */
	private Long confirmationDate;

	/**
	 * 转正状态
	 */
	private EnumSimple confirmationStatus;

	/**
	 * 用工类型\员工类型
	 */
	private DictSimple empType;

	/**
	 * 离职日期
	 */
	private Long leaveDate;

	/**
	 * 工时制
	 */
	private EnumSimple workHour;

	/**
	 * 司龄
	 */
	private BigDecimal divisionAge;

	/**
	 * 司龄调整
	 */
	private BigDecimal divisionAgeAdjust;

	/**
	 * 员工公司邮箱
	 */
	private String companyEmail;

	/**
	 * 工作地ID
	 */
	private String workplace;

	/**
	 * 入司途径
	 */
	private DictSimple joinCompanyWay;

	/**
	 * 成本中心
	 */
	private String costCenters;

	/**
	 * 合同公司ID
	 */
	private String company;

	/**
	 * 预计毕业日期
	 */
	private Long expectGraduateDate;
	/**
	 * 通讯地址邮编
	 */
	private String mailAddressZipCode;

	/**
	 * 预计离职日期
	 */
	private Long expectedResignDate;

	/**
	 * 社保缴纳地
	 */
	private Address socialSecurity;

	/**
	 * 公积金缴纳地
	 */
	private Address providentFund;

	/**
	 * 直接上级组织ID
	 */
	private String leaderOrganize;

	/**
	 * 直接上级岗位ID
	 */
	private String leaderPost;

	/**
	 * 离职类型
	 */
	private DictSimple resignType;

	/**
	 * 离职原因
	 */
	private DictSimple resignReason;

	/**
	 * 离职原因（员工申请）
	 */
	private DictSimple empResignReason;

	/**
	 * 司龄（至年底）
	 */
	private BigDecimal divisionAgeToYear;

	/**
	 * 试用期截止日期
	 */
	private Long probationPeriodEndDate;

	/**
	 * 合同类型
	 */
	private DictSimple contractType;

	/**
	 * 离职状态
	 */
	private EnumSimple resignationStatus;

	/**
	 * 退休日期
	 */
	private Long retireDate;

	private Long dataStartTime;
}
