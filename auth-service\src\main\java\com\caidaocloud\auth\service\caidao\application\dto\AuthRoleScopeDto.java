package com.caidaocloud.auth.service.caidao.application.dto;

import com.caidaocloud.auth.core.enums.AuthRoleScopeRestriction;
import com.caidaocloud.auth.core.enums.AuthRoleScopeTargetType;
import com.caidaocloud.auth.service.caidao.facade.vo.AuthScopeRestrictionVo;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.AuthRoleScopeComparator;
import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Data
public class AuthRoleScopeDto {
    private AuthRoleScopeTargetType targetType = AuthRoleScopeTargetType.STANDARD;
    private String targets;
    private String filterProperty;
    private AuthRoleScopeComparator comparator = AuthRoleScopeComparator.IN;
    private AuthRoleScopeRestriction restriction;
    private String values;
    private String simpleValues;

    public static List<AuthScopeRestrictionVo> convertToScopeRestrictionVo(List<AuthRoleScopeDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream()
                .filter(e -> e.getRestriction() != null)
                .distinct()
                .map(e -> new AuthScopeRestrictionVo(e.getRestriction(), e.getSimpleValues(), e.getTargets()))
                .collect(Collectors.toList());
    }
}