create table if not exists tenant_base_info
(
    tenant_id   bigint      not null ,
    tenant_name varchar(50) not null ,
    tenant_code varchar(50) ,
    logo        varchar(200) ,
    corp_id     bigint ,
    corp_code   varchar(50) ,
    create_by   bigint      not null ,
    create_time bigint      not null ,
    update_by   bigint ,
    update_time bigint ,
    deleted     int              default 0 not null ,
    status      int     not null default 1 ,
    primary key (tenant_id)
);


COMMENT
ON TABLE tenant_base_info IS '租户基本信息表';
COMMENT
ON COLUMN tenant_base_info.tenant_id IS '租户ID';
COMMENT
ON COLUMN tenant_base_info.tenant_name IS '租户名称';
COMMENT
ON COLUMN tenant_base_info.tenant_code IS '租户代码';
COMMENT
ON COLUMN tenant_base_info.logo IS '租户logo';
COMMENT
ON COLUMN tenant_base_info.corp_id IS '集团公司ID';
COMMENT
ON COLUMN tenant_base_info.corp_code IS '集团公司唯一编码';
COMMENT
ON COLUMN tenant_base_info.create_by IS '创建人';
COMMENT
ON COLUMN tenant_base_info.create_time IS '创建时间';
COMMENT
ON COLUMN tenant_base_info.update_by IS '修改人';
COMMENT
ON COLUMN tenant_base_info.update_time IS '修改时间';
COMMENT
ON COLUMN tenant_base_info.deleted IS '删除状态 0 未删除 1 已删除';
COMMENT
ON COLUMN tenant_base_info.deleted IS '租户状态';
