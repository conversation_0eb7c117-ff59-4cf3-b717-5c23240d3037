package com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthRoleGroupRulePo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/9
 **/
public interface AuthRoleGroupRuleDao extends BaseMapper<AuthRoleGroupRulePo> {

    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    Integer insertBatchSomeColumn(List<AuthRoleGroupRulePo> list);

    /**
     * 获取角色默认的规则id
     *
     * @param roleIdList
     * @return
     */
    @Select("<script>" +
            "SELECT t1.group_rule_id, t1.role_id FROM auth_role_group_rule t1 INNER JOIN " +
            "auth_group_rule t2 ON t1.group_rule_id = t2.id WHERE t2.is_default_role_group = 1 " +
            "<if test='roleIdList != null and roleIdList.size() > 0'> " +
            " AND t1.role_id IN" +
            " <foreach item='item' index='index' collection='roleIdList' open='(' separator=',' close=')'>" +
            "  #{item} " +
            " </foreach>" +
            "</if>" +
            "</script>")
    List<AuthRoleGroupRulePo> getRoleDefauldRuleId(@Param("roleIdList") List<Long> roleIdList);

}
