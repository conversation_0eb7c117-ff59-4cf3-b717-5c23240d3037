package com.caidaocloud.auth.service.caidao.infrastructure.enums;

import com.caidaocloud.em.BaseEnum;

/**
 * 员工状态
 * <AUTHOR>
 */
public enum EmpStatusEnum implements BaseEnum {
    IN_JOB(0, "在职"),
    LEAVE_JOB(1, "离职"),
    PROBATION(2, "试用期");

    private Integer index;
    private String name;

    EmpStatusEnum(Integer index, String name) {
        this.index = index;
        this.name = name;
    }

    public static String getName(Integer index) {
        for (EmpStatusEnum c : EmpStatusEnum.values()) {
            if (c.getIndex().equals(index)) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String showText() {
        return name;
    }

    @Override
    public Object realValue() {
        return index.toString();
    }

    @Override
    public String desc() {
        return null;
    }
}
