package com.caidaocloud.auth.core.scope.handler;

import java.util.List;

import com.caidaocloud.auth.core.dto.AuthRoleScopeFilterDetailDto;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.enums.AuthRoleScopeRestriction;

/**
 *
 * <AUTHOR>
 * @date 2024/1/8
 */
public class DefaultHandler implements ScopeValueHandler{
	@Override
	public List<String> handle(AuthRoleScopeFilterDetailDto scope) {
		try {
			AuthRoleScopeRestriction value = AuthRoleScopeRestriction.valueOf(scope.getRestriction()
					.name());
			return value.toValues(scope.getSimpleValues());
		}catch (IllegalArgumentException e){
			throw new ServerException("Auth code " + scope.getRestriction().name() + "is not supported",e);
		}
	}

}
