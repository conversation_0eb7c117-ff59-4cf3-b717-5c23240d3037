package com.caidaocloud.auth.service.caidao.infrastructure.factory;

import com.caidaocloud.auth.service.caidao.domain.repository.IAuthRoleScopeRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.util.ClassLoaderUtils;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;

public class JDKInvocation<PERSON>andler implements InvocationHandler {
    private Object target;

    public JDKInvocationHandler(Object target) {
        this.target = target;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        return target.getClass().getMethod(method.getName(), ClassLoaderUtils.getClazzByArgs(args)).invoke(target, args);
    }
}