package com.caidaocloud.auth.service.caidao.infrastructure.util;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;

/**
 * <AUTHOR>
 * @date 2022/5/23
 **/
public class UserContextUtil {

    public static UserInfo preCheckUser() {
        UserInfo userInfo = null;
        if (null == userInfo) {
            SecurityUserInfo ui = SecurityUserUtil.getSecurityUserInfo();
            if (null != ui) {
                userInfo = new UserInfo();
                userInfo.setTenantId(ui.getTenantId());
                userInfo.doSetUserId(ui.getUserId());
            }
        }
        PreCheck.preCheckArgument(userInfo == null, "Login invalid");
        return userInfo;
    }

    public static String getUserId() {
        UserInfo userInfo = UserContextUtil.preCheckUser();
        return null == userInfo || null == userInfo.getUserid() ? null : userInfo.getUserid().toString();
    }

    public static String getTenantId() {
        UserInfo userInfo = UserContextUtil.preCheckUser();
        return null == userInfo || null == userInfo.getTenantId() ? null : userInfo.getTenantId();
    }

}
