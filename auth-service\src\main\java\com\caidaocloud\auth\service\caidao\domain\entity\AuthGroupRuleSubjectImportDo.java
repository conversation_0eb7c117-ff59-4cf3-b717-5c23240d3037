package com.caidaocloud.auth.service.caidao.domain.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.util.List;

@Data
public class AuthGroupRuleSubjectImportDo {

    private Long subjectId;

    @Excel(name="账号", width = 15, orderNum = "1")
    private String account;

    @Excel(name="所属角色", width = 20, orderNum = "2")
    private String roleNames;

    private List<Long> roleIds;

    private boolean checkFailFlag = false;

    @Excel(name = "错误原因", width = 30, orderNum = "3")
    private String checkFailTips;

}
