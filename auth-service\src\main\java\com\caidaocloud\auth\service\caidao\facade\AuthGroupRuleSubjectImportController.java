package com.caidaocloud.auth.service.caidao.facade;

import com.caidaocloud.auth.service.caidao.application.service.AuthGroupRuleSubjectImportService;
import com.caidaocloud.dto.importdto.ImportExcelDto;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.vo.ImportExcelProcessVo;
import com.caidaocloud.vo.ImportExcelVo;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Api(tags = "权限员工")
@Controller
@RequestMapping("/api/auth/v1/subject")
public class AuthGroupRuleSubjectImportController {

    @Autowired
    private ISessionService sessionService;

    @Autowired
    private AuthGroupRuleSubjectImportService authGroupRuleSubJectImportService;

    @ApiOperation(value = "角色用户导入")
    @PostMapping("/importData")
    @ResponseBody
    public Result<ImportExcelVo> importData(ImportExcelDto dto) throws IOException {
//        DataImportService service = getDataImportServiceByCode(dto.getExcelCode());
        String tenantId = sessionService.getUserInfo().getTenantId();
        ImportExcelVo vo = authGroupRuleSubJectImportService.importDataWithExcel(dto);
        Long userId = sessionService.getUserInfo().getUserId();
        authGroupRuleSubJectImportService.operateDataFromInputStream(dto.getFile()
                .getInputStream(), vo.getProcessUUid(), userId, tenantId, SecurityUserUtil.getSecurityUserInfo()
                .getRole());
        return Result.ok(vo);
    }


    @ApiOperation(value = "根据导入id查询进度信息")
    @GetMapping("/getImportPercentage")
    @ResponseBody
    public Result<ImportExcelProcessVo> getImportPercentage(@RequestParam("processId") String processId, @RequestParam("excelCode") String excelCode) {
        return Result.ok(authGroupRuleSubJectImportService.getImportDataPercentage(processId));
    }

    @ApiOperation(value = "根据导入id下载错误数据", produces="application/octet-stream")
    @GetMapping("/downloadErrorImportInfo")
    @ResponseBody
    public void downloadErrorImportInfo(HttpServletResponse response, @RequestParam(value = "processId") String processId, @RequestParam("excelCode") String excelCode) {
        authGroupRuleSubJectImportService.downloadErrorImportData(response, processId);
    }

}
