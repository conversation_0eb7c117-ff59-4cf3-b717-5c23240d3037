package com.caidaocloud.auth.service.caidao.application.feign;

import com.caidaocloud.auth.service.caidao.application.dto.bi.MenuDto;
import com.caidaocloud.auth.service.caidao.application.feign.fallback.BiFeginFallback;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

@FeignClient(value = "${feign.rename.caidaocloud-bi-service:caidaocloud-bi-service}",
        fallback = BiFeginFallback.class,
        configuration = FeignConfiguration.class)
public interface BiFegin {
    @GetMapping("/api/bi/menu/v1/list")
    Result<List<MenuDto>> allMenus(String tenantId);
}