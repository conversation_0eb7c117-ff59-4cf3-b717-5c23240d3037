package com.caidaocloud.auth.service.caidao.application.service;

import com.caidaocloud.auth.core.dto.AuthRoleScopeTargetRegisterDto;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleScopeTargetDo;
import com.caidaocloud.util.ObjectConverter;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 *
 * <AUTHOR>
 * @date 2024/8/14
 */
@Service
public class AuthRoleScopeTargetRegisterService {
	@Transactional
	public void register(AuthRoleScopeTargetRegisterDto dto) {
		AuthRoleScopeTargetDo targetDo = ObjectConverter.convert(dto, AuthRoleScopeTargetDo.class);
		targetDo.save();
	}
}
