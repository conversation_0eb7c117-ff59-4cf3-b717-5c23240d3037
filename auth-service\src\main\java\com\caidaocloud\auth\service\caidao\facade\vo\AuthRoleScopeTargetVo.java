package com.caidaocloud.auth.service.caidao.facade.vo;

import java.util.List;

import com.caidaocloud.auth.core.enums.AuthRoleScopeRestriction;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @date 2024/8/13
 */
@Data
@ApiModel("数据范围权限vo")
@NoArgsConstructor
@AllArgsConstructor
public class AuthRoleScopeTargetVo {
	private String code;
	private String name;
}
