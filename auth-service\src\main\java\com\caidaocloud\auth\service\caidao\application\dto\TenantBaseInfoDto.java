package com.caidaocloud.auth.service.caidao.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("租户信息")
public class TenantBaseInfoDto {
    @ApiModelProperty("租户ID")
    private Long tenantId;
    @ApiModelProperty("租户名称")
    private String tenantName;
    @ApiModelProperty("租户代码")
    private String tenantCode;
    @ApiModelProperty("租户logo")
    private String logo;
    @ApiModelProperty("集团公司ID")
    private Long corpId;
    @ApiModelProperty("集团公司唯一编码")
    private String corpCode;
    @ApiModelProperty("创建人")
    private Long createBy;
    @ApiModelProperty("创建时间")
    private Long createTime;
    @ApiModelProperty("修改人")
    private Long updateBy;
    @ApiModelProperty("修改时间")
    private Long updateTime;
    @ApiModelProperty("删除状态 0 未删除 1 已删除")
    private Integer deleted;
    @ApiModelProperty("状态: 0 无效 1 正常 2 封存")
    private Integer status;
}
