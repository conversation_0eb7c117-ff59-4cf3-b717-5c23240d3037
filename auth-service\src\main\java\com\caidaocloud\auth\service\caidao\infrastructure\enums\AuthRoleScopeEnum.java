package com.caidaocloud.auth.service.caidao.infrastructure.enums;

import com.caidaocloud.auth.service.caidao.domain.repository.IAuthRoleScopeRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.impl.AuthReportScopeRepositoryImpl;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.impl.AuthRoleScopeRepositoryImpl;

public enum AuthRoleScopeEnum {
    DATA_SCOPE(AuthRoleScopeRepositoryImpl.class),
    REPORT_SCOPE(AuthReportScopeRepositoryImpl.class);

    public Class<? extends IAuthRoleScopeRepository> aClass;

    AuthRoleScopeEnum(Class<? extends IAuthRoleScopeRepository> aClass) {
        this.aClass = aClass;
    }
}