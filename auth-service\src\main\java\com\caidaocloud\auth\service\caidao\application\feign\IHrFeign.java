package com.caidaocloud.auth.service.caidao.application.feign;

import com.caidaocloud.auth.service.caidao.application.dto.hr.EmpSearchColumnsDto;
import com.caidaocloud.auth.service.caidao.application.feign.fallback.HrFeignFallback;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * created by: FoAng
 * create time: 8/12/2022 3:51 下午
 */
@FeignClient(
        value = "${feign.rename.caidaocloud-hr-service:caidaocloud-hr-service}",
        fallback = HrFeignFallback.class,
        configuration = FeignConfiguration.class,
        contextId = "hr-service"
)
public interface IHrFeign {


    @PostMapping("/api/hr/emp/work/v1/searchEmpColumnsPage")
    Result<PageResult<Map<String, String>>> searchEmpColumnsPage(@RequestBody EmpSearchColumnsDto empSearchDto);
}
