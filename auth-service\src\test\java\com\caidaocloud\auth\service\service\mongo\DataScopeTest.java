package com.caidaocloud.auth.service.service.mongo;

import com.caidaocloud.auth.service.caidao.application.service.AuthGroupRuleSubjectService;
import com.caidaocloud.auth.service.caidao.facade.vo.workflow.AuthRoleScopeFilterDetail;
import com.caidaocloud.web.Result;
import lombok.val;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
public class DataScopeTest {
    @Resource
    private AuthGroupRuleSubjectService authGroupRuleSubjectService;
    public void testGet(){
        val scopes = authGroupRuleSubjectService.getScopeBySubjectId(1433965157988360L);
        Result.ok(AuthRoleScopeFilterDetail.fromStandard(scopes, "entity.hr.EmpWorkInfo", null));
    }
}
