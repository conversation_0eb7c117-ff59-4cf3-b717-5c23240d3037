package com.caidaocloud.auth.service.caidao.application.cron;

import com.caidaocloud.auth.service.caidao.application.service.AuthGroupRuleService;
import com.caidaocloud.auth.service.caidao.domain.service.AuthGroupRuleDomainService;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthGroupRulePo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class AuthGroupRuleTask {


    @Value("${caidaocloud.tenant:8}")
    private List<String> tenantList;

    @Autowired
    private AuthGroupRuleDomainService authGroupRuleDomainService;

    @Autowired
    private AuthGroupRuleService authGroupRuleService;

    /**
     * 同步角色组下的员工
     * @return
     */
    @XxlJob("authGroupRuleJobHandler")
    public ReturnT<String> authRoleGroupRuleJobHandler() {
        XxlJobHelper.log("XxlJob authGroupRuleJobHandler start");
        log.info("cronTask[authGroupRuleJobHandler]------------------------start execution,time {}", System.currentTimeMillis());
        tenantList.forEach(tenantId -> {
            try {
                SecurityUserInfo userInfo = new SecurityUserInfo();
                userInfo.setTenantId(tenantId);
                userInfo.setUserId(0L);
                userInfo.setEmpId(0L);
                SecurityUserUtil.setSecurityUserInfo(userInfo);
                /**
                 * 1、没有日志
                 * 2、一条数据失败，后续都不处理
                 */
                synGroupRuleEmp();
            } catch (Exception e){
                log.error("authRoleGroupRuleJobHandler Scheduled Tasks err,{}", e.getMessage(), e);
            } finally {
                SecurityUserUtil.removeSecurityUserInfo();
            }
        });
        log.info("cronTask[authGroupRuleJobHandler]------------------------end execution,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob authGroupRuleJobHandler end");
        return ReturnT.SUCCESS;
    }

    private void synGroupRuleEmp() {
        QueryPageBean queryPageBean = new QueryPageBean();
        int pageNo = 1, pageSize = 100;
        queryPageBean.setPageSize(pageSize);
        PageResult<AuthGroupRulePo> pageResult = null;
        do {

            queryPageBean.setPageNo(pageNo);

            pageResult = authGroupRuleDomainService.pageList(queryPageBean);

            authGroupRuleService.synRuleSubject(pageResult.getItems());

        } while (pageNo++ * pageSize < pageResult.getTotal());




    }


}
