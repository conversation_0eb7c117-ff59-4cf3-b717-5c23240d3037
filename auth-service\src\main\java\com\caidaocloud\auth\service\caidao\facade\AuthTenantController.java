package com.caidaocloud.auth.service.caidao.facade;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import com.caidaocloud.auth.service.caidao.application.service.AuthInitTableService;
import com.caidaocloud.auth.service.caidao.application.service.TenantBaseInfoService;
import com.caidaocloud.auth.service.caidao.application.dto.SyncTenantBaseInfoDto;
import com.caidaocloud.auth.service.caidao.application.enums.DataOpEnum;
import com.caidaocloud.auth.service.caidao.domain.entity.TenantBaseInfoDo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2022/5/7
 **/
@Api(tags = "租户操作")
@Slf4j
@Controller
@RequestMapping("/api/auth/v1/tenant")
public class AuthTenantController {
    @Autowired
    private AuthInitTableService oAuthInitTableService;

    @Autowired
    private TenantBaseInfoService tenantBaseInfoService;

    @Autowired
    private RedisTemplate redisTemplate;

    @ApiOperation(value = "初始化租户")
    @PostMapping("/init")
    @ResponseBody
    public Result<String> initTenant() {
        oAuthInitTableService.initTable();
        return Result.ok("success");
    }

    @ApiOperation(value = "初始化admin角色")
    @PostMapping("/init/admin")
    @ResponseBody
    public Result<String> initAdmin(@RequestParam("tenantId") String tenantId,@RequestParam("userId") String userId) {
        oAuthInitTableService.initAdmin(userId);
        return Result.ok("success");
    }

    @ApiOperation(value = "初始化config角色")
    @PostMapping("/init/config")
    @ResponseBody
    public Result<String> initConfig(@RequestParam("tenantId") String tenantId,@RequestParam("userId") String userId) {
        oAuthInitTableService.initConfig(userId);
        return Result.ok("success");
    }

    @PostMapping("/sync")
    @ApiOperation("同步保存租户信息")
    @ResponseBody
    public Result<Boolean> syncTenant(@RequestBody SyncTenantBaseInfoDto dto) {
        try {
            PreCheck.preCheckArgument(dto == null, "Data is empty");
            PreCheck.preCheckArgument(dto.getTenantId() == null, "租户ID为空");
            if (dto.getOp() == DataOpEnum.DELETE) {
                tenantBaseInfoService.deleteByIds(new ArrayList<>(Arrays.asList(dto.getTenantId())));
            } else if (dto.getOp() == DataOpEnum.INSERT || dto.getOp() == DataOpEnum.UPDATE) {
                List<TenantBaseInfoDo> list = new ArrayList<>(Arrays.asList(ObjectConverter.convert(dto, TenantBaseInfoDo.class)));

                // 过滤出：无效状态的数据，进行逻辑删除
//                List<TenantBaseInfoDo> delList = list.stream().
//                        filter(o -> o.getStatus() != null && o.getStatus() == 0).collect(Collectors.toList());
//                if (!CollectionUtils.isEmpty(delList)) {
//                    List<Long> delIds = delList.stream().map(TenantBaseInfoDo::getTenantId).distinct().collect(Collectors.toList());
//                    tenantBaseInfoService.softDeleteByIds(delIds);
//                    list.removeAll(delList);
//                }

//                if (CollectionUtils.isEmpty(list)) {
//                    return Result.ok(true);
//                }
                tenantBaseInfoService.syncSave(list);
            } else {
                return Result.fail("Op Field value can only be [INSERT UPDATE DELETE]");
            }
        } catch (Exception e) {
            log.error("TenantBaseInfoController.syncTenant error msg:{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
        return Result.ok(true);
    }

}
