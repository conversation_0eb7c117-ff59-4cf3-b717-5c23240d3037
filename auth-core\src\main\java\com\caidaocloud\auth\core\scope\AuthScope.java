package com.caidaocloud.auth.core.scope;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 *
 * <AUTHOR>
 * @date 2024/1/3
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface AuthScope {


	/**
	 * 填写auth服务对应的identifier
	 * @return the method name
	 */
	String value();

	AuthScopeProperty[] property() default {};
}
