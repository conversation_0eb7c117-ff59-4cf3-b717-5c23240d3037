package com.caidaocloud.auth.service.caidao.application.service;

import com.caidaocloud.auth.service.caidao.domain.entity.OAuthCodeStoreDo;
import com.caidaocloud.auth.service.caidao.domain.repository.OAuthCodeStoreRepository;
import com.caidaocloud.util.FastjsonUtil;
import org.springframework.security.oauth2.common.util.RandomValueStringGenerator;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.security.oauth2.provider.code.RandomValueAuthorizationCodeServices;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 授权码 code 存储在 mongo 中
 * <AUTHOR>
 * @date 2021-11-11
 */
@Service
public class MongoAuthorizationCodeServices extends RandomValueAuthorizationCodeServices {
    private RandomValueStringGenerator generator = new RandomValueStringGenerator(32);

    @Resource
    private OAuthCodeStoreRepository oAuthCodeStoreRepository;

    /**
     * 默认授权码 code 为 6 位，才到自定义位 32 位
     * @param authentication
     * @return
     */
    @Override
    public String createAuthorizationCode(OAuth2Authentication authentication) {
        String code = this.generator.generate();
        this.store(code, authentication);
        return code;
    }

    /**
     * 存储 code
     * @param code
     * @param authentication
     */
    @Override
    protected void store(String code, OAuth2Authentication authentication) {
        String jsonAuth = FastjsonUtil.toJson(authentication);

        OAuthCodeStoreDo codeStorePo = new OAuthCodeStoreDo();
        codeStorePo.setAuthentication(jsonAuth);
        codeStorePo.setCode(code);
        codeStorePo.setCreateTime(System.currentTimeMillis());
        OAuth2Request oAuth2Request = authentication.getOAuth2Request();
        codeStorePo.setClientId(null != oAuth2Request ? oAuth2Request.getClientId() : "clentId");

        oAuthCodeStoreRepository.insertAuthCode(codeStorePo);
    }

    @Override
    protected OAuth2Authentication remove(String code) {
        return null;
    }
}
