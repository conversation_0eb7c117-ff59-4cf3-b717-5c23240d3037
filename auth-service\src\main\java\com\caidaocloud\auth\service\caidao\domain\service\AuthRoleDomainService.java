package com.caidaocloud.auth.service.caidao.domain.service;

import com.caidaocloud.auth.service.caidao.application.dto.AuthRoleAndRuleDto;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleAndRuleDo;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleDo;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRolePermissionDo;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthRoleRepository;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.excption.ServerException;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/10
 **/
@Service
public class AuthRoleDomainService {

    @Resource
    private IAuthRoleRepository oAuthRoleRepository;

    public Long saveOrUpdateRole(AuthRoleDo authRoleDo) {
        if (authRoleDo.getId() != null) {
            Option<AuthRoleDo> role = oAuthRoleRepository.getRoleByRoleId(authRoleDo.getId());
            if (role.isEmpty()) {
                throw new ServerException("role is not exist");
            }
            role.get().checkConfigRole();
        }
        return oAuthRoleRepository.saveOrUpdateRole(authRoleDo);
    }

    public void deleteRole(List<Long> roleIdList) {
        for (Long roleId : roleIdList) {
            Option<AuthRoleDo> role = oAuthRoleRepository.getRoleByRoleId(roleId);
            if (role.isEmpty()) {
                throw new ServerException("role is not exist");
            }
            role.get().delete();
        }
    }

    public Long copyRole(Long roleId) {
        return oAuthRoleRepository.copyRole(roleId);
    }

    public PageResult<AuthRoleDo> getPageOfRole(QueryPageBean queryPageBean) {
        val authRoleDo = new AuthRoleDo();
        return authRoleDo.getPage(queryPageBean);
    }

    public List<AuthRoleDo> getAuthRoleByCode(List<String> codeList) {
        return AuthRoleDo.getAuthRoleByCode(codeList);
    }

    public Option<AuthRoleDo> getRoleByRoleId(Long roleId) {
        return AuthRoleDo.getById(roleId);
    }

    public List<String> getResourceCodeListByRoleIdList(List<Long> roleIdList) {
        return oAuthRoleRepository.getResourceCodeListByRoleIdList(roleIdList);
    }

    public Map<String, String> getResourceScopeDetailBySubjectId(List<Long> roleIdList, String parentCode) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Maps.map();
        }
        return oAuthRoleRepository.getResourceScopeDetailBySubjectId(roleIdList, parentCode);
    }

    public List<String> getResourceCodeBySubjectId(List<Long> roleIdList, String parentCode) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return Lists.newArrayList();
        }
        return oAuthRoleRepository.getResourceCodeByRolesAndParentCode(roleIdList, parentCode);
    }

    public List<String> getResourceUrlListByRoleIdList(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            throw new ServerException("this user has no authorization");
        }
        return oAuthRoleRepository.getResourceUrlListByRoleIdList(roleIdList);
    }

    public List<AuthRolePermissionDo> getPermissionOfRole(Long roleId) {
        return oAuthRoleRepository.getPermissionOfRole(roleId);
    }

    public List<AuthRoleDo> getRoleIdAndNameByRoleName(List<String> roleNameList) {
        return oAuthRoleRepository.getRoleIdAndNameByRoleName(roleNameList);
    }

    public boolean isExistByRoleName(String roleName) {
        return oAuthRoleRepository.isExistByRoleName(roleName);
    }

    public boolean isExistByRoleName(String roleName, Long excludeId) {
        return oAuthRoleRepository.isExistByRoleName(roleName, excludeId);
    }

    public List<AuthRoleAndRuleDto> getRoleByRuleIds(List<Long> ruleIds) {
        List<AuthRoleAndRuleDo> roleAndRuleDoList = oAuthRoleRepository.getRoleByRuleIds(ruleIds);
        return Sequences.sequence(roleAndRuleDoList).map(e -> {
            var authRoleDto = new AuthRoleAndRuleDto();
            BeanUtils.copyProperties(e, authRoleDto);
            return authRoleDto;
        }).toList();
    }

    public List<AuthRoleDo> getAllRole() {
        var allRole = oAuthRoleRepository.getAllRole();
        if (allRole.isEmpty()) {
            return Lists.newArrayList();
        }
        return allRole.toList();
    }

    public void deleteRolePermission(String code) {
        if (StringUtils.isBlank(code)) {
            throw new ServerException("code is empty");
        }
        oAuthRoleRepository.deleteRolePermission(code);
    }

}
