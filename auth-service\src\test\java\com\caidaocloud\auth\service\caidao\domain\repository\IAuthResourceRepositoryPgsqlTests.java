package com.caidaocloud.auth.service.caidao.domain.repository;

import java.util.List;

import com.caidaocloud.auth.service.AuthApplication;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthResourceDo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum.FUNC;
import static com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum.MENU;
import static org.junit.Assert.*;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/5/5
 */
@SpringBootTest(classes = AuthApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
@Slf4j
public class IAuthResourceRepositoryPgsqlTests {
	@Autowired
	private IAuthResourceRepository repository;

	private AuthResourceDo resourceDo;
	private List<AuthResourceDo> resourceDoList;

	@Before
	public void setUp() throws Exception {
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("8");
		userInfo.setUserId(0L);
		userInfo.setEmpId(0L);
		SecurityUserUtil.setSecurityUserInfo(userInfo);

		String json = IOUtils.toString(new ClassPathResource("src/test/resources/testAuthResource.json").getInputStream(), "utf-8");
		List<AuthResourceDo> resourceDos = FastjsonUtil.toList(json, AuthResourceDo.class);
		resourceDo = resourceDos.remove(0);
		resourceDoList = resourceDos;

	}

	@Test
    public void selectTest() {
		repository.selectUrlOfFilterGateway();
		log.info("{}",repository.list(MENU, null));
		log.info("{}",repository.list(FUNC, "companylist"));
		log.info("{}",repository.loadByCode("companyadd1"));
		log.info("{}",repository.loadByCodes(Lists.list("companylist","companyadd1")));
		log.info("{}", repository.loadByCode("companyadd1", "zh", SecurityUserUtil.getSecurityUserInfo()
				.getTenantId()));
    }

	@Test
	public void saveTest() {
		repository.insert(resourceDo);
		repository.insertBatch(resourceDoList);
		List<AuthResourceDo> dbData = repository.list(FUNC, null);
		for (AuthResourceDo data : dbData) {
			data.setId(null);
		}
		List<AuthResourceDo> exceptData = Lists.list(resourceDo);
		exceptData.addAll(resourceDoList);
		assertArrayEquals(exceptData.toArray(), dbData.toArray());

	}

	@After
	public void remove(){
		repository.remove(resourceDo.getCode());
		for (AuthResourceDo authResourceDo : resourceDoList) {
			repository.remove(authResourceDo.getCode());

		}

	}
}