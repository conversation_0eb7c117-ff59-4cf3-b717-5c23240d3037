package com.caidaocloud.auth.service.caidao.application.dto.subject;

import com.caidaocloud.dto.QueryPageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/22
 **/
@Data
@ApiModel(value = "多个角色查看用户分页dto")
public class AuthSubjectPageByRolesDto {

    @ApiModelProperty("角色ids")
    private List<Long> roleIds;

    @ApiModelProperty("分页对象")
    private QueryPageBean queryPageBean;

}