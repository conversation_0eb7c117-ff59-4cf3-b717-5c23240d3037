package com.caidaocloud.auth.service.caidao.infrastructure.repository.impl;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.auth.service.caidao.domain.entity.OAuthClientDo;
import com.caidaocloud.auth.service.caidao.domain.repository.OAuthClientRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.OAuthClientDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.OAuthClientPo;
import com.caidaocloud.util.ObjectConverter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 查询第三方应用ClientDetail
 * <AUTHOR>
 * @date 2021-11-22
 */
@Repository
public class OAuthClientRepositoryImpl implements OAuthClientRepository {

    @Autowired
    private OAuthClientDao dao;

    @Override
    public List<OAuthClientDo> selectListAll() {
        QueryWrapper<OAuthClientPo> wrapper = new QueryWrapper<>();
        List<OAuthClientPo> list = dao.selectList(wrapper);
        return ObjectConverter.convertList(list, OAuthClientDo.class);
    }

    @Override
    public OAuthClientDo selectByClientId(String clientId) {
        LambdaQueryWrapper<OAuthClientPo> wrapper = new QueryWrapper<OAuthClientPo>().lambda();
        wrapper.eq(OAuthClientPo::getClientId, clientId);
        OAuthClientPo data = dao.selectOne(wrapper);
        return ObjectConverter.convert(data, OAuthClientDo.class);
    }
}
