package com.caidaocloud.auth.service;

import com.caidaocloud.auth.service.caidao.application.service.AuthGroupRuleSubjectService;
import com.caidaocloud.auth.service.caidao.application.service.AuthRoleService;
import com.caidaocloud.auth.service.caidao.facade.AuthGroupRuleSubjectController;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Map;

@SpringBootTest(classes = AuthApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class AuthDataScopeTest {
    @Autowired
    private AuthGroupRuleSubjectService authGroupRuleSubjectService;
    @Autowired
    private AuthGroupRuleSubjectController authGroupRuleSubjectController;

    @Test
    public void availableTest(){

        Map standardAuthScope = authGroupRuleSubjectService.getStandardAuthScope();
        System.out.println(FastjsonUtil.toJson(standardAuthScope));
    }

    @Before
    public void setUp() throws Exception {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        userInfo.setUserId(0L);
        userInfo.setEmpId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }

    @Test
    public void load(){
        authGroupRuleSubjectService.getStandardAuthScope();
    }

    @Test
    public void scopeTest(){
        authGroupRuleSubjectController.getScopeBySubject("entity.hr.EmpWorkInfo", 1715461739550728L, "false");
    }

    @Test
    public void testRefreshRole(){
        //SpringUtil.getBean(AuthRoleService.class).moduleRefresh(3L);
        SpringUtil.getBean(AuthRoleService.class).moduleRefresh(1L);
    }
}
