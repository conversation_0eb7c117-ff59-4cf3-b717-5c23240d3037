package com.caidaocloud.auth.service.caidao.infrastructure.entity;

import com.caidaocloud.auth.service.caidao.infrastructure.enums.tree.ConditionNodeRelationEnum;
import com.caidaocloud.auth.service.common.infrastructure.config.mybatis.type.JsonSerializeInterface;
import com.caidaocloud.hrpaas.paas.match.ConditionNode;
import com.googlecode.totallylazy.Sequences;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 条件对象
 *
 * <AUTHOR>
 * @date 2022/5/10
 **/
@Data
public class ConditionTree extends JsonSerializeInterface implements Serializable {
    private String id;
    private ConditionNodeRelationEnum relation;
    private List<ConditionNode> children;


    public boolean match(Map<String, String> preEmp) {
        if(ConditionNodeRelationEnum.and.equals(relation)){
            return Sequences.sequence(children).forAll(it->it.match(preEmp));
        }else{
            return Sequences.sequence(children).exists(it->it.match(preEmp));
        }
    }


}
