package com.caidaocloud.auth.service.caidao.domain.entity;

import com.caidaocloud.auth.service.caidao.domain.repository.IAuthGroupRuleRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.entity.ConditionTree;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthGroupRulePo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/10
 **/
@Data
public class AuthRoleGroupDo {

    private Long id;

    private String name;

    private String i18nName;

    private String roleId;

    private String roleName;

    private String expressionName;

    private String remark;

    private ConditionTree expression;

    private List<Long> roleIdList;

    private Boolean isDefaultRoleGroup;

    private Long ruleId;

    private static IAuthGroupRuleRepository repository() {
        return SpringUtil.getBean(IAuthGroupRuleRepository.class);
    }

    public PageResult<AuthGroupRulePo> getPage(QueryPageBean queryPageBean) {
        IAuthGroupRuleRepository repository = repository();
        if (repository == null) {
            throw new ServerException("not found IAuthGroupRuleRepository");
        }
        return repository.getPage(queryPageBean);

    }
}
