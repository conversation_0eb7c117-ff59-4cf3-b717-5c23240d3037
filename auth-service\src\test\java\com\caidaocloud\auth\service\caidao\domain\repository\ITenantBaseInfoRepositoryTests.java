package com.caidaocloud.auth.service.caidao.domain.repository;

import java.lang.reflect.Field;

import com.caidaocloud.auth.service.AuthApplication;
import com.caidaocloud.auth.service.caidao.domain.entity.TenantBaseInfoDo;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.service.SessionServiceImpl;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.RequestHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/5/6
 */
@SpringBootTest(classes = AuthApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
@Slf4j
public class ITenantBaseInfoRepositoryTests {

	@Autowired
	private ITenantBaseInfoRepository repository;

	@Before
	public void setUp() throws Exception {
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("8");
		userInfo.setUserId(0L);
		userInfo.setEmpId(0L);
		SecurityUserUtil.setSecurityUserInfo(userInfo);

		RequestHelper.getRequest()
				.setAttribute("_caidaocloud_userinfo_request_threadlocal_", ObjectConverter.convert(userInfo, UserInfo.class));

// Get the instance of SessionServiceImpl which is already created by Spring bean
		SessionServiceImpl service = (SessionServiceImpl) SpringUtil.getBean(ISessionService.class);
		try {
			// Get the private field "threadLocalCache" from the SessionServiceImpl class
			Field field = SessionServiceImpl.class.getDeclaredField("threadLocalCache");
			// Set the accessibility of the field to true, as it is private
			field.setAccessible(true);
			// Set the value of the "threadLocalCache" field to true
			field.set(service, true);
		}
		catch (NoSuchFieldException | IllegalAccessException e) {
			e.printStackTrace();
		}
	}
	@Test
	public void insert() {
		TenantBaseInfoDo tenantBaseInfoDo = new TenantBaseInfoDo(
				123456L, "Test Tenant", "test123", 7890L, "abc123"
		);
		tenantBaseInfoDo.setCreateBy(SecurityUserUtil.getSecurityUserInfo().getUserId());
		// Set createTime using the current timestamp
		tenantBaseInfoDo.setCreateTime(System.currentTimeMillis());
		repository.insert(tenantBaseInfoDo);

		// Assert the result
		TenantBaseInfoDo result = repository.getTenantByCode(tenantBaseInfoDo.getTenantCode());
		assertNotNull(result);
		assertEquals(tenantBaseInfoDo.getTenantId(), result.getTenantId());
		assertEquals(tenantBaseInfoDo.getTenantName(), result.getTenantName());
		assertEquals(tenantBaseInfoDo.getTenantCode(), result.getTenantCode());
		assertEquals(tenantBaseInfoDo.getCorpId(), result.getCorpId());
		assertEquals(tenantBaseInfoDo.getCorpCode(), result.getCorpCode());
		assertNull(result.getLogo());
		assertEquals(1, result.getStatus().intValue());
		log.info("Result after Get Tenant By Code: {}", result); // log added here

		// Update the created object
		tenantBaseInfoDo.setTenantName("Updated Tenant Name");
		tenantBaseInfoDo.setCorpCode("Updated Corp Code");
		// Update all other properties of TenantBaseInfoDo object
		repository.update(tenantBaseInfoDo);

		// Assert the result
		result = repository.getTenantByCode(tenantBaseInfoDo.getTenantCode());
		assertNotNull(result);
		assertEquals(tenantBaseInfoDo.getTenantId(), result.getTenantId());
		assertEquals(tenantBaseInfoDo.getTenantName(), result.getTenantName());
		assertEquals(tenantBaseInfoDo.getTenantCode(), result.getTenantCode());
		assertEquals(tenantBaseInfoDo.getCorpId(), result.getCorpId());
		assertEquals(tenantBaseInfoDo.getCorpCode(), result.getCorpCode());
		assertNull(result.getLogo());
		assertEquals(1, result.getStatus().intValue());
		log.info("Result after Get Tenant By Code: {}", result); // log added here

		// Delete the inserted object using a delete api
		repository.delete(Lists.newArrayList(tenantBaseInfoDo.getTenantId()));

		// Assert the result
		result = repository.getTenantByCode(tenantBaseInfoDo.getTenantCode());
		assertNull(result);
		log.info("Result after Get Tenant By Code (to Assert Deletion): {}", result); // log added here
	}

}