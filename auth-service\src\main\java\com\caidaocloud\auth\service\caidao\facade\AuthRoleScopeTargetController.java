package com.caidaocloud.auth.service.caidao.facade;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import com.caidaocloud.auth.core.dto.AuthRoleScopeTargetRegisterDto;
import com.caidaocloud.auth.service.caidao.application.dto.AuthResourceDto;
import com.caidaocloud.auth.service.caidao.application.dto.SyncTenantBaseInfoDto;
import com.caidaocloud.auth.service.caidao.application.enums.DataOpEnum;
import com.caidaocloud.auth.service.caidao.application.service.AuthInitTableService;
import com.caidaocloud.auth.service.caidao.application.service.AuthRoleScopeTargetRegisterService;
import com.caidaocloud.auth.service.caidao.application.service.TenantBaseInfoService;
import com.caidaocloud.auth.service.caidao.domain.entity.TenantBaseInfoDo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/5/7
 **/
@Api(tags = "数据权限")
@Slf4j
@RestController
@RequestMapping("/api/auth/v1/scope")
public class AuthRoleScopeTargetController {
    @Resource
    private AuthRoleScopeTargetRegisterService authRoleScopeTargetRegisterService;

    @PostMapping("register")
    public Result register(@RequestBody AuthRoleScopeTargetRegisterDto dto) {
        authRoleScopeTargetRegisterService.register(dto);
        return Result.ok();
    }

}
