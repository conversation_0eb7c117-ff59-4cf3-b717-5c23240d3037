package com.caidaocloud.auth.service.caidao.domain.repository;

import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleGroupDo;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthGroupRulePo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/9
 **/
public interface IAuthGroupRuleRepository {

    /**
     * 保存或更新角色组
     *
     * @param authRoleGroupDo
     */
    void saveOrUpdateRoleGroup(AuthRoleGroupDo authRoleGroupDo);


    AuthGroupRulePo getGroupRuleById(Long id);


    int deleteGroupRule(List<Long> idList);


    PageResult<AuthGroupRulePo> getPage(QueryPageBean queryPageBean);

    List<AuthGroupRulePo> getGroupRuleByIds(List<Long> ids);
}
