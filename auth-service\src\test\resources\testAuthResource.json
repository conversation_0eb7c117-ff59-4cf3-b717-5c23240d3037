[{"tenant_id": "0", "name": "公司管理编辑测试", "code": "companyupdateTest1", "lang": "zh", "category": "FUNC", "url": "/api/hr/company/v1/detail", "resource_action": "VIEW", "parent_code": "companylist", "extension": null, "create_time": 0, "create_by": null, "update_time": null, "update_by": null, "deleted": 0, "is_gateway_filter": 1}, {"tenant_id": "0", "name": "公司管理编辑", "code": "companyupdateTest2", "lang": "zh", "category": "FUNC", "url": "/api/bcc/dict/common/v1/dict/getEnableDictList", "resource_action": "VIEW", "parent_code": "companylist", "extension": null, "create_time": 0, "create_by": null, "update_time": null, "update_by": null, "deleted": 0, "is_gateway_filter": 1}, {"tenant_id": "0", "name": "公司管理编辑", "code": "companyupdateTest3", "lang": "zh", "category": "FUNC", "url": "/api/hr/company/v1/update", "resource_action": "EDIT", "parent_code": "companylist", "extension": null, "create_time": 0, "create_by": null, "update_time": null, "update_by": null, "deleted": 0, "is_gateway_filter": 1}]