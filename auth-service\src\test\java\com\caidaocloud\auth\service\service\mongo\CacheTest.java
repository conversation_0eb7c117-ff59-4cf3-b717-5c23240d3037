package com.caidaocloud.auth.service.service.mongo;

import com.caidaocloud.auth.service.caidao.application.feign.MasterDataFeign;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.impl.AuthResourceRepositoryImpl;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.tried.TriedTree;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class CacheTest {

    @Autowired
    private CacheService cacheService;

    @Autowired
    private RedisTemplate<String, Object> restTemplate;

    @Resource
    private AuthResourceRepositoryImpl authResourceRepository;

    private final String REDIS_KEY = "needGatewayCheck";

    @Test
    public void getList() {
        List<Object> needGatewayCheck = cacheService.getList("needGatewayCheck", 0, -1);
        for (Object o : needGatewayCheck) {
            System.out.println(o);
        }
    }

    @Test
    public void insertList() {
        List<String> urlList = Sequences.sequence(authResourceRepository.selectUrlOfFilterGateway())
                .filter(e -> StringUtils.isNotBlank(e)).toList();
        if (!CollectionUtils.isEmpty(urlList)) {
            Object[] objects = urlList.toArray();
            ListOperations listOperations = restTemplate.opsForList();
            listOperations.rightPushAll(REDIS_KEY, objects);
        }
    }

    @Test
    public void testSet() {
        Boolean member = cacheService.isMemberOfSet(REDIS_KEY, "/api/hr/postmanage/v1/updateStatus");
        System.out.println(member);
    }

    @Test
    public void testSet2() {
        boolean memberSet = cacheService.isMemberOfSet(REDIS_KEY, "/api/hr/postmanage/v1/updateStatus222");
        System.out.println(memberSet);
    }

    @Test
    public void insert() {
        ArrayList<String> strings = Lists.newArrayList("3", "4");
        cacheService.cacheList("test_list",strings );
    }

    @Test
    public void test2() {
        TriedTree tried = new TriedTree();
        tried.insert("hello/aa/ba".toCharArray());
        tried.insert("her".toCharArray());
        tried.insert("hi".toCharArray());
        tried.insert("how/insert/update".toCharArray());
        tried.insert("see".toCharArray());
        tried.insert("api/bcc/dict/common/v1/dict/getEnableDictList".toCharArray());
        tried.insert("/api/hr/dict/v1/getIdTypeList".toCharArray());
        tried.insert("/api/hr/workplace/v1/selectList".toCharArray());
        tried.insert("/api/hr/company/v1/list".toCharArray());
        tried.insert("/api/bcc/dict/common/v1/dict/getEnableDictList".toCharArray());
        tried.insert("/api/hr/company/v1/save".toCharArray());
        tried.insert("/api/hr/company/v1/detail".toCharArray());
        tried.insert("/api/hr/company/v1/update".toCharArray());
        tried.insert("/api/hr/company/v1/delete".toCharArray());
        tried.insert("/api/hr/org/v1/list".toCharArray());
        tried.insert("/api/hr/org/v1/save".toCharArray());
        tried.insert("/api/hr/org/v1/simpleTree".toCharArray());
        tried.insert("/api/hr/cost/v1/simpleTree".toCharArray());
        tried.insert("/api/hr/postmanage/v1/selectList".toCharArray());
        tried.insert("/api/hr/org/v1/detail".toCharArray());
        tried.insert("/api/hr/org/v1/update".toCharArray());
        tried.insert("/api/hr/org/v1/updateStatus".toCharArray());
        tried.insert("/api/hr/postSeq/v1/list".toCharArray());
        tried.insert("/api/hr/benchPost/v1/list".toCharArray());
        tried.insert("/api/hr/job/v1/selectList".toCharArray());
        tried.insert("/api/hr/postSeq/v1/save".toCharArray());
        tried.insert("/api/hr/postSeq/v1/update".toCharArray());
        tried.insert("/api/hr/postSeq/v1/detail".toCharArray());
        tried.insert("/api/hr/postSeq/v1/updateStatus".toCharArray());
        tried.insert("/api/hr/postSeq/v1/delete".toCharArray());
        tried.insert("/api/hr/benchPost/v1/save".toCharArray());
        tried.insert("/api/hr/jobgrade/v1/channel/list".toCharArray());
        tried.insert("/api/hr/benchPost/v1/update".toCharArray());
        tried.insert("/api/hr/benchPost/v1/detail".toCharArray());
        tried.insert("/api/hr/jobgrade/v1/list".toCharArray());
        tried.insert("/api/hr/benchPost/v1/updateStatus".toCharArray());
        tried.insert("/api/hr/benchPost/v1/delete".toCharArray());
        tried.insert("/api/hr/benchPost/v1/simpleTree".toCharArray());
        tried.insert("/api/hr/postmanage/v1/list".toCharArray());
        tried.insert("/api/hr/postmanage/v1/save".toCharArray());
        tried.insert("/api/hr/postmanage/v1/update".toCharArray());
        tried.insert("/api/hr/postmanage/v1/updateStatus".toCharArray());
        tried.insert("/api/hr/postmanage/v1/delete".toCharArray());
        tried.insert("/api/hr/jobType/v1/list".toCharArray());
        tried.insert("/api/hr/job/v1/list".toCharArray());
        tried.insert("/api/hr/job/v1/save".toCharArray());
        tried.insert("/api/hr/jobType/v1/detail".toCharArray());
        tried.insert("/api/hr/job/v1/update".toCharArray());
        tried.insert("/api/hr/job/v1/delete".toCharArray());
        tried.insert("/api/hr/job/v1/updateStatus".toCharArray());
        tried.insert("/api/hr/jobType/v1/save".toCharArray());
        tried.insert("/api/hr/job/v1/detail".toCharArray());
        tried.insert("/api/hr/jobType/v1/update".toCharArray());
        tried.insert("/api/hr/jobType/v1/delete".toCharArray());
        tried.insert("/api/hr/jobType/v1/updateStatus".toCharArray());
        tried.insert("/api/hr/jobgrade/v1/save".toCharArray());
        tried.insert("/api/hr/jobgrade/v1/channel/detail".toCharArray());
        tried.insert("/api/hr/jobgrade/v1/update".toCharArray());
        tried.insert("/api/hr/jobgrade/v1/delete".toCharArray());
        tried.insert("/api/hr/jobgrade/v1/updateStatus".toCharArray());
        tried.insert("/api/hr/jobgrade/v1/channel/save".toCharArray());
        tried.insert("/api/hr/jobgrade/v1/detail".toCharArray());
        tried.insert("/api/hr/jobgrade/v1/channel/update".toCharArray());
        tried.insert("/api/hr/jobgrade/v1/channel/delete".toCharArray());
        tried.insert("/api/hr/jobgrade/v1/channel/updateStatus".toCharArray());
        tried.insert("/api/hr/company/v1/selectList".toCharArray());
        tried.insert("/api/hr/workplace/v1/list".toCharArray());
        tried.insert("/api/hr/workplace/v1/save".toCharArray());
        tried.insert("/api/hr/workplace/v1/detail".toCharArray());
        tried.insert("/api/hr/workplace/v1/update".toCharArray());
        tried.insert("/api/hr/workplace/v1/delete".toCharArray());
        tried.insert("/api/hr/workplace/v1/updateStatus".toCharArray());
        tried.insert("/api/hr/cost/v1/tree".toCharArray());
        tried.insert("/api/hr/cost/v1/save".toCharArray());
        tried.insert("/api/hr/cost/v1/detail".toCharArray());
        tried.insert("/api/hr/cost/v1/update".toCharArray());
        tried.insert("/api/hr/cost/v1/delete".toCharArray());
        tried.insert("/api/hr/cost/v1/updateStatus".toCharArray());
        tried.insert("/api/hrpaas/metadata/tenant/v1/getOrg".toCharArray());
        tried.insert("/api/hrpaas/metadata/tenant/v1/orgRule".toCharArray());
        tried.insert("/api/bcc/dict/common/v1/type/simple/listV2".toCharArray());
        tried.insert("/api/bcc/dict/common/v1/dict/simple/list".toCharArray());
        tried.insert("/api/bcc/dict/common/v1/dict/dragSort".toCharArray());
        tried.insert("/api/bcc/dict/common/v1/dict/saveOrUpdate".toCharArray());
        tried.insert("/api/bcc/dict/common/v1/dict/enable".toCharArray());
        tried.insert("/api/bcc/dict/common/v1/dict/delete".toCharArray());
        tried.insert("/api/hrpaas/metadata/tenant/v1/logo".toCharArray());
        tried.insert("/api/hr/emp/work/v1/list".toCharArray());
        tried.insert("/api/hr/emp/work/v1/save".toCharArray());
        tried.insert("/api/hr/emp/basic/v1/detail".toCharArray());
        tried.insert("/api/hr/emp/subset/v1/list".toCharArray());
        tried.insert("/api/esign/contractSign/v1/waitLaunchDetail".toCharArray());
        tried.insert("/api/esign/contractSign/v1/empStartSign".toCharArray());
        tried.insert("/api/esign/contractSign/v1/importSignCheck".toCharArray());
        tried.insert("/api/esign/contractSign/v1/startSign".toCharArray());
        tried.insert("/api/esign/contractSign/v1/approvalStartSign".toCharArray());
        tried.insert("/api/esign/contractSign/v1/voidDetail".toCharArray());
        tried.insert("/api/esign/contractSign/v1/withdrawal".toCharArray());
        tried.insert("/api/esign/contractSign/v1/voided".toCharArray());
        tried.insert("/api/esign/contractSign/v1/voidContract".toCharArray());
        tried.insert("/api/esign/contractSign/v1/list".toCharArray());
        tried.insert("/api/esign/contractSign/v1/remove".toCharArray());
        tried.insert("/api/esign/contractSign/v1/urgingContract".toCharArray());
        tried.insert("/api/esign/contractSign/v1/statistics".toCharArray());
        tried.insert("/api/esign/contractSign/v1/revoke".toCharArray());
        tried.insert("/api/esign/contractSign/v1/signDetail".toCharArray());
        tried.insert("/api/esign/contractSign/v1/completeDetail".toCharArray());
        tried.insert("/api/esign/contractSign/v1/companySignLink".toCharArray());
        tried.insert("/api/esign/contractSign/v1/exportSignUser".toCharArray());
        tried.insert("/api/esign/contractSign/v1/batchSignCheck".toCharArray());
        tried.insert("/api/esign/contractSign/v1/batchSign".toCharArray());
        tried.insert("/api/esign/contractDocInfo/v1/getDetail".toCharArray());
        tried.insert("/api/esign/contractDocInfo/v1/saveDocInfo".toCharArray());
        tried.insert("/api/esign/contractDocInfo/v1/updateDocInfo".toCharArray());
        tried.insert("/api/esign/contractDocInfo/v1/delDocInfo".toCharArray());
        tried.insert("/api/esign/contractDocInfo/v1/list".toCharArray());
        tried.insert("/api/esign/contractDocInfo/v1/listSubCompany".toCharArray());
        tried.insert("/api/esign/contractDocInfo/v1/listCompanySeal".toCharArray());
        tried.insert("/api/esign/contractDocInfo/v1/getSealImage".toCharArray());
        tried.insert("/api/esign/contractTemplate/v1/getDetail".toCharArray());
        tried.insert("/api/esign/contractTemplate/v1/insert".toCharArray());
        tried.insert("/api/esign/contractTemplate/v1/delete".toCharArray());
        tried.insert("/api/esign/contractTemplate/v1/saveWelcome".toCharArray());
        tried.insert("/api/esign/contractTemplate/v1/getList".toCharArray());
        tried.insert("/api/esign/contractTemplate/v1/condition".toCharArray());
        tried.insert("/api/esign/contractTemplate/v1/select".toCharArray());
        tried.insert("/api/esign/contractTemplate/v1/docInfoList".toCharArray());
        tried.insert("/api/esign/contractTemplate/v1/getDocList".toCharArray());
        tried.insert("/api/esign/contractTemplate/v1/saveDoc".toCharArray());
        tried.insert("/api/esign/contractTemplate/v1/getDocInfo".toCharArray());
        tried.insert("/api/esign/contractTemplate/v1/removeDoc".toCharArray());
        tried.insert("/api/esign/contractTemplate/v1/selectByEmp".toCharArray());
        tried.insert("/api/hr/contract/v1/list".toCharArray());
        tried.insert("/api/hr/contract/v1/save".toCharArray());
        tried.insert("/api/hr/contract/v1/renewalContract".toCharArray());
        tried.insert("/api/hr/contract/v1/getDetail".toCharArray());
        tried.insert("/api/hr/contract/v1/getApprovalRecords".toCharArray());
        tried.insert("/api/esign/contractSign/v1/approvalStartSign".toCharArray());
        tried.insert("/api/hr/contract/v1/getContractRecords".toCharArray());
        tried.insert("/api/hr/contract/type/set/v1/list".toCharArray());
        tried.insert("/api/hr/contract/type/set/v1/save".toCharArray());
        tried.insert("/api/hr/contract/type/set/v1/update".toCharArray());
        tried.insert("/api/hr/contract/type/set/v1/delContract".toCharArray());
        tried.insert("/api/hr/contract/type/set/v1/getDetail".toCharArray());
        tried.insert("/api/hr/contract/type/set/v1/condition".toCharArray());
        tried.insert("/api/hr/contract/type/set/v1/updateStatus".toCharArray());
        tried.insert("/api/hr/contract/type/set/v1/checkDuplicate".toCharArray());
        String value = FastjsonUtil.toJson(tried.getRoot());
        cacheService.cacheValue("mytest2", value);
    }

    @Autowired
    private MasterDataFeign masterDataFeign;

    @Test
    public void test3() {
        ArrayList<Long> longs = Lists.newArrayList(0L);
        String empIds = Joiner.on(",").join(longs);
        // Result<List<SysEmpInfoDto>> empInfoByEmpIds = masterDataFeign.getEmpInfoByEmpIds(empIds);
        // List<SysEmpInfoDto> data = empInfoByEmpIds.getData();
        // for (SysEmpInfoDto datum : data) {
        //
        // }
    }

}
