package com.caidaocloud.auth.service.caidao.infrastructure.repository.impl;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.auth.service.caidao.domain.entity.OAuthOpenTenantDo;
import com.caidaocloud.auth.service.caidao.domain.repository.OAuthOpenTenantRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.OAuthOpenTenantDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.OAuthOpenTenantPo;
import com.caidaocloud.util.ObjectConverter;
import lombok.val;

import org.springframework.stereotype.Repository;

@Repository
public class OAuthOpenTenantRepositoryImpl implements OAuthOpenTenantRepository {

    @Resource
    private OAuthOpenTenantDao dao;

    @Override
    public OAuthOpenTenantDo selectTenantByCorpKey(String clientId, String tenantCorpKey) {
        LambdaQueryWrapper<OAuthOpenTenantPo> queryWrapper = new QueryWrapper<OAuthOpenTenantPo>().lambda();
        queryWrapper.eq(OAuthOpenTenantPo::getClientId, clientId)
                .eq(OAuthOpenTenantPo::getCorpKey, tenantCorpKey);

        OAuthOpenTenantPo po = dao.selectOne(queryWrapper);
        return po == null ? null : ObjectConverter.convert(po, OAuthOpenTenantDo.class);
    }

    @Override
    public void insertTenant(OAuthOpenTenantDo openTenant) {
        val po = ObjectConverter.convert(openTenant, OAuthOpenTenantPo.class);
        dao.insert(po);
    }

    @Override
    public Long getMaxBelongId() {
        LambdaQueryWrapper<OAuthOpenTenantPo> queryWrapper = new QueryWrapper<OAuthOpenTenantPo>().lambda();
        queryWrapper.orderByDesc(OAuthOpenTenantPo::getBelongId)
                .last("limit 1");
        OAuthOpenTenantPo po = dao.selectOne(queryWrapper);
        return po == null ? 0 : po.getBelongId();
    }
}
