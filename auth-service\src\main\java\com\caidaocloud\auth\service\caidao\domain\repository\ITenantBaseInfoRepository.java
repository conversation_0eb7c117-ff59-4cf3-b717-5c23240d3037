package com.caidaocloud.auth.service.caidao.domain.repository;

import java.util.List;

import com.caidaocloud.auth.service.caidao.domain.entity.TenantBaseInfoDo;


public interface ITenantBaseInfoRepository {
    void insertBatch(List<TenantBaseInfoDo> dataList);

    void insert(TenantBaseInfoDo data);

    void update(TenantBaseInfoDo data);

    int insertSelective(TenantBaseInfoDo record);

    int updateByPrimaryKeySelective(TenantBaseInfoDo record);

    void delete(List<Long> tenantIds);

    // void softDelete(List<Long> tenantIds);

    List<TenantBaseInfoDo> getTenantList(List<Long> tenantIds);

    TenantBaseInfoDo getTenantByCode(String tenantCode);

    List<TenantBaseInfoDo> getTenantListByCorpCode(String corpCode);
}
