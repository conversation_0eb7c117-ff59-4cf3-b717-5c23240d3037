package com.caidaocloud.auth.service.caidao.infrastructure.util;

import java.util.Random;

/**
 * 随机产生字符串
 */
public class RandomUtil {
    private final static String RANDOM_STR = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    public static String randomString (int length){
        String str = RANDOM_STR;
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for(int i=0; i< length; i++){
            int number = random.nextInt(62);
            sb.append(str.charAt(number));
        }
        return sb.toString();
    }
}
