package com.caidaocloud.auth.service.caidao.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.caidaocloud.auth.service.caidao.domain.entity.UserAccountBaseInfo;
import com.caidaocloud.auth.service.caidao.domain.entity.UserBaseInfoDo;
import com.caidaocloud.auth.service.caidao.domain.repository.IUserBaseInfoRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.AccountBaseInfoDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.UserBaseInfoDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AccountBaseInfoPo;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.UserBaseInfoPo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public class UserBaseInfoRepositoryImpl implements IUserBaseInfoRepository {

    @Autowired
    private UserBaseInfoDao userBaseInfoDao;

    @Autowired
    private AccountBaseInfoDao accountBaseInfoDao;

    @Override
    public List<UserBaseInfoDo> selectUserBaseInfoByAccounts(List<String> accounts) {
        if(null == accounts || accounts.size()==0){
            return new ArrayList<>();
        }
        return ObjectConverter.convertList(userBaseInfoDao.selectUserBaseInfoByAccounts(accounts), UserBaseInfoDo.class);
    }

    @Override
    public List<UserBaseInfoDo> selectUserBaseInfoByEmpIds(List<Long> empIds) {
        if(null == empIds || empIds.size()==0){
            return new ArrayList<>();
        }
        List<UserBaseInfoPo> userBaseInfoPos = userBaseInfoDao.selectList(new LambdaQueryWrapper<UserBaseInfoPo>().in(UserBaseInfoPo::getEmpId, empIds));
        return ObjectConverter.convertList(userBaseInfoPos, UserBaseInfoDo.class);
    }


    @Override
    public List<UserAccountBaseInfo> selectByAccounts(List<String> accounts) {
        val account = accountBaseInfoDao.selectList(new LambdaQueryWrapper<AccountBaseInfoPo>().in(AccountBaseInfoPo::getAccount, accounts));
        return FastjsonUtil.convertList(account, UserAccountBaseInfo.class);
    }


}
