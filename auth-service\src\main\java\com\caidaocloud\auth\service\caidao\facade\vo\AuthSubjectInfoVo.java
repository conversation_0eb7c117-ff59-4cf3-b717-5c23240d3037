package com.caidaocloud.auth.service.caidao.facade.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/12
 **/
@Data
@ApiModel(value = "员工信心vo")
public class AuthSubjectInfoVo {

    @ApiModelProperty("员工用户id")
    private Long subjectId;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("工号")
    private String workno;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("所属组织")
    private String organizeTxt;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("用户状态；用户状态：1 正常 2 停用 3 锁定")
    private Integer status;

    @ApiModelProperty("员工id")
    private Long empId;

}
