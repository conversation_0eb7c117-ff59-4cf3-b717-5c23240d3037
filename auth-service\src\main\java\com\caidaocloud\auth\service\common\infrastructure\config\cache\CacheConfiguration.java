package com.caidaocloud.auth.service.common.infrastructure.config.cache;

import com.jarvis.cache.serializer.FastjsonSerializer;
import com.jarvis.cache.serializer.ISerializer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/1/11
 **/
@Configuration
public class CacheConfiguration {
    @Bean
    @ConditionalOnMissingBean
    public ISerializer<Object> serialize() {
        Object res = new FastjsonSerializer();
        return (ISerializer) res;
    }
}
