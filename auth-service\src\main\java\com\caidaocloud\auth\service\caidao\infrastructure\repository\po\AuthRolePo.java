package com.caidaocloud.auth.service.caidao.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleDo;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.AdapterDeviceEnum;
import com.caidaocloud.util.FastjsonUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/5/7
 **/
@Data
@TableName("auth_role")
public class AuthRolePo implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 角色名称
     */
    private String name;
    /**
     * 多语言
     */
    private String i18nName;

    /**
     * 角色编码
     */
    private String code;

    /**
     * 角色描述
     */
    private String remark;

    /**
     * 角色类型; 0：系统角色 1：自定义角色
     */
    private Integer roleType;

    /**
     * 适用设备;ALL、PC、H5、APP
     */
    private String device;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否启用
     */
    @TableField(value = "is_enabled")
    private Boolean isEnabled;

    /**
     * 是否删除
     */
    private Boolean deleted;

    public AuthRoleDo toEntity() {
        AuthRoleDo authRoleDo = FastjsonUtil.convertObject(this, AuthRoleDo.class);
        if (StringUtils.isNotBlank(device)) {
            authRoleDo.setDevice(FastjsonUtil.toList(this.device, AdapterDeviceEnum.class));
        }
        return authRoleDo;
    }

}
