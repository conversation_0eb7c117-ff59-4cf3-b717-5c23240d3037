package com.caidaocloud.auth.service.caidao.application.feign;

import com.caidaocloud.auth.service.caidao.application.dto.masterdata.SysEmpInfoDto;
import com.caidaocloud.auth.service.caidao.application.feign.fallback.MasterDataFeignFallback;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/12
 **/
@FeignClient(value = "${feign.rename.caidaocloud-masterdata-service:caidaocloud-masterdata-service}", path = "/api/masterData",
        fallback = MasterDataFeignFallback.class, configuration = FeignConfiguration.class)
public interface MasterDataFeign {

    /**
     * 获取员工信息
     *
     * @param empIds
     * @return
     */
    @GetMapping(value = "/emp/v1/getEmpInfoByEmpIds")
    Result<List<SysEmpInfoDto>> getEmpInfoByEmpIds(@RequestParam("empIds")String empIds);

}