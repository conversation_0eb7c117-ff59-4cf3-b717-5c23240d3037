package com.caidaocloud.auth.service.caidao.infrastructure.util.proxy;

import com.caidaocloud.auth.service.caidao.domain.repository.IAuthRoleScopeRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.AuthRoleScopeEnum;
import com.caidaocloud.auth.service.caidao.infrastructure.factory.JDKInvocationHandler;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.SpringUtil;
import com.google.common.collect.Maps;

import java.lang.reflect.Proxy;
import java.util.concurrent.ConcurrentMap;

public class AuthRoleScopeProxy {
    private static ConcurrentMap<AuthRoleScopeEnum, IAuthRoleScopeRepository> container = Maps.newConcurrentMap();

    public static IAuthRoleScopeRepository getProxy(AuthRoleScopeEnum type) {
        if (type == null) {
            throw new ServerException("parameter is null");
        }
        if (container.containsKey(type)) {
            return container.get(type);
        }
        IAuthRoleScopeRepository iAuthRoleScopeRepository = null;
        synchronized (AuthRoleScopeProxy.class) {
            if (container.containsKey(type)) {
                return container.get(type);
            }
            JDKInvocationHandler jdkInvocationHandler = new JDKInvocationHandler(SpringUtil.getBean(type.aClass));
            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            iAuthRoleScopeRepository = (IAuthRoleScopeRepository) Proxy.newProxyInstance(classLoader, new Class[]{IAuthRoleScopeRepository.class}, jdkInvocationHandler);
            container.put(type, iAuthRoleScopeRepository);
        }
        return iAuthRoleScopeRepository;
    }
}