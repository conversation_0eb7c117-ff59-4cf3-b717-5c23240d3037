package com.caidaocloud.auth.service.service.mongo;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.auth.service.caidao.application.service.AuthGroupRuleSubjectService;
import com.caidaocloud.auth.service.caidao.application.dto.AuthRoleGroupDto;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleDo;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthRoleRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.entity.ConditionTree;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.tree.ConditionNodeRelationEnum;
import com.caidaocloud.auth.service.caidao.facade.vo.AuthSubjectAndRoleVo;
import com.caidaocloud.auth.service.caidao.facade.vo.RoleDetailVo;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.AuthGroupRuleDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthGroupRulePo;
import com.caidaocloud.auth.service.caidao.application.service.AuthRoleGroupService;
import com.caidaocloud.auth.service.caidao.application.service.AuthRoleService;
import com.caidaocloud.hrpaas.paas.match.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class OAuthRoleTest {

    @Autowired
    private AuthRoleService oAuthRoleService;

    @Autowired
    private AuthRoleGroupService oAuthRoleGroupService;

    @Autowired
    private AuthGroupRuleDao authGroupRuleDao;

    @Resource
    private IAuthRoleRepository authRoleRepository;

    @Autowired
    private AuthGroupRuleSubjectService authGroupRuleSubjectService;

    @Test
    public void testDelete() {
        ArrayList<Long> roleIdList = Lists.newArrayList();
        roleIdList.add(1L);
        oAuthRoleService.deleteRole(roleIdList);
    }

    @Test
    public void selectRoleByCode() {
        List<AuthRoleDo> roleByCode = oAuthRoleService.getRoleByCode(Lists.newArrayList("ADMIN"));
        System.out.println(roleByCode);
    }

    @Test
    public void testInsertRoleGroup() {
        AuthRoleGroupDto authRoleGroupDto = new AuthRoleGroupDto();
        authRoleGroupDto.setName("test role group");
        authRoleGroupDto.setRemark("test role group");
        ConditionTree conditionTree = new ConditionTree();
        conditionTree.setId("1648194162688_728");
        conditionTree.setRelation(ConditionNodeRelationEnum.and);
        List<ConditionNode> children = Lists.newArrayList();
        ConditionNode conditionNode = new ConditionNode();
        conditionNode.setId("1648194261134_265");
        conditionNode.setType(ConditionNodeTypeEnum.single);
        ConditionExp conditionExp = new ConditionExp();
        conditionExp.setName("empName");
        conditionExp.setComponentType(ConditionComponentEnum.STRING_INPUT);
        conditionExp.setValue("song");
        conditionExp.setSymbol(ConditionOperatorEnum.EQ);
        conditionNode.setCondition(conditionExp);
        children.add(conditionNode);
        conditionTree.setChildren(children);
        authRoleGroupDto.setExpression(conditionTree);
        oAuthRoleGroupService.saveOrUpdateRoleGroup(authRoleGroupDto);
    }

    @Test
    public void selectRoleGroup() {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", 1);
        AuthGroupRulePo authGroupRulePo = authGroupRuleDao.selectOne(queryWrapper);
        System.out.println(authGroupRulePo);
    }

    @Test
    public void testSelectRole() {
        RoleDetailVo vo = oAuthRoleService.getRoleByRoleId(4L);
        System.out.println(vo);
    }

    @Test
    public void testGetRoleBySubject() {
        ArrayList<Long> list = Lists.newArrayList(22L, 36055L);
        List<AuthSubjectAndRoleVo> roleBySubjectIds = authGroupRuleSubjectService.getRoleBySubjectIds(list);
        System.out.println(roleBySubjectIds);
    }

}
