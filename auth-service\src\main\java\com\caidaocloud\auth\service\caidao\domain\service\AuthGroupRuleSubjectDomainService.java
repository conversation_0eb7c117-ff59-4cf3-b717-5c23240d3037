package com.caidaocloud.auth.service.caidao.domain.service;

import com.caidaocloud.auth.service.caidao.application.dto.subject.AuthRuleIdAndSubjectIdDto;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleDo;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRuleIdAndSubjectIdDo;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthSubjectDo;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthGroupRuleRepository;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthGroupRuleSubjectRepository;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthRoleRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.SystemRoleEnum;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.util.ObjectUtil;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import com.googlecode.totallylazy.Strings;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.caidaocloud.auth.service.caidao.infrastructure.constant.CodeConstant.SYSTEM_ROLE_TYPE;

/**
 * <AUTHOR>
 * @date 2022/5/11
 **/
@Service
@Slf4j
public class AuthGroupRuleSubjectDomainService {

    @Resource
    private IAuthGroupRuleSubjectRepository authGroupRuleSubjectRepository;
    @Resource
    private IAuthRoleRepository authRoleRepository;
    @Resource
    private IAuthGroupRuleRepository authGroupRuleRepository;


    public PageResult<AuthSubjectDo> getPage(List<Long> ruleIds, QueryPageBean queryPageBean) {
        if (CollectionUtils.isEmpty(ruleIds)) {
            throw new ServerException("ruleIds is empty");
        }
        return authGroupRuleSubjectRepository.getPage(ruleIds, queryPageBean);
    }

    public void createSubject(Long ruleId, List<Long> subjectIdList, String roleId) {
        if (ruleId == null || CollectionUtils.isEmpty(subjectIdList)) {
            throw new ServerException("ruleId or subjectIdList is empty");
        }
        checkConfigRole(roleId, ruleId);
        authGroupRuleSubjectRepository.createSubject(ruleId, subjectIdList);
    }

    public void checkConfigRole(String roleId, Long ruleId) {
        Optional<SystemRoleEnum> systemRoleEnumOptional = SystemRoleEnum.getCurrentUserRole();
        if (!systemRoleEnumOptional.isPresent() || systemRoleEnumOptional.get()!= SystemRoleEnum.CONFIG) {
            return;
        }

        Option<AuthRoleDo> role = authRoleRepository.getRoleByRoleId(Long.valueOf(roleId));
        if (role.isEmpty()) {
            log.warn("roleId is not exist,ruleId={},roleId={}",ruleId,roleId);
        }
        if (ObjectUtil.nullSafeEquals(role.get().getRoleType(), SYSTEM_ROLE_TYPE)) {
            throw new ServerException("不可调整配置管理员的权限");
        }
    }
    public void checkConfigRole(AuthRoleDo role, Long ruleId) {
        Optional<SystemRoleEnum> systemRoleEnumOptional = SystemRoleEnum.getCurrentUserRole();
        if (!systemRoleEnumOptional.isPresent() || systemRoleEnumOptional.get()!= SystemRoleEnum.CONFIG) {
            return;
        }

        if (ObjectUtil.nullSafeEquals(role.getRoleType(), SYSTEM_ROLE_TYPE)) {
            throw new ServerException("不可调整配置管理员的权限");
        }
    }

    public void deleteSubject(Long ruleId, List<Long> subjectIdList, Long roleId) {
        if (ruleId == null || CollectionUtils.isEmpty(subjectIdList)) {
            throw new ServerException("ruleId or subjectIdList is empty");
        }
        checkConfigRole(String.valueOf(roleId),ruleId);
        authGroupRuleSubjectRepository.deleteSubject(ruleId, subjectIdList);
    }

    public void deleteByRuleId(Long ruleId, String roleId) {
        if (ruleId == null) {
            throw new ServerException("deleteByRuleId ruleId is empty");
        }
        checkConfigRole(roleId, ruleId);
        authGroupRuleSubjectRepository.deleteByRuleId(ruleId);
    }


    public void deleteSubjects(String subjectIds) {
        authGroupRuleSubjectRepository.deleteSubjects(subjectIds);
    }

    public List<Long> getRuleIdListBySubject(Long subjectId){
        return authGroupRuleSubjectRepository.getRuleIdListBySubject(subjectId);
    }

    public List<AuthRuleIdAndSubjectIdDto> getRuleIdAndSubjectIdBySubjectIds(List<Long> subjectIds) {
        List<AuthRuleIdAndSubjectIdDo> ruleIdAndSubjectIdList = authGroupRuleSubjectRepository.getRuleIdAndSubjectIdBySubjectIds(subjectIds);
        return Sequences.sequence(ruleIdAndSubjectIdList).map(e -> {
            var dto = new AuthRuleIdAndSubjectIdDto();
            BeanUtils.copyProperties(e, dto);
            return dto;
        }).toList();
    }

}
