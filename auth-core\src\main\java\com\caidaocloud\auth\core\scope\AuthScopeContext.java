package com.caidaocloud.auth.core.scope;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.caidaocloud.auth.core.dto.AuthRoleScopeFilterDetailDto;
import com.caidaocloud.auth.core.scope.handler.ScopeValueHandler;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.mapper.MapperFactoryBean;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2024/1/4
 */
@Slf4j
public class AuthScopeContext implements BeanPostProcessor {

	private ScopeValueHandler handler;

	/**
	 * 记录所有需要数据权限的mapper接口
	 */
	private static final Map<String, AuthScopeDefinition> authScopeMap = new HashMap<>();

	public AuthScopeContext(ScopeValueHandler scopeValueHandler) {
		this.handler = scopeValueHandler;
	}

	public static String getAuthScopeProperty(String id, String property) {
		return authScopeMap.get(id).getPropertyMap().getOrDefault(property, property);
	}


	/**
	 * 在mapper实例化后，将过滤数据权限的接口注册到authScopeMap中
	 * @param bean
	 * @param beanName
	 * @return
	 * @throws BeansException
	 */
	@Override
	public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
		if (bean instanceof MapperFactoryBean) {
			Class<?> clazz = ((MapperFactoryBean<?>) bean).getMapperInterface();
			for (Method method : clazz.getMethods()) {
				String statementId = clazz.getName() + "." + method.getName();
				if (log.isDebugEnabled()) {
					log.debug("扫描mapper接口，接口id={}", statementId);
				}
				AuthScope annotation = method.getAnnotation(AuthScope.class);
				if (annotation != null) {
					authScopeMap.put(statementId, createAuthScopeDefinition(annotation.value(), annotation.property()));
				}
			}
		}
		return BeanPostProcessor.super.postProcessAfterInitialization(bean, beanName);
	}

	private AuthScopeDefinition createAuthScopeDefinition(String value, AuthScopeProperty[] property) {
		AuthScopeDefinition definition = new AuthScopeDefinition();
		definition.setAuthScopeCode(value);
		for (AuthScopeProperty scopeProperty : property) {
			definition.getPropertyMap().put(scopeProperty.source(), scopeProperty.target());
		}
		return definition;
	}

	public static String getAuthScopeCode(String statementId) {
		return Optional.ofNullable(authScopeMap.get(statementId)).map(AuthScopeDefinition::getAuthScopeCode)
				.orElse(null);
	}

	public List<String> handleValue(AuthRoleScopeFilterDetailDto scope) {
		return handler.handle(scope);
	}
}
