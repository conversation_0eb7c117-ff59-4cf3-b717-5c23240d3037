package com.caidaocloud.auth.service.service.mongo;

import com.caidaocloud.auth.service.caidao.application.service.AuthGroupRuleSubjectService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class AuthGroupRuleTest {

    @Autowired
    private AuthGroupRuleSubjectService authGroupRuleSubjectService;

    @Test
    public void test() {
        List<String> resourceUrlListBySubjectId = authGroupRuleSubjectService.getResourceUrlListBySubjectId(36052L);
        for (String s : resourceUrlListBySubjectId) {
            System.out.println(s);
        }
    }

}
