package com.caidaocloud.auth.service.caidao.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 授权码存储 Po
 * <AUTHOR>
 * @date 2021-11-11
 */
@Data
@Accessors(chain = true)
@TableName("oauth_code_store")
public class OAuthCodeStorePo {
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 授权码
     */
    private String code;

    /**
     * 认证客户端 Id
     */
    private String clientId;

    /**
     * 认证信息
     */
    private String authentication;

    /**
     * code 创建时间
     */
    private Long createTime;

    /**
     * code 失效时间
     */
    private Long expiresTime;
}
