package com.caidaocloud.auth.core.dto;

import com.caidaocloud.auth.core.enums.AuthRoleScopeRestriction;
import com.caidaocloud.auth.core.enums.AuthRoleScopeTarget;
import com.caidaocloud.auth.core.enums.AuthRoleScopeTargetType;
import com.googlecode.totallylazy.Lists;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
public class AuthRoleScopeFilterDetailDto {
    private AuthRoleScopeTargetType targetType = AuthRoleScopeTargetType.STANDARD;
    private String property;
    private boolean inToOr;
    private AuthRoleScopeRestriction restriction;
    private String simpleValues;
    private String target;
}
