package com.caidaocloud.auth.service.caidao.infrastructure.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.dto.FilterBean;
import lombok.var;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/23
 **/
public class QueryWrapperUtil {

    /**
     * 构建QueryWrapper
     *
     * @param filterList
     * @return
     */
    public static QueryWrapper buildQueryWrapper(List<FilterBean> filterList) {
        var queryWrapper = new QueryWrapper();
        if (CollectionUtils.isEmpty(filterList)) {
            return queryWrapper;
        }
        for (FilterBean filterBean : filterList) {
            switch (filterBean.getOp()) {
                case eq:
                    queryWrapper.eq(filterBean.getProp(), filterBean.getValue());
                    break;
                case ne:
                    queryWrapper.ne(filterBean.getProp(), filterBean.getValue());
                    break;
                case gt:
                    queryWrapper.gt(filterBean.getProp(), filterBean.getValue());
                    break;
                case ge:
                    queryWrapper.ge(filterBean.getProp(), filterBean.getValue());
                    break;
                case lt:
                    queryWrapper.lt(filterBean.getProp(), filterBean.getValue());
                    break;
                case le:
                    queryWrapper.le(filterBean.getProp(), filterBean.getValue());
                    break;
            }
        }
        return queryWrapper;
    }

}
