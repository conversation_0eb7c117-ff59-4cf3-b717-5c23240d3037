package com.caidaocloud.auth.service.caidao.infrastructure.config.auth;

import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@Configuration
@EnableWebSecurity
public class OAuthWebSecurityConfig extends WebSecurityConfigurerAdapter {

    @Resource
    private UserDetailsService userDetailsService;

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.csrf().disable()
            /**
             * SessionCreationPolicy 的 4 个策略备注如下：
             *  ALWAYS：总是创建 HttpSession
             *  NEVER：Spring Security只会在需要时创建一个HttpSession
             *  IF_REQUIRED：Spring Security不会创建HttpSession，但如果它已经存在，将可以使用HttpSession
             *  STATELESS：Spring Security永远不会创建HttpSession，它不会使用HttpSession来获取SecurityContext
             */
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED)
            .and().formLogin()
            .loginPage("/login")
            .loginProcessingUrl("/oauth2-login")
             // 登录失败，处理 handler
            .failureHandler(new LoginErrAuthenticationFailureHandler())
             // 登录成功处理 handler
            .successHandler(new LoginOkAuthenticationSuccessHandler())
            .and()
            .exceptionHandling()
            //没有权限，返回json
            .accessDeniedHandler(new PermissionAccessDeniedHandler())
            .and()
            .logout()
            //退出登录url
            .logoutUrl("/logout")
            //退出成功，返回json
            .logoutSuccessHandler(new AuthLogoutSuccessHandler())
            .and().logout().logoutUrl("/logout").permitAll()
            .and().authorizeRequests()
            .antMatchers("/assets/**",
                    "/oauth/authorize", "/oauth/confirm_access", "/oauth/error", "oauth/check_token",
                    // swagger 相关配置请求全放开
                    "/swagger-ui.html", "/swagger-resources/**", "/webjars/**", "/v2/api-docs", "/api/**")
            .permitAll()
            .anyRequest().authenticated();
    }

    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        // 认证管理器
        return super.authenticationManagerBean();
    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        DaoAuthenticationProvider dap = new DaoAuthenticationProvider();
        dap.setUserDetailsService(userDetailsService);
        dap.setPasswordEncoder(newUserPasswordEncoder());
        auth.authenticationProvider(dap);
    }

    @Bean
    public PasswordEncoder newUserPasswordEncoder(){
        return new UserPasswordEncoder();
    }

    /**
     * 登录失败处理逻辑
     */
    private class LoginErrAuthenticationFailureHandler implements AuthenticationFailureHandler {
        @Override
        public void onAuthenticationFailure(HttpServletRequest request,
            HttpServletResponse response, AuthenticationException ex) throws IOException, ServletException {
            response.setContentType("application/json;charset=utf-8");
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            PrintWriter out = response.getWriter();

            Result result = Result.fail(HttpServletResponse.SC_UNAUTHORIZED, "登录失败!");

            if (ex instanceof UsernameNotFoundException || ex instanceof BadCredentialsException) {
                result.setMsg("用户名或密码错误");
            } else if (ex instanceof DisabledException) {
                result.setMsg("账户被禁用");
            }

            out.println(FastjsonUtil.toJson(result));
            out.flush();
            out.close();
        }
    }

    /**
     * 认证成功处理逻辑
     */
    private class LoginOkAuthenticationSuccessHandler implements AuthenticationSuccessHandler {

        @Override
        public void onAuthenticationSuccess(HttpServletRequest request,
            HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
            response.setContentType("application/json;charset=utf-8");

            PrintWriter out = response.getWriter();
            out.println(FastjsonUtil.toJson(Result.ok(authentication)));
            out.flush();
            out.close();
        }
    }

    /**
     * 无权限处理逻辑
     */
    private class PermissionAccessDeniedHandler implements AccessDeniedHandler {

        @Override
        public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException ade) throws IOException, ServletException {
            response.setContentType("application/json;charset=utf-8");
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            PrintWriter out = response.getWriter();

            out.println(FastjsonUtil.toJson(Result.fail(4003, "权限不足")));
            out.flush();
            out.close();
        }
    }

    private class AuthLogoutSuccessHandler implements LogoutSuccessHandler{

        @Override
        public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
            response.setContentType("application/json;charset=utf-8");

            PrintWriter out = response.getWriter();
            out.println(FastjsonUtil.toJson(Result.success("退出成功")));
            out.flush();
            out.close();
        }
    }

}
