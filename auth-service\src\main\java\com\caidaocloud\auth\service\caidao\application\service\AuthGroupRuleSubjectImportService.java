package com.caidaocloud.auth.service.caidao.application.service;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.caidaocloud.auth.service.caidao.application.dto.AuthRoleAndRuleDto;
import com.caidaocloud.auth.service.caidao.application.dto.subject.AuthSubjectOfRoleDto;
import com.caidaocloud.auth.service.caidao.application.util.BeanUtil;
import com.caidaocloud.auth.service.caidao.application.util.ExcelUtils;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthGroupRuleSubjectImportDo;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleDo;
import com.caidaocloud.auth.service.caidao.domain.entity.UserBaseInfoDo;
import com.caidaocloud.auth.service.caidao.domain.service.AuthGroupRuleSubjectDomainService;
import com.caidaocloud.auth.service.caidao.domain.service.AuthGroupRuleSubjectImportDomainService;
import com.caidaocloud.auth.service.caidao.domain.service.AuthRoleDomainService;
import com.caidaocloud.auth.service.caidao.domain.service.AuthRoleGroupDomainService;
import com.caidaocloud.auth.service.caidao.domain.service.UserBaseInfoDomainService;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.importdto.ImportExcelDto;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.vo.ImportExcelProcessVo;
import com.caidaocloud.vo.ImportExcelVo;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AuthGroupRuleSubjectImportService {

    private static String ERROR_CACHE_KEY = "%s_ERROR";
    private static String PERCENTAGE_CACHE_KEY = "%s_PERCENTAGE";

    @Resource
    private CacheService cacheService;

    @Resource
    private AuthGroupRuleSubjectImportDomainService authGroupRuleSubjectImportDomainService;
    @Resource
    private UserBaseInfoDomainService userBaseInfoDomainService;
    @Autowired
    private AuthRoleDomainService authRoleDomainService;
    @Autowired
    private AuthRoleGroupDomainService authRoleGroupDomainService;
    @Autowired
    private AuthGroupRuleSubjectService authGroupRuleSubjectService;
    @Resource
    private AuthGroupRuleSubjectDomainService authGroupRuleSubjectDomainService;

    private static String businessCode = "AUTHGROUPRULE_SUBJECT_IMPORT";

    public boolean matchExcelCode(String excelCode){
        return this.businessCode.equals(excelCode.trim());
    }

    public ImportExcelVo importDataWithExcel(ImportExcelDto dto) {
        String processId = UUID.randomUUID().toString().replaceAll("-", "");
        ImportExcelVo vo = new ImportExcelVo();
        vo.setProcessUUid(processId);
        initProperty();
        return vo;
    }

    private void initProperty() {
    }

    private Map<String, UserBaseInfoDo> beforeInstallPropUserBaseInfoDo(List<String> accounts){
        Map<String, UserBaseInfoDo> map = new HashMap<>();
        List<UserBaseInfoDo> list = userBaseInfoDomainService.selectUserBaseInfoByAccounts(accounts);
        for(UserBaseInfoDo baseInfoDo:list){
            if(StringUtils.equals(String.valueOf(baseInfoDo.getTenantId()), SecurityUserUtil.getSecurityUserInfo().getTenantId())){
                map.put(baseInfoDo.getAccount(), baseInfoDo);
            }
        }
        return map;
    }

    @Async("taskExecutor")
    public void operateDataFromInputStream(InputStream inputStream, String processId, Long userId, String tenantId, String role) {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId(tenantId);
        userInfo.setUserId(userId);
        userInfo.setRole(role);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        int total = 0;
        int completed = 0;
        int successCount = 0;
        int failCount = 0;
        List<AuthGroupRuleSubjectImportDo> errorProList = new ArrayList<>();
        List<AuthGroupRuleSubjectImportDo> passDataList = new ArrayList<>();
        try {
            putImportExcelProcessVo(processId, total, completed, successCount, failCount);
            List<AuthGroupRuleSubjectImportDo> list = authGroupRuleSubjectImportDomainService.getImportDoFromExcel(inputStream);
            if(null == list || list.size()==0){
                putImportExcelProcessVo(processId, total, completed, successCount, failCount);
            }

            Set<String> accountSet = new HashSet<>();
            Set<String> roleSet = new HashSet<>();
            for(AuthGroupRuleSubjectImportDo importDo:list){
                accountSet.add(importDo.getAccount());
                if(null!=importDo.getRoleNames() && !importDo.getRoleNames().trim().equals("")) {
                    String roleNames = importDo.getRoleNames();
                    if (roleNames.indexOf("，")>-1) {
                        roleNames.replaceAll("，", ",");
                    }
                    for(String roleName:roleNames.split(",")){
                        roleSet.add(roleName);
                    }
                }
            }
            Map<String, UserBaseInfoDo> baseInfoMap = beforeInstallPropUserBaseInfoDo(new ArrayList<>(accountSet));
            List<AuthRoleDo> roleList = authRoleDomainService.getRoleIdAndNameByRoleName(new ArrayList<>(roleSet));
            Map<String, AuthRoleDo> roleMap = Sequences.sequence(roleList).stream()
                    .collect(Collectors.toMap(e -> e.getName(), e ->e, (v1, v2) -> v1));
            
            total = list.size();
            int i = 0;
            for (AuthGroupRuleSubjectImportDo data: list) {
                i++;
                completed++;
//                检查空字段
                if (checkEmptyProp(data)) {
                    errorProList.add(data);
                    failCount++;
                    continue;
                }
//                补充需要的字段，如id，枚举类型的数据，如果成功了放到通过的list，失败了放到失败的列表
                if(installProp(data, baseInfoMap, roleMap, tenantId)){
                    passDataList.add(data);
                    successCount++;
                }else {
                    errorProList.add(data);
                    failCount++;
                    continue;
                }
                if(passDataList.size()>0 && passDataList.size()%500 == 0){
//                batchInsertUpdate basicInfo, privateInfo and workInfo
                    errorProList.addAll(batchInsertUpdateData(passDataList));
                    passDataList.clear();
                }
                if(i>0 && i%50==0){
//                刷新缓存当前解析数据信息
                    putImportExcelProcessVo(processId, total, completed, completed - errorProList.size(), errorProList.size());
                }
            }
        }catch (Exception e){
            log.error("导入数据异常，-->error{}", e.getMessage());
            e.printStackTrace();
        }finally {
//          结束后，刷新缓存当前解析数据信息,并把错误信息放到
            errorProList.addAll(batchInsertUpdateData(passDataList));
            cacheErrorInfo(processId, errorProList);
            completed = total;
            failCount = errorProList.size();
            putImportExcelProcessVo(processId, total, completed, completed - failCount, failCount);
            operateAfterImport();
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

    private boolean installProp(AuthGroupRuleSubjectImportDo data, Map<String, UserBaseInfoDo> baseInfoMap, Map<String, AuthRoleDo> roleMap, String tenantId) {
        if(baseInfoMap.get(data.getAccount())==null){
            setEmpPropTip(data, "账号不存在");
        }else {
            data.setSubjectId(baseInfoMap.get(data.getAccount()).getUserId());
        }
        Set<String> roleSet = new HashSet<>();
        if(null!=data.getRoleNames() && !data.getRoleNames().trim().equals("")) {
            String roleNames = data.getRoleNames();
            if (roleNames.indexOf("，")>-1) {
                roleNames.replaceAll("，", ",");
            }
            for(String roleName:roleNames.split(",")){
                roleSet.add(roleName);
            }
        }
        for(String roleName:roleSet){
            AuthRoleDo roleDo = roleMap.get(roleName);
            try {
                authGroupRuleSubjectDomainService.checkConfigRole(roleDo, null);
            }
            catch (Exception e) {
                setEmpPropTip(data, e.getMessage());
            }
            if(null==roleDo){
                setEmpPropTip(data, "角色"+roleName+"不存在");
            }else {
                data.getRoleIds().add(roleDo.getId());
            }
        }

        return !data.isCheckFailFlag();
    }

    private List<AuthGroupRuleSubjectImportDo> batchInsertUpdateData(List<AuthGroupRuleSubjectImportDo> passDataList) {
        Map<Long, List<Long>> map = new HashMap<>();
        String subjects = "";
        for(AuthGroupRuleSubjectImportDo importDo:passDataList){
            if(importDo.getRoleIds()!=null && importDo.getRoleIds().size()>0){
                subjects += importDo.getSubjectId()+",";
                for(Long roleId:importDo.getRoleIds()){
                    List<Long> subjectIds = map.get(roleId);
                    if(subjectIds==null){
                        subjectIds = new ArrayList<>();
                    }
                    subjectIds.add(importDo.getSubjectId());
                    map.put(roleId, subjectIds);
                }
            }
        }
//      删除角色规则关联账号
        if(StringUtils.isNotEmpty(subjects)){
            List<AuthRoleAndRuleDto> roles = authGroupRuleSubjectService.loadRoleBySubjectId(Arrays.stream(subjects.split(","))
                    .map(s -> Long.valueOf(s))
                    .collect(Collectors.toList()));
            for (AuthRoleAndRuleDto role : roles) {
                authGroupRuleSubjectDomainService.checkConfigRole(String.valueOf(role.getRoleId()), role.getRuleId());
            }
            authGroupRuleSubjectService.deleteSubjects(subjects);
        }
//        角色规则关联账号保存
        for(Long roleId:map.keySet()){
            AuthSubjectOfRoleDto roleDto = new AuthSubjectOfRoleDto();
            roleDto.setRoleId(roleId);
            roleDto.setSubjectIdList(map.get(roleId));
            authGroupRuleSubjectService.createSubjectOfRole(roleDto);
        }

        return new ArrayList<>();
    }

    private void setEmpPropTip(AuthGroupRuleSubjectImportDo importDo, String tip){
        importDo.setCheckFailFlag(true);
        if(null==importDo.getCheckFailTips()){
            importDo.setCheckFailTips(tip);
        }else {
            importDo.setCheckFailTips(importDo.getCheckFailTips()+"，"+tip);
        }
    }
    private boolean checkEmptyProp(AuthGroupRuleSubjectImportDo importDo){
        if(importDo.getAccount()==null){
            setEmpPropTip(importDo, "账号不能为空");
        }
        List<Long> roleIds = new ArrayList<Long>();
        if(importDo==null || importDo.getRoleNames().trim().equals("")){
//            默认给赋值为o的规则id
            roleIds.add(0l);
        }
        importDo.setRoleIds(roleIds);
        return importDo.isCheckFailFlag();
    }

    private void operateAfterImport() {
    }

    private void cacheErrorInfo(String processId, List<?> errorList){
        cacheService.cacheValue(getErrorCacheKey(processId), FastjsonUtil.toJson(errorList), 1800);
    }
    private void putImportExcelProcessVo(String processId, int total, int completed, int successCount, int failCount){
        int notDone = total - completed;
        if(total == 0){
            total = 1;
        }
        ImportExcelProcessVo vo = new ImportExcelProcessVo(processId, total, completed, notDone, successCount, failCount);
        if(total == 0){
            vo.setTotal(0);
        }
        boolean flag = cacheService.cacheValue(getPercentageCacheKey(processId), FastjsonUtil.toJson(vo), 300);
    }

    public ImportExcelProcessVo getImportDataPercentage(String processId) {
        ImportExcelProcessVo vo = FastjsonUtil.toObject(cacheService.getValue(getPercentageCacheKey(processId)), ImportExcelProcessVo.class);
        return vo;
    }

    public void downloadErrorImportData(HttpServletResponse response, String processId) {
        List<AuthGroupRuleSubjectImportDo> list = FastjsonUtil.toList(cacheService.getValue(getErrorCacheKey(processId)), AuthGroupRuleSubjectImportDo.class);
        ExcelUtils.downloadDataMapExcel(installExportEntity(AuthGroupRuleSubjectImportDo.class), convertObjectToMap(list), "导入失败员工信息", response);
    }

    private List<ExcelExportEntity> installExportEntity(Class clazz){
        List<ExcelExportEntity> colList = new ArrayList<>();
        Field[] fields = clazz.getDeclaredFields();
        for(Field f:fields){
            Excel annotation = f.getAnnotation(Excel.class);
            if(null == annotation){
                continue;
            }
            String name = annotation.name();
            String property = f.getName();
            String order = annotation.orderNum();
            double width = annotation.width();
            if(width == 0){
                width = 13;
            }
            ExcelExportEntity entity = new ExcelExportEntity(name, property);
            entity.setWidth(width);
            if(null!=order){
                entity.setOrderNum(Integer.valueOf(order));
            }
            colList.add(entity);
        }
        return colList;
    }

    private List<Map<String, Object>> convertObjectToMap(List<?> objs) {
        try {
            List<Map<String, Object>> list = new ArrayList<>();
            for(Object obj:objs){
                list.add(BeanUtil.bean2map(obj));
            }
            return list;
        }catch (Exception e){
            throw new ServerException("");
        }
    }

    protected String getErrorCacheKey(String processId){
        return String.format(ERROR_CACHE_KEY, processId);
    }

    protected String getPercentageCacheKey(String processId){
        return String.format(PERCENTAGE_CACHE_KEY, processId);
    }
}
