package com.caidaocloud.auth.service.caidao.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceActionEnum;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/5/6
 **/
@Data
@TableName("auth_resource")
public class AuthResourcePo implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 资源编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 语言编码
     */
    private String lang;

    /**
     * 资源分类
     */
    private ResourceCategoryEnum category;

    /**
     * API链接
     */
    private String url;

    /**
     * 动作
     */
    private ResourceActionEnum resourceAction;

    /**
     * 扩展字段
     */
    private String extension;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     *
     */
    private String parentCode;

    /**
     * 是否网关拦截
     */
    @TableField(value = "is_gateway_filter")
    private Boolean isGatewayFilter;

}
