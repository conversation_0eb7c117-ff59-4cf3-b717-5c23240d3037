package com.caidaocloud.auth.service.caidao.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/5/9
 **/
@Data
@TableName("auth_role_group_rule")
public class AuthRoleGroupRulePo implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 分组规则id
     */
    @TableField(value = "group_rule_id")
    private Long groupRuleId;

    /**
     * 角色id
     */
    @TableField(value = "role_id")
    private Long roleId;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除
     */
    private Boolean deleted;

}
