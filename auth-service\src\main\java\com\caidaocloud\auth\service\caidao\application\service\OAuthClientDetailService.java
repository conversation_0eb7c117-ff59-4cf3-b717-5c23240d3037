package com.caidaocloud.auth.service.caidao.application.service;

import com.caidaocloud.auth.service.caidao.application.dto.ClientDetail;
import com.caidaocloud.auth.service.caidao.domain.entity.OAuthClientDo;
import com.caidaocloud.auth.service.caidao.domain.repository.OAuthClientRepository;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.ClientRegistrationException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * 加载要认证的客户端信息
 * <AUTHOR>
 * @date 2021-11-11
 */
@Service
@Slf4j
public class OAuthClientDetailService implements ClientDetailsService {
    public static final String OAUTH2_CLIENT_KEY = "CAIDAOCLOUD_OAUTH2_CLIENT_%s";

    @Resource
    private OAuthClientRepository oauthClientRepository;

    @Resource
    private CacheService cacheService;

    // @PostConstruct
    // public void init() {
    //     List<OAuthClientDetailsDo> clientList = oauthClientDetailRepository.selectListAll();
    //     clientList.stream().forEach(oauthClient -> {
    //         String oauthClientKey = String.format(OAUTH2_CLIENT_KEY, oauthClient.getClientId());
    //         cacheService.cacheValue(oauthClientKey, FastjsonUtil.toJson(oauthClient));
    //     });
    // }

    @Override
    public ClientDetails loadClientByClientId(String clientId) throws ClientRegistrationException {
        OAuthClientDo oauth2Client = this.loadClientById(clientId);
        if(null == oauth2Client){
            throw new ClientRegistrationException("The app is disabled or does not exist");
        }

        ClientDetail clientDetail = new ClientDetail();
        clientDetail.setTenantId(oauth2Client.getTenantId());
        clientDetail.setBelongid(oauth2Client.getBelongId());
        clientDetail.setClientId(oauth2Client.getClientId());
        clientDetail.setClientSecret(oauth2Client.getClientSecret());
        clientDetail.setAccessTokenValiditySeconds(oauth2Client.getAccessTokenValidity());
        clientDetail.setRefreshTokenValiditySeconds(oauth2Client.getRefreshTokenValidity());
        clientDetail.setAuthorizedGrantTypes(Arrays.asList(oauth2Client.getAuthorizedGrantTypes().split(",")));
        clientDetail.setScope(Arrays.asList(oauth2Client.getScope()));

        /***
         * 设置这个会默认的跳过授权页面
         */
        // clientDetail.setAutoApproveScopes(Arrays.asList("all"));

        return clientDetail;
    }

    public OAuthClientDo loadClientById(String clientId){
        String oauthClientKey = String.format(OAUTH2_CLIENT_KEY, clientId);
        String cacheOauthClientDetailJson = cacheService.getValue(oauthClientKey);
        if (cacheOauthClientDetailJson == null) {
            OAuthClientDo client = oauthClientRepository.selectByClientId(clientId);
            cacheService.cacheValue(oauthClientKey, FastjsonUtil.toJson(client));
            return client;
        }
        OAuthClientDo oauth2Client = FastjsonUtil.toObject(cacheOauthClientDetailJson, OAuthClientDo.class);
        return oauth2Client;
    }
}
