CREATE TABLE IF NOT EXISTS auth_role_group_rule_$tenant_id
(
    id            BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键唯一标识',
    group_rule_id BIGINT      DEFAULT 0 COMMENT '分组规则id',
    role_id       BIGINT NOT NULL COMMENT '角色id',
    tenant_id     VARCHAR(50) DEFAULT "" COMMENT '租户id',
    create_time   BIGINT UNSIGNED NOT NULL COMMENT '创建时间',
    create_by     VARCHAR(50) DEFAULT "" COMMENT '创建者',
    update_time   BIGINT UNSIGNED DEFAULT NULL COMMENT '更新时间',
    update_by     VARCHAR(50) DEFAULT "" COMMENT '更新者',
    deleted       TINYINT UNSIGNED DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (id)
)ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT = '角色与控制分组关系';
