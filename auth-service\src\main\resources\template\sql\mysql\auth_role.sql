CREATE TABLE IF NOT EXISTS auth_role_$tenant_id
(
    id          BIGINT NOT NULL AUTO_INCREMENT COMMENT '唯一标识',
    tenant_id   VARCHAR(50)  DEFAULT "" COMMENT '租户id',
    name        VARCHAR(100) DEFAULT "" COMMENT '角色名称',
    code        VARCHAR(50)  DEFAULT "" COMMENT '角色编码',
    remark      VARCHAR(255) DEFAULT "" COMMENT '角色描述',
    role_type   SMALLINT     DEFAULT 0 COMMENT '角色类型;0:系统角色 1:自定义角色',
    device      VARCHAR(25)  DEFAULT "" COMMENT '适用设备',
    create_time BIGINT UNSIGNED NOT NULL COMMENT '创建时间',
    create_by   VARCHAR(50)  DEFAULT "" COMMENT '创建者',
    update_time BIGINT UNSIGNED DEFAULT NULL COMMENT '更新时间',
    update_by   VARCHAR(50)  DEFAULT "" COMMENT '更新者',
    is_enabled  TINYINT UNSIGNED DEFAULT 1 COMMENT '是否启用;0停用1启用',
    deleted     TINYINT UNSIGNED DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT = '权限角色';