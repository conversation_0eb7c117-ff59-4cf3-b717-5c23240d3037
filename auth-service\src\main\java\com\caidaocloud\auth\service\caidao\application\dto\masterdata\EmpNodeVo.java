package com.caidaocloud.auth.service.caidao.application.dto.masterdata;

import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("员工信息")
@Data
public class EmpNodeVo {

    @ApiModelProperty("员工姓名")
    private String name;

    @ApiModelProperty("工号")
    private String workno;

    @ApiModelProperty("员工英文名")
    private String enName;

    @ApiModelProperty("员工ID")
    private String empId;

    @ApiModelProperty("是否隐藏")
    private Boolean isHidden;

    @ApiModelProperty("离职日期")
    private Long leaveDate;

    @ApiModelProperty("岗位ID")
    private String post;

    @ApiModelProperty("用工类型")
    private DictSimple empType;

    @ApiModelProperty("所属组织Id")
    private String organize;


}