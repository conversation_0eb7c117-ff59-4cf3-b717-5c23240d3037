CREATE TABLE IF NOT EXISTS auth_role_group_rule_$tenant_id
(
    id            SERIAL NOT NULL,
    group_rule_id BIGINT      DEFAULT 0,
    role_id       BIGINT NOT NULL,
    tenant_id     VARCHAR(50) DEFAULT '',
    create_time   BIGINT NOT NULL,
    create_by     VARCHAR(50) DEFAULT '',
    update_time   BIGINT DEFAULT NULL,
    update_by     VARCHAR(50) DEFAULT '',
    deleted       SMALLINT    DEFAULT 0,
    PRIMARY KEY (id)
);

COMMENT
ON TABLE auth_role_group_rule_$tenant_id IS '角色与控制分组关系';
COMMENT
ON COLUMN auth_role_group_rule_$tenant_id.id IS '主键唯一标识';
COMMENT
ON COLUMN auth_role_group_rule_$tenant_id.group_rule_id IS '分组规则id';
COMMENT
ON COLUMN auth_role_group_rule_$tenant_id.role_id IS '角色id';
COMMENT
ON COLUMN auth_role_group_rule_$tenant_id.tenant_id IS '租户id';
COMMENT
ON COLUMN auth_role_group_rule_$tenant_id.create_time IS '创建时间';
COMMENT
ON COLUMN auth_role_group_rule_$tenant_id.create_by IS '创建者';
COMMENT
ON COLUMN auth_role_group_rule_$tenant_id.update_time IS '更新时间';
COMMENT
ON COLUMN auth_role_group_rule_$tenant_id.update_by IS '更新者';
COMMENT
ON COLUMN auth_role_group_rule_$tenant_id.deleted IS '是否删除';