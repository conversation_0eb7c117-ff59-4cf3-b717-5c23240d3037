package com.caidaocloud.auth.service.caidao.application.dto;

import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceActionEnum;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AuthResourceDto {
    /**
     * 资源编码
     */
    @ApiModelProperty("code")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 语言编码
     */
    @ApiModelProperty("多语言")
    private String lang;

    /**
     * 资源分类
     */
    @ApiModelProperty("分类")
    private ResourceCategoryEnum category;

    /**
     * API链接
     */
    @ApiModelProperty("url")
    private String url;

    /**
     * 动作
     */
    @ApiModelProperty("动作")
    private ResourceActionEnum resourceAction = ResourceActionEnum.VIEW;

    /**
     * 扩展字段
     */
    @ApiModelProperty("扩展字段")
    private String extension;

    @ApiModelProperty("父级code")
    private String parentCode;

    private String tenantId = "0";
}
