package com.caidaocloud.auth.service.caidao.domain.entity;

import lombok.Data;

/**
 * 授权码存储 Po
 * <AUTHOR>
 * @date 2021-11-11
 */
@Data
public class OAuthCodeStoreDo {
    private String id;

    /**
     * 授权码
     */
    private String code;

    /**
     * 认证客户端 Id
     */
    private String clientId;

    /**
     * 认证信息
     */
    private String authentication;

    /**
     * code 创建时间
     */
    private Long createTime;

    /**
     * code 失效时间
     */
    private Long expiresTime;
}
