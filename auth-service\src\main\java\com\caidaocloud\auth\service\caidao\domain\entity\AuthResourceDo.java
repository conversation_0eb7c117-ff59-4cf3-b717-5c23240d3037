package com.caidaocloud.auth.service.caidao.domain.entity;

import com.caidaocloud.auth.service.caidao.domain.repository.IAuthResourceRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceActionEnum;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.WebUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Data
public class AuthResourceDo {

    private Long id;

    /**
     * 资源编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 语言编码
     */
    private String lang;

    /**
     * 资源分类
     */
    private ResourceCategoryEnum category;

    /**
     * API链接
     */
    private String url;

    /**
     * 动作
     */
    private ResourceActionEnum resourceAction;

    /**
     * 扩展字段
     */
    private String extension;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 父级编码
     */
    private String parentCode;

    /**
     * 是否网关拦截
     */
    private Boolean isGatewayFilter;

    private static IAuthResourceRepository repository() {
        return SpringUtil.getBean(IAuthResourceRepository.class);
    }

    public static List<AuthResourceDo> list(ResourceCategoryEnum resourceCategory, String parentCode) {
        return repository().list(resourceCategory, parentCode);
    }

    public static List<AuthResourceDo> listByCode(String code) {
        return repository().listByCode(code);
    }

    public static List<AuthResourceDo> listByUrl(String url) {
        return repository().listByUrl(url);
    }

    public AuthResourceDo loadByCode(String code) {
        AuthResourceDo authResourceDo = repository().loadByCode(code);
        return authResourceDo;
    }

    public static List<AuthResourceDo> loadByCodes(List<String> codeList) {
        return repository().loadByCodes(codeList);
    }

    private AuthResourceDo loadByCode(String code, String lang, String... tenantIds) {
        AuthResourceDo authResourceDo = repository().loadByCode(code, lang, tenantIds);
        return authResourceDo;
    }

    public void updateByCode(boolean saas) {
        initLang(saas);
        AuthResourceDo authResourceDo = repository().loadByCode(code, lang, tenantId);
        if (null == authResourceDo) {
            throw new ServerException("code不存在");
        }
        initUpdateInfo(authResourceDo, saas);
        if (StringUtils.isNotEmpty(this.parentCode)) {
            checkParentCode(saas);
        }
        repository().updateByCode(this);
    }

    public Long create(boolean saas) {
        initLang(saas);
        initCreateInfo(saas);
        AuthResourceDo authResourceDo = loadByCode(code, lang, tenantId, "0");
        if (null == authResourceDo) {
            if (StringUtils.isNotEmpty(this.parentCode)) {
                checkParentCode(saas);
            }
            return repository().insert(this);
        } else {
            throw new ServerException("code已存在");
        }
    }

    public static void createBatch(List<AuthResourceDo> list, boolean saas) {
        if (CollectionUtils.isEmpty(list)) {
            log.info("list is empty");
            return;
        }
        var codeList = list.stream().map(e -> e.getCode()).collect(Collectors.toList());
        var existCodeList = loadByCodes(codeList);
        Map<String, AuthResourceDo> codeMap = new HashMap();
        if (!CollectionUtils.isEmpty(existCodeList)) {
            log.warn("code was existed, codeList={}", existCodeList);
            existCodeList.forEach(ard -> {
                codeMap.put(ard.getCode(), ard);
            });
            //throw new ServerException("code已存在");
        }

        List<AuthResourceDo> collect = list.stream().filter(ard -> !codeMap.containsKey(ard.getCode())).map(ard -> {
            ard.initLang(saas);
            ard.initCreateInfo(saas);
            return ard;
        }).collect(Collectors.toList());
        repository().insertBatch(collect);
    }

    private void initLang(boolean saas) {
        if (StringUtils.isEmpty(lang)) {
            if (saas) {
                lang = Locale.CHINESE.getLanguage();
            } else {
                lang = MessageHandler.getLocaleByRequest(WebUtil.getRequest()).getLanguage();
            }
        }
    }

    private void initUpdateInfo(AuthResourceDo existed, boolean saas) {
        if (saas) {
            tenantId = "0";
        } else {
            val sessionService = SpringUtil.getBean(ISessionService.class);
            tenantId = sessionService.getTenantId();
            val userId = sessionService.getUserInfo().getUserId();
            if (null != userId) {
                updateBy = userId.toString();
            }
        }
        updateTime = System.currentTimeMillis();
        createBy = existed.createBy;
        createTime = existed.createTime;
    }

    private void initCreateInfo(boolean saas) {
        if (saas) {
            tenantId = "0";
        } else {
            // 修复王旭只从 sessionService 中取租户的问题
            SecurityUserInfo securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
            tenantId = securityUserInfo.getTenantId();
            val userId = securityUserInfo.getUserId();
            if (null != userId) {
                createBy = userId.toString();
            }
        }
        createTime = System.currentTimeMillis();
    }



    private void checkParentCode(boolean saas) {
        AuthResourceDo parentAuthResourceDo = loadByCode(parentCode, lang, tenantId, "0");
        if (null == parentAuthResourceDo) {
            throw new ServerException("父code不存在");
        }
        if (saas) {
            if (!"0".equals(parentAuthResourceDo.tenantId)) {
                throw new ServerException("父code不存在");
            }
        }
    }

    public static void remove(String code) {
        repository().remove(code);
    }
}
