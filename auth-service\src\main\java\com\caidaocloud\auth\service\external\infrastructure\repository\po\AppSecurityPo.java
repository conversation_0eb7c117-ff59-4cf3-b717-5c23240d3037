package com.caidaocloud.auth.service.external.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.auth.service.external.domain.entity.AppSecurityEntity;
import com.caidaocloud.auth.service.external.infrastructure.enums.ExternalAuthEnum;
import com.caidaocloud.util.FastjsonUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.Map;

@Data
@Accessors(chain = true)
@TableName("external_app_security")
public class AppSecurityPo {
    @TableId(type = IdType.INPUT)
    private String id;
    private String tenantId;
    private String appKey;
    private String appSecret;
    private ExternalAuthEnum type;
    private String ext;
    private Long createTime;
    private Long updateTime;
    private Boolean deleted;

    public AppSecurityEntity convertEntity() {
        AppSecurityEntity appSecurityEntity = new AppSecurityEntity();
        BeanUtils.copyProperties(this, appSecurityEntity, "ext");
        if (StringUtils.isNotBlank(ext)) {
            Map<String, Object> extMap = FastjsonUtil.toObject(ext, Map.class);
            appSecurityEntity.setExt(extMap);
        }
        return appSecurityEntity;
    }
}