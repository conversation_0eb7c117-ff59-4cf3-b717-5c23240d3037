package com.caidaocloud.auth.service.caidao.application.feign;

import com.caidaocloud.auth.service.caidao.application.dto.TenantCommonConfigDto;
import com.caidaocloud.auth.service.caidao.application.dto.hr.EmpSearchColumnsDto;
import com.caidaocloud.auth.service.caidao.application.feign.fallback.HrFeignFallback;
import com.caidaocloud.auth.service.caidao.application.feign.fallback.MaintenanceFeignFallback;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * created by: FoAng
 * create time: 8/12/2022 3:51 下午
 */
@FeignClient(
        value = "${feign.rename.caidaocloud-maintenance-service:caidaocloud-maintenance-service}",
        fallback = MaintenanceFeignFallback.class,
        configuration = FeignConfiguration.class,
        contextId = "maintenance-service"
)
public interface IMaintenanceFeign {


    @GetMapping("/api/maintenance/v1/common/config")
    Result<TenantCommonConfigDto> tenantCommonConfig();
}
