package com.caidaocloud.auth.service.caidao.infrastructure.repository.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.AuthRolePermissionDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthRolePermissionPo;
import org.springframework.stereotype.Service;

@Service
public class AuthRolePermissionService extends ServiceImpl<AuthRolePermissionDao, AuthRolePermissionPo> {
}