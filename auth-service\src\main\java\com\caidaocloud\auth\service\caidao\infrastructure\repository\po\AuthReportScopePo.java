package com.caidaocloud.auth.service.caidao.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.auth.core.enums.AuthRoleScopeRestriction;
import com.caidaocloud.auth.core.enums.AuthRoleScopeTargetType;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.AuthRoleScopeComparator;
import lombok.Data;

@Data
@TableName("auth_report_scope")
public class AuthReportScopePo {
    @TableId(type = IdType.AUTO)
    private String id;
    private Long roleId;
    private AuthRoleScopeTargetType targetType = AuthRoleScopeTargetType.STANDARD;
    private String targets;
    private String filterProperty;
    private AuthRoleScopeComparator comparator = AuthRoleScopeComparator.IN;
    private AuthRoleScopeRestriction restriction;
    @TableField("{#key:values}")
    private String values;
    private String simpleValues;
}