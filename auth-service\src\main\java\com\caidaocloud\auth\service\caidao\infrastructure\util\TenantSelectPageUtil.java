package com.caidaocloud.auth.service.caidao.infrastructure.util;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 租户级分页查询
 *
 * <AUTHOR>
 * @date 2022/5/7
 **/
public class TenantSelectPageUtil {

    public static <T, P extends Page<T>> P selectPage(BaseMapper<T> baseMapper, P page, Wrapper<T> wrapper) {
        page.setSearchCount(false);
        Long count = baseMapper.selectCount(wrapper);
        P p = baseMapper.selectPage(page, wrapper);
        p.setTotal(count);
        return p;
    }

}
