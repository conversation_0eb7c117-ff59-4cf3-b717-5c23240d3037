package com.caidaocloud.auth.service.caidao.facade;

import com.caidaocloud.auth.core.dto.AuthRoleScopeFilterDetailDto;
import com.caidaocloud.auth.core.enums.AuthRoleScopeRestriction;
import com.caidaocloud.auth.service.caidao.application.dto.AuthRoleScopeDto;
import com.caidaocloud.auth.service.caidao.application.dto.AuthSubjectRoleDto;
import com.caidaocloud.auth.service.caidao.application.dto.subject.AuthSubjectOfRoleDto;
import com.caidaocloud.auth.service.caidao.application.dto.subject.AuthSubjectPageByRolesDto;
import com.caidaocloud.auth.service.caidao.application.dto.subject.AuthSubjectPageDto;
import com.caidaocloud.auth.service.caidao.application.dto.subject.AuthToSubjectDto;
import com.caidaocloud.auth.service.caidao.application.service.AuthGroupRuleSubjectService;
import com.caidaocloud.auth.service.caidao.facade.dto.AuthQueryDto;
import com.caidaocloud.auth.service.caidao.facade.vo.*;
import com.caidaocloud.auth.service.caidao.facade.vo.workflow.AuthRoleScopeFilterDetail;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Strings;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/11
 **/
@Api(tags = "权限员工")
@RestController
@RequestMapping("/api/auth/v1/subject")
@Slf4j
public class AuthGroupRuleSubjectController {
    @Autowired
    private AuthGroupRuleSubjectService authGroupRuleSubjectService;
    @Autowired
    private ISessionService sessionService;

    @ApiOperation(value = "角色查看用户分页")
    @PostMapping("/role/query-page")
    public Result<PageResult<AuthSubjectInfoVo>> queryPageSubjectOfRole(@RequestBody AuthSubjectPageDto authSubjectPageDto) {
        if (authSubjectPageDto == null) {
            throw new ServerException("parameter is null");
        }
        return Result.ok(authGroupRuleSubjectService.getSubjectPageOfRole(Lists.newArrayList(authSubjectPageDto.getRoleId()), authSubjectPageDto.getQueryPageBean()));
    }

    @ApiOperation(value = "根据多个角色查看用户分页")
    @PostMapping("/roles/query-page")
    public Result<PageResult<AuthSubjectInfoVo>> queryPageSubjectOfRoles(@RequestBody AuthSubjectPageByRolesDto authSubjectPageDto) {
        if (authSubjectPageDto == null) {
            throw new ServerException("parameter is null");
        }
        return Result.ok(authGroupRuleSubjectService.getSubjectPageOfRoles(authSubjectPageDto.getRoleIds(), authSubjectPageDto.getQueryPageBean()));
    }

    @ApiOperation(value = "角色下创建用户权限")
    @PostMapping("/role")
    public Result<String> createSubjectOfRole(@RequestBody AuthSubjectOfRoleDto authSubjectOfRoleDto) {
        authGroupRuleSubjectService.createSubjectOfRole(authSubjectOfRoleDto);
        return Result.ok("success");
    }

    @ApiOperation(value = "角色下删除用户权限")
    @DeleteMapping("/role")
    public Result<String> deleteSubjectOfRole(@RequestParam(value = "subjectIds", required = true)
                                              @ApiParam(value = "员工id,多值用逗号分隔")
                                                      String subjectIds,
                                              @RequestParam(value = "roleId", required = true)
                                              @ApiParam(value = "角色id")
                                                      Long roleId) {
        try {
            if (StringUtils.isNotBlank(subjectIds)) {
                List<Long> subjectIdList = Strings.split(",").call(subjectIds)
                        .filter(e -> StringUtils.isNotBlank(e)).map(e -> Long.parseLong(e)).toList();
                authGroupRuleSubjectService.deleteSubjectOfRole(roleId, subjectIdList);
            }
        } catch (Exception e) {
            log.error("deleteSubjectOfRole occur error, {} \n {}", e.getMessage(), e);
            throw new ServerException(e.getMessage());
        }
        return Result.ok("success");
    }

    @ApiOperation(value = "删除用户所有权限")
    @DeleteMapping("/role/all")
    public Result<String> deleteSubject(@RequestParam("subjectIds") String subjectIds) {
        authGroupRuleSubjectService.deleteSubjects(subjectIds);
        return Result.ok("success");
    }

    @ApiOperation(value = "获取用户所有权限CODE")
    @GetMapping("/resource/codes/all")
    public Result<List<String>> getResourceCodeListBySubjectId(@RequestParam(value = "subjectId", required = false) Long subjectId) {
        UserInfo userInfo = null;
        if (subjectId == null && (userInfo = sessionService.getUserInfo()) != null) {
            subjectId = (long) userInfo.getUserId();
        }
        log.info("subjectId:" + subjectId);
        if (subjectId == null) {
            return Result.ok(Lists.newArrayList());
        }
        val result = authGroupRuleSubjectService.getResourceCodeListBySubjectId(subjectId);
        log.info("code list:" + FastjsonUtil.toJson(result));
        return Result.ok(result);
    }

    @ApiOperation(value = "获取用户权限详情")
    @GetMapping("/resource/scope/detail")
    public Result<Map<String, String>> getResourceScopeDetailBySubjectId(@RequestParam("subjectId") Long subjectId, @RequestParam("parentCode") String parentCode) {
        return Result.ok(authGroupRuleSubjectService.getResourceScopeDetailBySubjectId(subjectId, parentCode));
    }

    @ApiOperation(value = "通过用户id获取用户权限code")
    @GetMapping("/resource/code")
    public Result<List<String>> getResourceCodeBySubjectId(@RequestParam("subjectId") Long subjectId, @RequestParam("parentCode") String parentCode) {
        return Result.ok(authGroupRuleSubjectService.getResourceCodeBySubjectId(subjectId, parentCode));
    }

    @ApiOperation(value = "获取用户所有权限url")
    @GetMapping("/resource/url/all")
    public Result<List<String>> getResourceUrlListBySubjectId(@RequestParam(value = "subjectId", required = false) Long subjectId) {
        if (subjectId == null) {
            subjectId = (long) sessionService.getUserInfo().getUserId();
        }
        return Result.ok(authGroupRuleSubjectService.getResourceUrlListBySubjectId(subjectId));
    }

    @ApiOperation(value = "员工权限导入")
    @PostMapping("/authorize")
    public Result<List<AuthImportSubjectErrorVo>> authorizationToUser(@RequestBody List<AuthToSubjectDto> authToSubjectList) {
        return Result.ok(authGroupRuleSubjectService.authorizationToUser(authToSubjectList));
    }
    @ApiOperation(value = "员工权限导入--覆盖")
    @PostMapping("/authorize/refresh")
    public Result<List<AuthImportSubjectErrorVo>> refreshAuthorizationToUser(@RequestBody List<AuthToSubjectDto> authToSubjectList) {
        return Result.ok(authGroupRuleSubjectService.refreshAuthorizationToUser(authToSubjectList));
    }

    @PostMapping("/authorize/admin")
    @ApiOperation("导入默认管理员权限")
    public Result<?> authDefaultAdminRole(List<Long> subjects) {
        if (CollectionUtils.isNotEmpty(subjects)) {
            authGroupRuleSubjectService.authDefaultAdmin(subjects);
            return Result.ok("操作成功");
        }
        return Result.ok();
    }


    @ApiOperation(value = "获取用户角色名称")
    @GetMapping("/role/name")
    public Result<List<AuthSubjectAndRoleVo>> getRoleNameBySubjectIds(@RequestParam(value = "subjectIds", required = true)
                                                                      @ApiParam(value = "员工id,多值用逗号分隔") String subjectIds) {
        List<AuthSubjectAndRoleVo> roleBySubjectIds = null;
        if (StringUtils.isNotBlank(subjectIds)) {
            try {
                List<Long> subjectIdList = Strings.split(",").call(subjectIds)
                        .filter(e -> StringUtils.isNotBlank(e))
                        .map(e -> Long.parseLong(e)).stream()
                        .distinct().collect(Collectors.toList());
                roleBySubjectIds = authGroupRuleSubjectService.getRoleBySubjectIds(subjectIdList);
            } catch (Exception e) {
                log.error("getRoleNameBySubjectIds occur error, {} \n {}", e.getMessage(), e);
                throw new ServerException(e.getMessage());
            }
        }
        if (roleBySubjectIds == null) {
            roleBySubjectIds = Lists.newArrayList();
        }
        return Result.ok(roleBySubjectIds);
    }

    @ApiOperation(value = "post获取用户角色名称")
    @PostMapping("/role/name")
    public Result<List<AuthSubjectAndRoleVo>> getRoleNameBySubjectIds(@RequestBody AuthQueryDto queryDto) {
        List<AuthSubjectAndRoleVo> roleBySubjectIds = null;
        if (Objects.isNull(queryDto)) {
            roleBySubjectIds = Lists.newArrayList();
        }
        if (StringUtils.isNotBlank(queryDto.getSubjectIds())) {
            try {
                List<Long> subjectIdList = Strings.split(",").call(queryDto.getSubjectIds())
                        .filter(e -> StringUtils.isNotBlank(e))
                        .map(e -> Long.parseLong(e)).stream()
                        .distinct().collect(Collectors.toList());
                roleBySubjectIds = authGroupRuleSubjectService.getRoleBySubjectIds(subjectIdList);
            } catch (Exception e) {
                log.error("getRoleNameBySubjectIds occur error, {} \n {}", e.getMessage(), e);
                throw new ServerException(e.getMessage());
            }
        }
        if (roleBySubjectIds == null) {
            roleBySubjectIds = Lists.newArrayList();
        }
        return Result.ok(roleBySubjectIds);
    }

    @ApiOperation(value = "获取用户角色名称")
    @GetMapping("/role/list")
    public Result<List<Long>> getRoleIdsBySubject(@RequestParam Long subjectId) {
        return Result.ok(authGroupRuleSubjectService.getRoleIdsBySubject(subjectId));
    }

    @ApiOperation(value = "获取用户数据范围权限")
    @GetMapping("/role/scope/list")
    public Result<List<AuthRoleScopeFilterDetailDto>> getScopeBySubject(@RequestParam String identifier,
                                                                        @RequestParam Long subjectId,
                                                                        @RequestParam(value = "dymaicTarget", defaultValue = "", required = false) String dymaicTarget) {
        val scopes = authGroupRuleSubjectService.getScopeBySubjectId(subjectId);
        log.info("scopeFilterDetails for  identifier " + identifier + " " + subjectId + " is " + FastjsonUtil.toJson(scopes));
        List<AuthRoleScopeFilterDetail> details = AuthRoleScopeFilterDetail.fromStandard(scopes, identifier, dymaicTarget);
        return Result.ok(FastjsonUtil.convertList(details, AuthRoleScopeFilterDetailDto.class));
    }

    @ApiOperation(value = "获取用户数据范围权限可选值")
    @GetMapping("/role/scope/available")
    public Result<Map<String, List<AuthRoleScopeRestriction>>> getStandardAuthScope() {
        return Result.ok(authGroupRuleSubjectService.getStandardAuthScope());
    }

    @ApiOperation(value = "获取用户数据范围权限可选值")
    @GetMapping("/role/scope/available/list")
    public Result<List<AuthRoleScopeTargetVo>> getAuthScopeList() {
        return Result.ok(authGroupRuleSubjectService.loadAuthScopeKvList());
    }

    @ApiOperation(value = "获取异动数据适用范围")
    @GetMapping("/role/scope/transfer/list")
    public Result<List<AuthRoleScopeTargetVo>> getAuthScopeOfTransferList() {
        return Result.ok(authGroupRuleSubjectService.loadTransferScopeCode());
    }

    @ApiOperation(value = "获取用户数据范围权限枚举")
    @GetMapping("/role/scope/restriction/list")
    public Result<List<AuthScopeRestrictionVo>> getRestrictioOfScopeBySubject(@RequestParam("subjectId") Long subjectId) {
        var scopes = authGroupRuleSubjectService.getScopeBySubjectId(subjectId);
        return Result.ok(AuthRoleScopeDto.convertToScopeRestrictionVo(scopes));
    }

    @ApiOperation("用户修改角色信息")
    @PostMapping("/emp/role/change")
    public Result empChangeRole(@RequestBody AuthSubjectRoleDto subjectRoleDto) {
        authGroupRuleSubjectService.empChangeRole(subjectRoleDto);
        return Result.ok();
    }

    @ApiOperation(value = "获取用户报表数据范围权限枚举")
    @GetMapping("/role/report/scope/restriction/list")
    public Result<List<AuthScopeRestrictionVo>> getRestrictioOfReportScopeBySubject(@RequestParam("subjectId") Long subjectId,
                                                                                    @RequestParam("appId") String appId) {
        return Result.ok(AuthRoleScopeDto.convertToScopeRestrictionVo(authGroupRuleSubjectService.getReportScopeBySubjectId(subjectId, appId)));
    }
}