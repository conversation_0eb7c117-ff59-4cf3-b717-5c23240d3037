
package com.caidaocloud.auth.service.caidao.application.service;

import java.util.List;

import com.caidaocloud.auth.service.caidao.domain.entity.TenantBaseInfoDo;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class TenantBaseInfoService {
    @Autowired
    private TenantBaseInfoDo tenantBaseInfoDo;

    @Transactional
    public void syncSave(List<TenantBaseInfoDo> dataList) throws Exception {
        tenantBaseInfoDo.syncSave(dataList);
    }

    @Transactional
    public void deleteByIds(List<Long> tenantIds) {
        tenantBaseInfoDo.deleteByIds(tenantIds);
    }
    //
    // @Transactional
    // public void softDeleteByIds(List<Long> tenantIds) {
    //     tenantBaseInfoDo.softDeleteByIds(tenantIds);
    // }

}
