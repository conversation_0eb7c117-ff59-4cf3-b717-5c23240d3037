package com.caidaocloud.auth.service.caidao.application.service;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.auth.service.caidao.application.dto.AuthRoleDto;
import com.caidaocloud.auth.service.caidao.application.dto.AuthRolePermissionDto;
import com.caidaocloud.auth.service.caidao.application.dto.subject.AuthSubjectOfRoleDto;
import com.caidaocloud.auth.service.caidao.application.repository.IInitTenantRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.AdapterDeviceEnum;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.SystemRoleEnum;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.service.ISessionService;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.caidaocloud.auth.service.caidao.infrastructure.constant.CodeConstant.SYSTEM_ROLE_TYPE;

/**
 * 租户表初始化
 *
 * <AUTHOR>
 * @date 2022/5/7
 **/
@Service
@Slf4j
public class AuthInitTableService {

    @NacosValue("${caidaocloud.dbType:mysql}")
    private String dbType;

    @javax.annotation.Resource
    private IInitTenantRepository initTenantRepository;

    @Autowired
    private ISessionService userService;

    @Autowired
    private AuthResourceService authResourceService;

    @Autowired
    private AuthRoleService authRoleService;

    @Autowired
    private AuthGroupRuleSubjectService authGroupRuleSubjectService;

    @Transactional(rollbackFor = Exception.class)
    public void initTable() {
        try {
            PathMatchingResourcePatternResolver patternResolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = patternResolver.getResources(getSqlTemplateLocation());
            for (Resource resource : resources) {
                var sql = IOUtils.toString(resource.getInputStream(), "UTF-8");
                String tenantId = userService.getTenantId();
                if (StringUtils.isBlank(tenantId)) {
                    throw new ServerException("this user tenantId is null");
                }
                if (StringUtils.isNotBlank(sql)) {
                    sql = sql.replaceAll("\\$tenant_id", tenantId);
                    initTenantRepository.initTenantTable(sql);
                }
            }
            initSystemRole(SystemRoleEnum.ADMIN);
        } catch (Exception e) {
            log.error("initTable error, {}", e);
            throw new ServerException(e.getMessage());
        }
    }

    private String getSqlTemplateLocation() {
        return String.format("%s/template/sql/%s/*.sql", ResourcePatternResolver.CLASSPATH_ALL_URL_PREFIX, dbType);
    }

    public Long initSystemRole(SystemRoleEnum role) {
        var roleList = authRoleService.getRoleByCode(Lists.newArrayList(role.name()));
        if (!CollectionUtils.isEmpty(roleList)) {
            return roleList.get(0).getId();
        }
        var resourceList = authResourceService.list(null, null);
        List<AuthRolePermissionDto> authRolePermissionList = Sequences.sequence(resourceList)
                .map(e -> {
                    var authRolePermissionDto = new AuthRolePermissionDto();
                    authRolePermissionDto.setCode(e.getCode());
                    authRolePermissionDto.setParentCode(e.getParentCode());
                    authRolePermissionDto.setCategory(e.getCategory());
                    return authRolePermissionDto;
                }).toList();
        var authRoleDto = new AuthRoleDto();
        authRoleDto.setName(role.chName);
        authRoleDto.setI18nName(Maps.map("default", role.chName));
        authRoleDto.setCode(role.name());
        authRoleDto.setRemark("");
        authRoleDto.setRoleType(SYSTEM_ROLE_TYPE);
        authRoleDto.setDevice(AdapterDeviceEnum.toList());
        authRoleDto.setAuthRolePermissionList(authRolePermissionList);
        return authRoleService.saveOrUpdateRole(authRoleDto);
    }

    public void initAdmin(String userId) {
        // 初始化admin角色
        Long roleId = initSystemRole(SystemRoleEnum.ADMIN);
        // admin角色绑定
        AuthSubjectOfRoleDto dto = new AuthSubjectOfRoleDto();
        dto.setRoleId(roleId);
        dto.setSubjectIdList(Lists.newArrayList(Long.valueOf(userId)));
        authGroupRuleSubjectService.createSubjectOfRole(dto);
    }


    public void initConfig(String userId) {
        // 初始化admin角色
        Long roleId = initSystemRole(SystemRoleEnum.CONFIG);
        // admin角色绑定
        AuthSubjectOfRoleDto dto = new AuthSubjectOfRoleDto();
        dto.setRoleId(roleId);
        dto.setSubjectIdList(Lists.newArrayList(Long.valueOf(userId)));
        authGroupRuleSubjectService.createSubjectOfRole(dto);
    }

}
