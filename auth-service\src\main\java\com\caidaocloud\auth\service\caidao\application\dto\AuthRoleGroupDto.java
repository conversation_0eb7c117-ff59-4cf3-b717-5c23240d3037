package com.caidaocloud.auth.service.caidao.application.dto;

import com.caidaocloud.auth.service.caidao.infrastructure.entity.ConditionTree;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/9
 **/
@Data
@ApiModel(value = "角色组dto")
public class AuthRoleGroupDto {

    @ApiModelProperty("角色组id")
    private Long id;

    @ApiModelProperty("角色组名称")
    private String name;

    @ApiModelProperty("多语言名称")
    private Map<String,Object> i18nName;

    @ApiModelProperty("角色组描述")
    private String remark;

    @ApiModelProperty("规则表达式")
    private ConditionTree expression;

    @ApiModelProperty("匹配角色")
    private List<Long> roleIdList;

    @ApiModelProperty(value = "是否是角色默认角色组", hidden = true)
    private Boolean isDefaultRoleGroup;

}
