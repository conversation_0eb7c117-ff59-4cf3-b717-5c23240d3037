package com.caidaocloud.auth.service.caidao.domain.repository;

import java.lang.reflect.Field;
import java.util.List;

import com.caidaocloud.auth.service.AuthApplication;
import com.caidaocloud.auth.service.caidao.domain.entity.UserAccountBaseInfo;
import com.caidaocloud.auth.service.caidao.domain.entity.UserBaseInfoDo;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.service.SessionServiceImpl;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.RequestHelper;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/5/6
 */
@SpringBootTest(classes = AuthApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
@Slf4j
public class IUserBaseInfoRepositoryTests {

	@Autowired
	private IUserBaseInfoRepository userBaseInfoRepository;


	@Before
	public void setUp() throws Exception {
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("8");
		userInfo.setUserId(0L);
		userInfo.setEmpId(0L);
		SecurityUserUtil.setSecurityUserInfo(userInfo);

		RequestHelper.getRequest()
				.setAttribute("_caidaocloud_userinfo_request_threadlocal_", ObjectConverter.convert(userInfo, UserInfo.class));

// Get the instance of SessionServiceImpl which is already created by Spring bean
		SessionServiceImpl service = (SessionServiceImpl) SpringUtil.getBean(ISessionService.class);
		try {
			// Get the private field "threadLocalCache" from the SessionServiceImpl class
			Field field = SessionServiceImpl.class.getDeclaredField("threadLocalCache");
			// Set the accessibility of the field to true, as it is private
			field.setAccessible(true);
			// Set the value of the "threadLocalCache" field to true
			field.set(service, true);
		}
		catch (NoSuchFieldException | IllegalAccessException e) {
			e.printStackTrace();
		}
	}

	private List<String> accounts = Lists.list("AVT-admin", "AVT-*********");

	@Test
	public void testSelect() {
		List<UserBaseInfoDo> userBaseInfoByAccounts = userBaseInfoRepository.selectUserBaseInfoByAccounts(accounts);
		List<UserAccountBaseInfo> userAccountBaseInfos = userBaseInfoRepository.selectByAccounts(accounts);
		assertNotNull(userBaseInfoByAccounts);
		assertNotNull(userAccountBaseInfos);
		assertFalse(userBaseInfoByAccounts.isEmpty());
		assertFalse(userAccountBaseInfos.isEmpty());
	}
}