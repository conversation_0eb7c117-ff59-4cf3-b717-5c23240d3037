package com.caidaocloud.auth.service.caidao.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.auth.service.caidao.infrastructure.entity.ConditionTree;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsObject;
import com.caidaocloud.util.FastjsonUtil;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/5/9
 **/
@Data
@TableName("auth_group_rule")
public class AuthGroupRulePo implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 角色id
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 角色多语言名称
     */
    private String i18nName;

    /**
     * 用户匹配规则
     */
    private String expressionName;


    /**
     * 角色组描述
     */
    private String remark;

    /**
     * 最近刷新时间
     */
    private Long lastRefreshed;

    /**
     * 规则表达式
     */
    @DisplayAsObject
    private ConditionTree expression;

    /**
     * 状态;0：启用 1：禁用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否是角色默认角色组: 1是，0否
     */
    @TableField(value = "is_default_role_group")
    private Boolean isDefaultRoleGroup;

    /**
     * 是否删除
     */
    private Boolean deleted;

    public AuthGroupRulePo toEntity() {
        AuthGroupRulePo authGroupRulePo = FastjsonUtil.convertObject(this, AuthGroupRulePo.class);
        return authGroupRulePo;
    }


}
