package com.caidaocloud.auth.service.caidao.facade;

import com.caidaocloud.auth.service.caidao.application.dto.AuthResourceDto;
import com.caidaocloud.auth.service.caidao.application.service.AuthResourceService;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthResourceDo;
import com.caidaocloud.auth.service.caidao.facade.vo.AuthResourceUrlVo;
import com.caidaocloud.auth.service.caidao.facade.vo.AuthResourceVo;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Sequence;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "权限资源操作")
@Controller
@RequestMapping("/api/auth/v1")
public class AuthResourceController {
    @Autowired
    private AuthResourceService authResourceService;

    @ApiOperation(value = "权限资源列表")
    @GetMapping("/resource/list")
    @ResponseBody
    public Result<List<AuthResourceVo>> list(@RequestParam(value = "resourceCategory", required = false) ResourceCategoryEnum resourceCategory,
                                             @RequestParam(value = "parentCode", required = false) String parentCode) {
        val list = authResourceService.list(resourceCategory, parentCode);
        return Result.ok(
                FastjsonUtil.convertList(list, AuthResourceVo.class)
        );
    }

    @ApiOperation(value = "权限资源列表")
    @GetMapping("/resource/list/byCode")
    @ResponseBody
    public Result<List<AuthResourceVo>> list(@RequestParam(value = "code", required = false) String code) {
        val list = authResourceService.listByCode(code);
        return Result.ok(
                FastjsonUtil.convertList(list, AuthResourceVo.class)
        );
    }

    @ApiOperation("获取资源详情")
    @GetMapping("/resource/url/detail")
    @ResponseBody
    public Result<AuthResourceUrlVo> detail(@RequestParam("url") String url) {
        val resourceVo = authResourceService.detailByUrl(url);
        return Result.ok(resourceVo);
    }


    @ApiOperation(value = "保存权限资源")
    @PostMapping("/resource")
    @ResponseBody
    public Result<Long> create(@RequestBody AuthResourceDto dto) {
        Long id = authResourceService.create(FastjsonUtil.convertObject(dto,
                AuthResourceDo.class), false);
        return Result.ok(id);
    }

    @ApiOperation(value = "保存多个权限资源")
    @PostMapping("/resources")
    @ResponseBody
    public Result<String> create(@RequestBody List<AuthResourceDto> resourceList) {
        authResourceService.createList(resourceList, false);
        return Result.ok("success");
    }

    @ApiOperation(value = "删除权限资源")
    @DeleteMapping("/resource")
    @ResponseBody
    public Result<String> delete(@RequestParam("resourceCode") String resourceCode) {
        authResourceService.remove(resourceCode);
        return Result.ok("success");
    }

    @ApiOperation(value = "删除权限资源")
    @DeleteMapping("/resources")
    @ResponseBody
    public Result<String> delete(@RequestBody List<AuthResourceDto> removeList) {
        authResourceService.removeList(removeList);
        return Result.ok("success");
    }

    @ApiOperation(value = "保存权限资源/所有租户")
    @PostMapping("/resource/saas")
    @ResponseBody
    public Result<Long> createSaas(@RequestBody AuthResourceDto dto) {
        Long id = authResourceService.create(FastjsonUtil.convertObject(dto,
                AuthResourceDo.class), true);
        return Result.ok(id);
    }


    @ApiOperation(value = "更新权限资源")
    @PutMapping("/resource")
    @ResponseBody
    public Result<String> update(@RequestBody AuthResourceDto dto) {
        authResourceService.update(FastjsonUtil.convertObject(dto, AuthResourceDo.class), false);
        return Result.ok("success");
    }

    @ApiOperation(value = "更新权限资源/所有租户")
    @PutMapping("/resource/saas")
    @ResponseBody
    public Result<String> updateSaas(@RequestBody AuthResourceDto dto) {
        authResourceService.update(FastjsonUtil.convertObject(dto, AuthResourceDo.class), true);
        return Result.ok("success");
    }

    @ApiOperation(value = "更新多个权限资源")
    @PutMapping("/resources")
    @ResponseBody
    public Result<String> update(@RequestBody List<AuthResourceDto> resourceList) {
        authResourceService.updateList(resourceList);
        return Result.ok("success");
    }

    @ApiOperation(value = "加在资源")
    @PostMapping("/loadResource")
    @ResponseBody
    public Result<List<AuthResourceVo>> loadResource(@RequestBody List<String> codeList) {
        val sequence = authResourceService.loadResources(codeList);
        if (sequence.isEmpty()) {
            return Result.ok(Lists.newArrayList());
        }
        return Result.ok(FastjsonUtil.convertList(sequence.toList(), AuthResourceVo.class));
    }
}