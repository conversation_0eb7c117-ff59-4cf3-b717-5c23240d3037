package com.caidaocloud.auth.service.caidao.application.handler;

import com.caidaocloud.auth.service.caidao.domain.entity.OAuthOpenTenantDo;
import com.caidaocloud.auth.service.caidao.domain.repository.OAuthOpenTenantRepository;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.StringUtil;
import com.mongodb.DuplicateKeyException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Service("DynamicAdaptTenant")
public class DynamicAdaptTenantHandler implements IHandlerType {
    private static SnowflakeUtil snowFlake = new SnowflakeUtil(1, 1);
    private final String OAUTH_TENANT_BELONGID_INCR = "oauth_tenant_belongid_incr";

    @Resource
    private OAuthOpenTenantRepository authOpenTenantRepository;
    @Resource
    private CacheService cacheService;

    @Override
    public void executeHandlerType(Map<String, String> sourceMap, Map<String, Object> targetMap) {
        String tenantKey = sourceMap.getOrDefault("tenantCorpKey", "");
        String clientId = sourceMap.getOrDefault("client_id", "");
        if(StringUtil.isEmpty(tenantKey) || StringUtil.isEmpty(clientId)){
            log.error("tenantCorpKey or clientId is empty, executeHandlerType end.");
            throw new BadCredentialsException("The current app has been disabled or the status is abnormal");
        }

        OAuthOpenTenantDo openTenant = authOpenTenantRepository.selectTenantByCorpKey(clientId, tenantKey);
        if(null != openTenant){
            targetMap.put("belongId", openTenant.getBelongId());
            return;
        }

        openTenant = new OAuthOpenTenantDo();
        openTenant.setBelongId(cacheService.increment(OAUTH_TENANT_BELONGID_INCR));
        openTenant.setClientId(clientId);
        openTenant.setCorpKey(tenantKey);
        openTenant.setCreateTime(System.currentTimeMillis());

        try{
            authOpenTenantRepository.insertTenant(openTenant);
        } catch (DuplicateKeyException e){
            log.error("insertTenant DuplicateKeyException err,{}", e.getMessage(), e);
            Long newBelongId = cacheService.increment(OAUTH_TENANT_BELONGID_INCR, authOpenTenantRepository.getMaxBelongId());
            openTenant.setBelongId(newBelongId);
            log.info("insertTenant newBelongId={}, clientId={}, corpKey={}", newBelongId, openTenant.getClientId(),openTenant.getCorpKey());
            authOpenTenantRepository.insertTenant(openTenant);
        }

        targetMap.put("belongId", openTenant.getBelongId());
    }

}
