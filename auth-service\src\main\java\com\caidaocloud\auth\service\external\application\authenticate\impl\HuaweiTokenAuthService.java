package com.caidaocloud.auth.service.external.application.authenticate.impl;

import com.caidaocloud.auth.service.external.application.authenticate.IExternalAuthService;
import com.caidaocloud.auth.service.external.infrastructure.enums.ExternalAuthEnum;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Maps;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 华为token认证服务
 * ref: https://support.huaweicloud.com/devg-apig/apig-dev-180307020.html
 *
 * <AUTHOR>
 * @date 2024/11/19
 **/
@Slf4j
@Service
public class HuaweiTokenAuthService implements IExternalAuthService {
    @Autowired
    private RestTemplate restTemplate;
    private final String TOKEN_URI = "/v3/auth/tokens";
    private final String BODY_TEMPLATE = "{\"auth\":{\"identity\":{\"methods\":[\"password\"],\"password\":{\"user\":{\"name\":\"{user_name}\",\"domain\":{\"name\":\"{domain_user_name}\"},\"password\":\"{password}\"}}},\"scope\":{\"project\":{\"id\":\"{project_id}\"}}}}";

    @Override
    public Map<String, Object> sso(HttpServletRequest request, HttpServletResponse response) {
        val userInfo = SecurityUserUtil.getSecurityUserInfo();
        PreCheck.preCheckArgument(userInfo == null || StringUtils.isBlank(userInfo.getTenantId()) || userInfo.getUserId() == null, ErrorMessage.fromCode("USER_NO_LOG_IN"));
        val appSecurityEntity = getgetAppSecurity(userInfo.getTenantId(), ExternalAuthEnum.HUAWEI_TOKEN);
        if (MapUtils.isEmpty(appSecurityEntity.getExt())) {
            log.info("not find configuration of HUAWEI_TOKEN, appSecurity={}", FastjsonUtil.toJson(appSecurityEntity));
            throw new ServerException("not find configuration of HUAWEI_TOKEN");
        }
        val userName = appSecurityEntity.getExt().getOrDefault("user_name", "").toString();
        val domainUserName = appSecurityEntity.getExt().getOrDefault("domain_user_name", "").toString();
        val password = appSecurityEntity.getExt().getOrDefault("password", "").toString();
        val projectId = appSecurityEntity.getExt().getOrDefault("project_id", "").toString();
        var domain = appSecurityEntity.getExt().getOrDefault("domain", "").toString();
        val b = StringUtils.isBlank(domainUserName) || StringUtils.isBlank(userName) || StringUtils.isBlank(password) || StringUtils.isBlank(projectId) || StringUtils.isBlank(domain);
        if (b) {
            log.error("configuration is illegal, userName={} password{} projectId={} domain={}", userName, password, projectId, domain);
            PreCheck.preCheckArgument(b, "configuration is illegal");
        }
        val body = StringUtils.replaceEach(BODY_TEMPLATE, new String[]{"{user_name}", "{password}", "{project_id}", "{domain_user_name}"}, new String[]{userName, password, projectId, domainUserName});
        if (StringUtils.lastIndexOf(domain, "/") == domain.length() - 1) {
            domain = StringUtils.substring(domain, 0, domain.length() - 1);
        }
        val headers = new HttpHeaders();
        headers.set("content-type", "application/json");
        val responseEntity = restTemplate.exchange(String.format("%s%s", domain, TOKEN_URI), HttpMethod.POST, new HttpEntity(body, headers), String.class);
        if (responseEntity.getStatusCode() == HttpStatus.OK || responseEntity.getStatusCode() == HttpStatus.CREATED) {
            log.info("request atr url={} body={} data={}", domain, body, responseEntity.getBody());
            Map<String, Object> map = FastjsonUtil.toObject(responseEntity.getBody(), Map.class);
            HttpHeaders responseHeader = responseEntity.getHeaders();
            if (responseHeader.containsKey("X-Subject-Token") && CollectionUtils.isNotEmpty(responseHeader.get("X-Subject-Token"))) {
                String data = responseHeader.get("X-Subject-Token").get(0);
                if (StringUtils.isNotBlank(data)) {
                    map.getOrDefault("token", "");
                    return Maps.map("X-Subject-Token", data, "expireTime", DateUtil.convertISO8601ToUTCMillis(String.valueOf(((Map)map.getOrDefault("token", Maps.map())).getOrDefault("expires_at", null))));
                }
                log.info("body={} result data = {}", body, data);
                throw ServerException.globalException(ErrorMessage.fromCode("UNKNOWN_ERROR"));
            }
        }
        throw ServerException.globalException(ErrorMessage.fromCode("NO_ATS_AUTH"));
    }
}
