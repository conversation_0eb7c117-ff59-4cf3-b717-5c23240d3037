package com.caidaocloud.auth.service.caidao.application.service;

import com.caidaocloud.auth.core.util.ObjectConvertUtil;
import com.caidaocloud.auth.service.caidao.application.dto.AuthRoleGroupDto;
import com.caidaocloud.auth.service.caidao.application.dto.hr.EmpSearchColumnsDto;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleGroupDo;
import com.caidaocloud.auth.service.caidao.domain.entity.UserBaseInfoDo;
import com.caidaocloud.auth.service.caidao.domain.repository.ISysEmpRepositoy;
import com.caidaocloud.auth.service.caidao.domain.service.AuthGroupRuleDomainService;
import com.caidaocloud.auth.service.caidao.domain.service.AuthGroupRuleSubjectDomainService;
import com.caidaocloud.auth.service.caidao.domain.service.AuthRoleDomainService;
import com.caidaocloud.auth.service.caidao.domain.service.UserBaseInfoDomainService;
import com.caidaocloud.auth.service.caidao.facade.dto.AuthGroupRuleDto;
import com.caidaocloud.auth.service.caidao.facade.vo.AuthGroupRuleVo;
import com.caidaocloud.auth.service.caidao.infrastructure.entity.ConditionTree;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthGroupRulePo;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.paas.common.feign.IConditionFeign;
import com.caidaocloud.hrpaas.paas.match.vo.ConditionDataVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Strings;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 角色组
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthGroupRuleService {
    @Autowired
    private AuthGroupRuleDomainService authGroupRuleDomainService;
    @Resource
    private AuthGroupRuleSubjectService authGroupRuleSubjectService;
    @Autowired
    private AuthGroupRuleSubjectDomainService authGroupRuleSubjectDomainService;
    @Autowired
    private ISysEmpRepositoy empRepositoy;
    @Resource
    private UserBaseInfoDomainService userBaseInfoDomainService;
    @Resource
    private AuthRoleDomainService authRoleDomainService;

    /**
     * 保存或更新角色组
     *
     * @param authRoleGroupDto
     */
    public void saveOrUpdateRoleGroup(AuthRoleGroupDto authRoleGroupDto) {
        authRoleGroupDto.setName(String.valueOf(authRoleGroupDto.getI18nName().get("default")));
        if (authRoleGroupDto == null) {
            throw new ServerException("parameter is null");
        }
        var authRoleGroupDo = FastjsonUtil.convertObject(authRoleGroupDto, AuthRoleGroupDo.class);
        authRoleGroupDo.setI18nName(FastjsonUtil.toJson(authRoleGroupDto.getI18nName()));
        authGroupRuleDomainService.saveOrUpdateRoleGroup(authRoleGroupDo);
    }

    public AuthGroupRuleDto getGroupRuleById(Long id) {

        AuthGroupRulePo authGroupRulePo = authGroupRuleDomainService.getGroupRuleById(id);
        PreCheck.preCheckArgument(StringUtils.isEmpty(authGroupRulePo.getName()), ErrorMessage.fromCode("NAME_NOT_FOUND"));
        AuthGroupRuleDto convert = ObjectConvertUtil.convert(authGroupRulePo, AuthGroupRuleDto.class, (t1, v1) -> {
            if (t1.getI18nName()!=null){
                v1.setI18nName(FastjsonUtil.toObject(t1.getI18nName(), Map.class));
            }
            Map<String,Object> i18nName=new HashMap<>();
            i18nName.put("default",t1.getName());
            v1.setI18nName(i18nName);
        });
        return convert;

    }
    public List<AuthGroupRuleVo> getGroupRuleByIds(List<Long> ids) {

        List<AuthGroupRulePo> authGroupRulePoList = authGroupRuleDomainService.getGroupRuleByIds(ids);
        return FastjsonUtil.convertList(authGroupRulePoList, AuthGroupRuleVo.class);

    }

    public int deleteGroupRule(List<Long> idList) {

        return authGroupRuleDomainService.deleteGroupRule(idList);
    }

    public PageResult<AuthGroupRuleDto> pageList(QueryPageBean queryPageBean) {

        var pageOfRole = authGroupRuleDomainService.pageList(queryPageBean);
//        var authRoleVos = FastjsonUtil.convertList(pageOfRole.getItems(), AuthGroupRuleDto.class);
        List<AuthGroupRuleDto> ruleDtos = ObjectConvertUtil.convertList(pageOfRole.getItems(), AuthGroupRuleDto.class,
                (t1, v1) -> {
                    v1.setI18nName(FastjsonUtil.toObject(t1.getI18nName() == null ? "" : t1.getI18nName(), Map.class));
                });
        var pageResult = new PageResult<AuthGroupRuleDto>();
        BeanUtils.copyProperties(pageOfRole, pageResult, "items");
        pageResult.setItems(ruleDtos);
        return pageResult;
    }

    /**
     * 页面【同步用户】按钮，手动根据用户匹配规则更新各个角色组下用户
     * @param idList
     */
    @Transactional
    public void synUser(List<Long> idList) {
        List<AuthGroupRulePo> rulePoList = authGroupRuleDomainService.getGroupRuleByIds(idList);
        synRuleSubject(rulePoList);
    }

    /**
     *
     * @param rulePoList
     */
    public void synRuleSubject(List<AuthGroupRulePo> rulePoList) {

        //需要查询的列
        List<String> queryColumns = getQueryColumns();
        //empId用来查询数据
        if (!queryColumns.contains("empId")) {
            queryColumns.add("empId");
        }
        log.info("synRuleSubject queryColumns:{}", FastjsonUtil.toJson(queryColumns));

        for (AuthGroupRulePo authGroupRulePo : rulePoList) {
            if(StringUtil.isEmpty(authGroupRulePo.getExpression())){
                continue;
            }
            for (String roleId : authGroupRulePo.getRoleId().split(",")) {
                authGroupRuleSubjectDomainService.checkConfigRole(roleId, authGroupRulePo.getId());
            }
            //先将旧的删除(逻辑删除)
            authGroupRuleSubjectDomainService.deleteByRuleId(authGroupRulePo.getId(), authGroupRulePo.getRoleId());
            EmpSearchColumnsDto dto = new EmpSearchColumnsDto();
            int pageNo = 1, pageSize = 500;
            dto.setPageSize(pageSize);
            dto.setColumns(queryColumns);
            PageResult<Map<String, String>> pageResult = null;

            do {
                dto.setPageNo(pageNo);
                List<Long> empIdList = Lists.newArrayList();

                pageResult = empRepositoy.searchEmpColumnsPage(dto);
                List<Map<String, String>> workInfoVoList = pageResult.getItems();
                log.info("synRuleSubject workInfoVoList:{}", FastjsonUtil.toJson(workInfoVoList));

                if (CollectionUtils.isNotEmpty(workInfoVoList)) {
                    for (Map<String, String> empMap : workInfoVoList) {
                        ConditionTree expression = FastjsonUtil.convertObject(authGroupRulePo.getExpression(), ConditionTree.class);
                        if (expression != null && expression.match(empMap)) {
                            empIdList.add(Long.parseLong(empMap.get("empId")));
                        }
                    }
                }
                //empId转成userId
                if (CollectionUtils.isNotEmpty(empIdList)) {
                    log.info("synRuleSubject empIdList:{}", FastjsonUtil.toJson(empIdList));
                    List<UserBaseInfoDo> userBaseInfoDos = userBaseInfoDomainService.selectUserBaseInfoByEmpIds(empIdList);
                    List<Long> userIds = userBaseInfoDos.stream().map(UserBaseInfoDo::getUserId).collect(Collectors.toList());
                    authGroupRuleSubjectService.checkConfigRole(userIds);
                    if (CollectionUtils.isNotEmpty(userIds)) {
                        authGroupRuleSubjectDomainService.createSubject(authGroupRulePo.getId(), userIds, authGroupRulePo.getRoleId());
                    }
                }

            } while (pageNo++ * pageSize < pageResult.getTotal());
        }
    }

    private List<String> getQueryColumns(){
        List<ConditionDataVo> conditionDataList = SpringUtil.getBean(IConditionFeign.class)
                .getConditionDataByCode("role_group", false).getData();
        return conditionDataList.stream().map(data -> {
            String column = data.getCode();
            switch (data.getComponent()){
                case DICT_SELECTOR:
                    column += ".dict.value";
                    break;
            }
            return column;
        }).collect(Collectors.toList());
    }

    @SneakyThrows
    public void deleteSubject(String subjectIds, Long ruleId) {
        AuthGroupRulePo authGroupRulePo = authGroupRuleDomainService.getGroupRuleById(ruleId);
        if(null == authGroupRulePo){
            return;
        }

        List<Long> roleIdList = Strings.split(",").call(authGroupRulePo.getRoleId())
                .filter(e -> StringUtils.isNotBlank(e)).map(e -> Long.parseLong(e)).toList();
        List<Long> subjectIdList = Strings.split(",").call(subjectIds)
                .filter(e -> StringUtils.isNotBlank(e)).map(e -> Long.parseLong(e)).toList();
        roleIdList.forEach(roleId -> {
            authGroupRuleSubjectService.deleteSubjectOfRole(roleId, subjectIdList);
        });
    }
}
