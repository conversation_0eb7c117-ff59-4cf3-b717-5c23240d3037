package com.caidaocloud.auth.service.caidao.infrastructure.config.auth;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.auth.service.caidao.application.dto.UserDetailDto;
import com.caidaocloud.auth.service.caidao.domain.entity.OAuthClientDo;
import com.caidaocloud.auth.service.caidao.infrastructure.config.converter.AuthJwtAccessTokenConverter;
import com.caidaocloud.auth.service.caidao.infrastructure.config.filter.OAuthClientCredentialsTokenEndpointFilter;
import com.caidaocloud.auth.service.caidao.application.service.OAuthClientDetailService;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.code.AuthorizationCodeServices;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenEnhancerChain;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;
import org.springframework.security.web.AuthenticationEntryPoint;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 授权认证服务器配置
 *
 * <AUTHOR>
 * @date 2021-11-11
 */
@Configuration
@EnableAuthorizationServer
public class AuthorizationServerConfig extends AuthorizationServerConfigurerAdapter {
    /**
     * 统一配置认证请求前缀
     */
    @NacosValue(value = "${hrpaas.oauth.pathmapping.prefix:/api/auth/open}", autoRefreshed = true)
    private String oauthPathPrefix;

    @Resource
    private OAuthClientDetailService oAuthClientDetailService;

    @Resource
    private UserDetailsService userDetailsService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Resource
    private AuthorizationCodeServices authorizationCodeServices;

    @Resource
    private AuthJwtAccessTokenConverter authJwtAccessTokenConverter;

    @Resource
    private AuthenticationEntryPoint authenticationEntryPoint;

    /**
     * 配置安全约束相关配置
     *
     * @param security 定义令牌终结点上的安全约束
     * @throws Exception
     */
    @Override
    public void configure(AuthorizationServerSecurityConfigurer security) throws Exception {
        // 支持 client_id、client_secret 以 form 表单的形式登录
        // 参考：https://exmail.qq.com/qy_mng_logic/doc
        //security.allowFormAuthenticationForClients();

        OAuthClientCredentialsTokenEndpointFilter endpointFilter = new OAuthClientCredentialsTokenEndpointFilter(security);
        endpointFilter.afterPropertiesSet();
        endpointFilter.setAuthenticationEntryPoint(authenticationEntryPoint);
        security.addTokenEndpointAuthenticationFilter(endpointFilter);

        security.tokenKeyAccess("permitAll()").checkTokenAccess("isAuthenticated()");
    }

    /**
     * 配置客户端详细信息
     *
     * @param clients 定义客户端详细信息服务的配置程序，初始化客户端详细信息
     */
    @Override
    public void configure(ClientDetailsServiceConfigurer clients) throws Exception {
        // 客户端信息通过 oAuthClientDetailService 处理
        clients.withClientDetails(oAuthClientDetailService);
    }

    /**
     * 认证端点配置
     */
    @Override
    public void configure(AuthorizationServerEndpointsConfigurer endpoints) throws Exception {
        JwtAccessTokenConverter jwtAccessTokenConverter = authJwtAccessTokenConverter;
        TokenEnhancerChain tokenEnhancerChain = new TokenEnhancerChain();
        tokenEnhancerChain.setTokenEnhancers(Arrays.asList(tokenEnhancer(), jwtAccessTokenConverter));

        TokenStore ts = tokenStoreGenerator(jwtAccessTokenConverter);

        endpoints.tokenStore(ts)
                // 自定义 /api/hrpaas/open/auth/oauth/token 异常处理
                .exceptionTranslator(new OAuthWebResponseExceptionTranslator())
                .tokenEnhancer(tokenEnhancerChain)
                .authenticationManager(authenticationManager)
                // 禁止重复使用 refresh_token
                .reuseRefreshTokens(false)
                .userDetailsService(userDetailsService)
                .authorizationCodeServices(authorizationCodeServices)
                .pathMapping("/oauth/authorize", oauthPathPrefix + "/oauth/authorize")
                // 修改客户端认证的默认 path
                .pathMapping("/oauth/token", oauthPathPrefix + "/oauth/token")
                .pathMapping("/oauth/confirm_access", oauthPathPrefix + "/oauth/confirm_access")
                .pathMapping("/oauth/error", oauthPathPrefix + "/oauth/error")
                .pathMapping("/oauth/check_token", oauthPathPrefix + "/oauth/check_token")
                .pathMapping("/oauth/token_key", oauthPathPrefix + "/oauth/token_key")
                .setClientDetailsService(endpoints.getClientDetailsService());
    }

    /**
     * token 端点存储的信息
     *
     * @return
     */
    private TokenEnhancer tokenEnhancer() {
        return (accessToken, authentication) -> {
            final Map<String, Object> additionalInfo = new HashMap<>(10);
            additionalInfo.put("userid", 0);
            additionalInfo.put("empid", 0);
            if (authentication.getUserAuthentication() != null) {
                UserDetailDto user = (UserDetailDto) authentication.getUserAuthentication().getPrincipal();
                if (user != null) {
                    additionalInfo.put("tenantId", user.getTenantId());
                    additionalInfo.put("belongId", user.getBelongId());
                }
            } else {
                OAuthClientDo oauthClient = oAuthClientDetailService.loadClientById(authentication.getOAuth2Request().getClientId());
                additionalInfo.put("tenantId", oauthClient.getClientId());
                additionalInfo.put("belongId", oauthClient.getBelongId());
                additionalInfo.put("authUrl", StringUtils.isNotBlank(oauthClient.getAuthUrl()) ? oauthClient.getAuthUrl() : "");
            }
            ((DefaultOAuth2AccessToken) accessToken).setAdditionalInformation(additionalInfo);
            return accessToken;
        };
    }

    /**
     * 采用 jwt token
     *
     * @param jwtAccessTokenConverter jwt token 转换器
     */
    private TokenStore tokenStoreGenerator(JwtAccessTokenConverter jwtAccessTokenConverter) {
        return new JwtTokenStore(jwtAccessTokenConverter);
    }

}
