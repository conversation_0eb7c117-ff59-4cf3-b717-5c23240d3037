package com.caidaocloud.auth.service.caidao.domain.repository;

import com.caidaocloud.auth.service.caidao.domain.entity.AuthResourceDo;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.ResourceCategoryEnum;

import java.util.List;

public interface IAuthResourceRepository {

    List<AuthResourceDo> list(ResourceCategoryEnum resourceCategory, String parentCode);

    Long insert(AuthResourceDo authResourceDo);

    void insertBatch(List<AuthResourceDo> list);

    void updateByCode(AuthResourceDo authResourceDo);

    AuthResourceDo loadByCode(String code);

    List<AuthResourceDo> loadByCodes(List<String> codeList);

    AuthResourceDo loadByCode(String code, String lang, String... tenantIds);

    List<String> selectUrlOfFilterGateway();

    void remove(String code);

    List<AuthResourceDo> listByCode(String code);

    List<AuthResourceDo> listByUrl(String url);
}
