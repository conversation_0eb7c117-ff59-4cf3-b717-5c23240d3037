package com.caidaocloud.auth.service.caidao.facade.vo.workflow;

import com.caidaocloud.auth.core.dto.AuthRoleScopeTargetDetail;
import com.caidaocloud.auth.core.dto.AuthRoleScopeTargetRegisterDto;
import com.caidaocloud.auth.core.enums.AuthRoleScopeRestriction;
import com.caidaocloud.auth.core.enums.AuthRoleScopeTarget;
import com.caidaocloud.auth.core.enums.AuthRoleScopeTargetType;
import com.caidaocloud.auth.service.caidao.application.dto.AuthRoleScopeDto;
import com.caidaocloud.auth.service.caidao.application.factory.FormAuthScopeFactory;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleScopeDo;
import com.googlecode.totallylazy.Lists;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.caidaocloud.auth.core.enums.AuthRoleScopeRestriction.NO_AUTH;

@Data
public class AuthRoleScopeFilterDetail {
    private AuthRoleScopeTargetType targetType = AuthRoleScopeTargetType.STANDARD;
    private String property;
    private boolean inToOr;
    private AuthRoleScopeRestriction restriction;
    private String simpleValues;
    private String target;

    public static List<AuthRoleScopeFilterDetail> fromStandard(List<AuthRoleScopeDto> scopes, String identifier, String dymaicTarget) {
        List<AuthRoleScopeFilterDetail> results = Lists.list();
        for (AuthRoleScopeDto scope : scopes) {
            val targets = scope.getTargets().split(",");
            for (String target : targets) {
                if (StringUtils.isBlank(target)) {
                    continue;
                }

                if (StringUtils.isNotBlank(dymaicTarget) && target.equals(dymaicTarget)) {
                    val filterDetail = new AuthRoleScopeFilterDetail();
                    filterDetail.setRestriction(scope.getRestriction());
                    filterDetail.setSimpleValues(scope.getSimpleValues());
                    filterDetail.setTarget(target);
                    results.add(filterDetail);
                    continue;
                }
                if (AuthRoleScopeTargetType.findTargetType(target) == AuthRoleScopeTargetType.FORM) {
                    buildFormAuthScope(results, scope, target, identifier);
                    continue;
                }
                if (!AuthRoleScopeTarget.container(target)) {
                    continue;
                }
                val scopeTarget = AuthRoleScopeTarget.valueOf(target);
                val targetDetails = Lists.list(scopeTarget.getTargetDetails());
                targetDetails.add(
                        new AuthRoleScopeTargetDetail(NO_AUTH, targetDetails.get(0).getIdentifier(), targetDetails.get(0).getProperty())
                );
                for (AuthRoleScopeTargetDetail targetDetail : targetDetails) {
                    if (targetDetail.checkAuthScope(identifier,scope.getRestriction())){
                        val filterDetail = new AuthRoleScopeFilterDetail();
                        filterDetail.setRestriction(scope.getRestriction());
                        filterDetail.setInToOr(targetDetail.isInToOr());
                        filterDetail.setProperty(targetDetail.getProperty());
                        filterDetail.setSimpleValues(scope.getSimpleValues());
                        filterDetail.setTarget(target);
                        results.add(filterDetail);
                    }
                }
            }
        }
        val noAuth = results.stream().filter(it -> it.getRestriction().equals(NO_AUTH))
                .findFirst().orElse(null);
        if (noAuth != null) {
            results = results.stream().filter(it -> !it.getRestriction().equals(NO_AUTH)).collect(Collectors.toList());
            if (results.isEmpty()) {
                return Lists.list(noAuth);
            }
        }
        return results;
    }

    @NotNull
    private static void buildFormAuthScope(List<AuthRoleScopeFilterDetail> results, AuthRoleScopeDto scope, String target, String identifier) {
        String formId = StringUtils.substringAfterLast(target, "_");
        AuthRoleScopeTargetRegisterDto authScope = FormAuthScopeFactory.create("", formId);
        for (AuthRoleScopeTargetDetail detail : authScope.getDetails()) {
            if (detail.checkAuthScope(identifier, scope.getRestriction())) {
                val filterDetail = new AuthRoleScopeFilterDetail();
                filterDetail.setRestriction(scope.getRestriction());
                filterDetail.setInToOr(detail.isInToOr());
                filterDetail.setProperty(detail.getProperty());
                filterDetail.setSimpleValues(scope.getSimpleValues());
                filterDetail.setTarget(target);
                results.add(filterDetail);
            }
        }

    }

    private static boolean checkIdentifierMapping(String queryIdentifier, String scopeSetIdentifier) {
        if (StringUtils.equals(queryIdentifier, scopeSetIdentifier)) {
            return true;
        }
        return "entity.hr.EmpWorkInfo".equals(queryIdentifier)
                && scopeSetIdentifier.startsWith("entity.hr.EMP_SUB");
    }
}
