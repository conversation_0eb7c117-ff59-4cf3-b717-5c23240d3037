package com.caidaocloud.auth.service.caidao.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRuleIdAndSubjectIdDo;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthSubjectDo;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthGroupRuleSubjectRepository;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.mybatis.AuthGroupRuleSubjectDao;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthGroupRuleSubjectPo;
import com.caidaocloud.auth.service.caidao.infrastructure.repository.po.AuthSubjectInfoPo;
import com.caidaocloud.auth.service.caidao.infrastructure.util.QueryWrapperUtil;
import com.caidaocloud.auth.service.caidao.infrastructure.util.UserContextUtil;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/11
 **/
@Repository
@Slf4j
public class AuthGroupRuleSubjectRepositoryImpl implements IAuthGroupRuleSubjectRepository {
    @Autowired
    private AuthGroupRuleSubjectDao authGroupRuleSubjectDao;
    @Autowired
    private ISessionService sessionService;

    @Override
    public PageResult<AuthSubjectDo> getPage(List<Long> ruleIds, QueryPageBean queryPageBean) {
        if (CollectionUtils.isEmpty(ruleIds)) {
            throw new ServerException("ruleIds is empty");
        }
        var page = new Page<AuthSubjectInfoPo>(queryPageBean.getPageNo(), queryPageBean.getPageSize());
        page.setSearchCount(false);
        var keywords = queryPageBean.getKeywords();
        var queryWrapper = QueryWrapperUtil.buildQueryWrapper(queryPageBean.getFilterList());
        var count = authGroupRuleSubjectDao.selectCountOfUserInfo(ruleIds, keywords, queryWrapper);
        var pageResult = authGroupRuleSubjectDao.selectPageOfUserInfo(page, ruleIds, keywords, queryWrapper);
        pageResult.setTotal(count);
        return new PageResult(Sequences.sequence(pageResult.getRecords()).map(e -> FastjsonUtil.convertObject(e, AuthSubjectDo.class)).toList(),
                (int) pageResult.getCurrent(), (int) pageResult.getSize(), (int) pageResult.getTotal());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createSubject(Long ruleId, List<Long> subjectIdList) {
        if (ruleId == null || CollectionUtils.isEmpty(subjectIdList)) {
            throw new ServerException("ruleId or subjectIdList is empty");
        }
        var currentTimeMillis = System.currentTimeMillis();
        UserInfo userInfo = null;
        try {
            userInfo = sessionService.getUserInfo();
            if (userInfo == null) {
                userInfo = UserContextUtil.preCheckUser();
            }
        } catch (Exception e) {
            userInfo = UserContextUtil.preCheckUser();
            if (userInfo == null) {
                log.error("get userinfo occour, {}", e);
                throw new ServerException("not found user info");
            }
        }

        val empId = userInfo.getStaffId() == null ? "0" : userInfo.getStaffId().toString();
        var queryWrapper = new LambdaQueryWrapper<AuthGroupRuleSubjectPo>()
                .in(AuthGroupRuleSubjectPo::getSubjectId, subjectIdList)
                .eq(AuthGroupRuleSubjectPo::getRuleId, ruleId)
                .eq(AuthGroupRuleSubjectPo::getDeleted, 0);
        var authGroupRuleSubjectList = authGroupRuleSubjectDao.selectList(queryWrapper);
        var updateSequence = Sequences.sequence(authGroupRuleSubjectList)
                .filter(e -> subjectIdList.contains(e.getSubjectId()));
        if (!updateSequence.isEmpty()) {
            var queryWrapper2 = new LambdaQueryWrapper<AuthGroupRuleSubjectPo>()
                    .in(AuthGroupRuleSubjectPo::getId, updateSequence.map(AuthGroupRuleSubjectPo::getId).toList());
            var authGroupRuleSubjectPo = new AuthGroupRuleSubjectPo();
            authGroupRuleSubjectPo.setUpdateTime(currentTimeMillis);
            authGroupRuleSubjectPo.setUpdateBy(empId);
            authGroupRuleSubjectDao.update(authGroupRuleSubjectPo, queryWrapper2);
        }
        var subjectIdSet = Sequences.sequence(authGroupRuleSubjectList).map(AuthGroupRuleSubjectPo::getSubjectId).toSet();
        ArrayList<AuthGroupRuleSubjectPo> insertList = Lists.newArrayList();
        for (Long subjectId : subjectIdList) {
            if (!subjectIdSet.contains(subjectId)) {
                var authGroupRuleSubjectPo = new AuthGroupRuleSubjectPo();
                authGroupRuleSubjectPo.setRuleId(ruleId);
                authGroupRuleSubjectPo.setSubjectId(subjectId);
                authGroupRuleSubjectPo.setTenantId(userInfo.getTenantId());
                authGroupRuleSubjectPo.setCreateTime(currentTimeMillis);
                authGroupRuleSubjectPo.setCreateBy(empId);
                authGroupRuleSubjectPo.setDeleted(false);
                insertList.add(authGroupRuleSubjectPo);
            }
        }
        if (!CollectionUtils.isEmpty(insertList)) {
            authGroupRuleSubjectDao.insertBatchSomeColumn(insertList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteSubject(Long ruleId, List<Long> subjectIdList) {
        if (ruleId == null || CollectionUtils.isEmpty(subjectIdList)) {
            throw new ServerException("ruleId or subjectIdList is empty");
        }
        var queryWrapper = new LambdaQueryWrapper<AuthGroupRuleSubjectPo>()
                .in(AuthGroupRuleSubjectPo::getSubjectId, subjectIdList)
                .eq(AuthGroupRuleSubjectPo::getRuleId, ruleId);
        authGroupRuleSubjectDao.delete(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByRuleId(Long ruleId) {
        if (ruleId == null) {
            throw new ServerException("ruleId is empty");
        }

        var queryWrapper = new LambdaQueryWrapper<AuthGroupRuleSubjectPo>()
                .eq(AuthGroupRuleSubjectPo::getRuleId, ruleId);


        authGroupRuleSubjectDao.delete(queryWrapper);

    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteSubjects(String subjectIds) {
        val queryWrapper = new LambdaQueryWrapper<AuthGroupRuleSubjectPo>()
                .in(AuthGroupRuleSubjectPo::getSubjectId, Arrays.stream(subjectIds.split(",")).map(Long::valueOf)
                        .collect(Collectors.toList()));
        authGroupRuleSubjectDao.delete(queryWrapper);
    }

    @Override
    public List<Long> getRuleIdListBySubject(Long subjectId) {
        val queryWrapper = new LambdaQueryWrapper<AuthGroupRuleSubjectPo>()
                .eq(AuthGroupRuleSubjectPo::getSubjectId, subjectId).eq(AuthGroupRuleSubjectPo::getDeleted, false);
        return authGroupRuleSubjectDao.selectList(queryWrapper).stream()
                .map(it -> it.getRuleId()).collect(Collectors.toList());
    }

    @Override
    public List<AuthRuleIdAndSubjectIdDo> getRuleIdAndSubjectIdBySubjectIds(List<Long> subjectIds) {
        if (CollectionUtils.isEmpty(subjectIds)) {
            return Lists.newArrayList();
        }
        var queryWrapper = new LambdaQueryWrapper<AuthGroupRuleSubjectPo>()
                .in(AuthGroupRuleSubjectPo::getSubjectId, subjectIds).eq(AuthGroupRuleSubjectPo::getDeleted, false)
                .select(AuthGroupRuleSubjectPo::getSubjectId, AuthGroupRuleSubjectPo::getRuleId);
        List<AuthGroupRuleSubjectPo> authGroupRuleSubjectPos = authGroupRuleSubjectDao.selectList(queryWrapper);
        return Sequences.sequence(authGroupRuleSubjectPos)
                .filter(e -> e.getSubjectId() != null && e.getRuleId() != null)
                .map(e -> {
                    var entity = new AuthRuleIdAndSubjectIdDo();
                    entity.setSubjectId(e.getSubjectId());
                    entity.setRuleId(e.getRuleId());
                    return entity;
                }).toList();
    }

}
