package com.caidaocloud.auth.core.scope.mybatis;

import java.util.List;

import com.caidaocloud.auth.core.dto.AuthRoleScopeFilterDetailDto;
import com.caidaocloud.auth.core.feign.IAuthClient;
import com.caidaocloud.auth.core.scope.AuthScopeContext;
import com.caidaocloud.auth.core.util.PluginUtil;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

/**
 * 权限范围拦截器
 * <AUTHOR> Zhou
 * @date 2024/1/2
 */
@Intercepts({
		@Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
})
@Slf4j
public class AuthScopeInterceptor implements Interceptor {



	private static final int MAPPED_STATEMENT_INDEX = 0;
	private static final int PARAMETER_INDEX = 1;


	@Override
	public Object intercept(Invocation invocation) throws Throwable {
		Object[] args = invocation.getArgs();
		MappedStatement ms = (MappedStatement) args[MAPPED_STATEMENT_INDEX];
		Object parameter = args[PARAMETER_INDEX];
		if (!checkAuthScope(ms)) {
			return invocation.proceed();
		}

		BoundSql boundSql = ms.getBoundSql(parameter);
		String sql = ms.getBoundSql(parameter).getSql().trim();
		String authSql = appendAuthScope(ms, sql);
		BoundSql newBoundSql = new BoundSql(ms.getConfiguration(),
				authSql, boundSql.getParameterMappings(),
				boundSql.getParameterObject());
		MappedStatement replacedMappedStatement = PluginUtil.replaceSql2MappedStatement(ms, newBoundSql);
		args[MAPPED_STATEMENT_INDEX] = replacedMappedStatement;
		return invocation.proceed();
	}

	/**
	 * 校验是否需要数据权限过滤
	 * @param ms
	 * @return
	 */
	private boolean checkAuthScope(MappedStatement ms) {
		String code = AuthScopeContext.getAuthScopeCode(ms.getId());
		return code != null && AuthScopeFilterUtil.checkIdentifier(code);
	}

	private String appendAuthScope(MappedStatement ms, String sql) {
		if (log.isDebugEnabled()) {
			log.debug("original SQL:{}", sql);
		}
		String scopeCode = AuthScopeContext.getAuthScopeCode(ms.getId());
		List<AuthRoleScopeFilterDetailDto> scopeDetail = getScopeDetail(scopeCode);
		StringBuilder sb = new StringBuilder("select * from (").append(sql).append(") as tmp where 1=1");
		for (int i = 0; i < scopeDetail.size(); i++) {
			if (i==0) {
				sb.append(" and (");
			}
			AuthRoleScopeFilterDetailDto scope = scopeDetail.get(i);
			List<String> scopeValue = getScopeValue(scope);
			// TODO: 2024/1/4 scopeValue长度过大时，改为临时表
			appendValue(ms, sb, scope, scopeValue);
			if (i < scopeDetail.size() - 1) {
				sb.append(" or ");
			}
			else {
				sb.append(")");
			}
		}
		if (log.isDebugEnabled()) {
			log.debug("auth scope SQL:{}", sb);
		}
		return sb.toString();
	}

	private void appendValue(MappedStatement ms, StringBuilder sb, AuthRoleScopeFilterDetailDto scope, List<String> scopeValue) {
		if (scopeValue.isEmpty()) {
			sb.append("(1=0)");
			return;
		}
		sb.append(AuthScopeContext.getAuthScopeProperty(ms.getId(), scope.getProperty())).append(" in ( ");
		scopeValue.forEach(s -> sb.append("'").append(s).append("',"));
		sb.deleteCharAt(sb.length() - 1);
		sb.append(") ");
	}

	private List<String> getScopeValue(AuthRoleScopeFilterDetailDto scope) {
		try {
			return SpringUtil.getBean(AuthScopeContext.class).handleValue(scope);
		}
		catch (Exception e) {
			throw new ServerException("权限解析失败," + e.getMessage(),e);
		}
	}

	private List<AuthRoleScopeFilterDetailDto> getScopeDetail(String scopeCode) {
		Result<List<AuthRoleScopeFilterDetailDto>> result = SpringUtil.getBean(IAuthClient.class)
				.getRoleScope(scopeCode, String.valueOf(SecurityUserUtil.getSecurityUserInfo().getUserId()));
		if (!result.isSuccess()) {
			throw new ServerException("Failed to get role scope from auth service,message:" + result.getMsg());
		}
		return result.getData();
	}

}
