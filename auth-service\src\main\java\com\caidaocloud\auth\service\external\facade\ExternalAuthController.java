package com.caidaocloud.auth.service.external.facade;

import com.caidaocloud.auth.service.external.application.dto.AppSecurityDto;
import com.caidaocloud.auth.service.external.application.dto.SsoDto;
import com.caidaocloud.auth.service.external.application.service.ExternalAuthApplicationService;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@RestController
@Api(tags = "外部系统认证")
@RequestMapping("/api/auth/v1/external")
public class ExternalAuthController {
    @Autowired
    private ExternalAuthApplicationService externalAuthApplicationService;

    @ApiOperation("单点登录")
    @PostMapping("sso")
    public Result<Map<String, Object>> sso(@RequestBody SsoDto ssoDto, HttpServletRequest request, HttpServletResponse response) {
        return Result.ok(externalAuthApplicationService.sso(ssoDto == null ? null : ssoDto.getType(), request, response));
    }

    @ApiOperation("外部系统密钥保存")
    @PostMapping("app/security")
    public Result saveAppSecurity(@RequestBody AppSecurityDto appSecurityDto) {
        externalAuthApplicationService.saveOrUpdateSecurity(appSecurityDto);
        return Result.ok();
    }
}