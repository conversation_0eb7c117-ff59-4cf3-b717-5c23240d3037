package com.caidaocloud.auth.service;

import com.caidaocloud.auth.service.caidao.application.service.AuthResourceService;
import com.caidaocloud.auth.service.caidao.facade.vo.AuthResourceUrlVo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * created by: FoAng
 * create time: 29/5/2024 2:15 下午
 */
@SpringBootTest(classes = AuthApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class AuthResourceTestService {

    @Autowired
    private AuthResourceService authResourceService;

    @Before
    public void setUp() throws Exception {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("11");
        userInfo.setUserId(0L);
        userInfo.setEmpId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }

    @Test
    public void detail() {
        AuthResourceUrlVo detailVo = authResourceService.detailByUrl("/api/hr/company/v1/update");
        System.out.println(FastjsonUtil.toJson(detailVo));
    }
}
