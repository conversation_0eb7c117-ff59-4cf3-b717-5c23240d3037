package com.caidaocloud.auth.service.external.application.authenticate;

import com.caidaocloud.auth.service.external.domain.entity.AppSecurityEntity;
import com.caidaocloud.auth.service.external.domain.service.AppSecurityDomainService;
import com.caidaocloud.auth.service.external.infrastructure.enums.ExternalAuthEnum;
import com.caidaocloud.util.SpringUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IExternalAuthService {
    /**
     * 单点登录
     *
     * @return
     */
    Map<String, Object> sso(HttpServletRequest request, HttpServletResponse response);

    default AppSecurityEntity getgetAppSecurity(String tenantId, ExternalAuthEnum type) {
        return SpringUtil.getBean(AppSecurityDomainService.class).getAppSecurity(tenantId, type);
    }
}