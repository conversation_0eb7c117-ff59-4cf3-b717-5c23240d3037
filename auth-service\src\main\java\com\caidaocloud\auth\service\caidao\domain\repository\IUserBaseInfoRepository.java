package com.caidaocloud.auth.service.caidao.domain.repository;

import com.caidaocloud.auth.service.caidao.domain.entity.UserAccountBaseInfo;
import com.caidaocloud.auth.service.caidao.domain.entity.UserBaseInfoDo;

import java.util.List;

public interface IUserBaseInfoRepository {
    List<UserBaseInfoDo> selectUserBaseInfoByAccounts(List<String> accounts);


    List<UserBaseInfoDo> selectUserBaseInfoByEmpIds(List<Long> empIds);

    List<UserAccountBaseInfo> selectByAccounts(List<String> accounts);
}
