package com.caidaocloud.auth.service.caidao.domain.repository;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.caidaocloud.auth.service.AuthApplication;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleScopeDo;
import com.caidaocloud.auth.service.caidao.infrastructure.enums.AuthRoleScopeComparator;
import com.caidaocloud.auth.core.enums.AuthRoleScopeRestriction;
import com.caidaocloud.auth.core.enums.AuthRoleScopeTargetType;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.service.SessionServiceImpl;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.RequestHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/5/6
 */
@SpringBootTest(classes = AuthApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
@Slf4j
public class IAuthRoleScopeRepositoryTests {

	@Autowired
	private IAuthRoleScopeRepository authRoleScopeRepository;

	private final Long testRoleId = 10L;

	private final Long testCopyRoleId = 11L;


	@Before
	public void setUp() throws Exception {
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("8");
		userInfo.setUserId(0L);
		userInfo.setEmpId(0L);
		SecurityUserUtil.setSecurityUserInfo(userInfo);

		RequestHelper.getRequest()
				.setAttribute("_caidaocloud_userinfo_request_threadlocal_", ObjectConverter.convert(userInfo, UserInfo.class));

// Get the instance of SessionServiceImpl which is already created by Spring bean
		SessionServiceImpl service = (SessionServiceImpl) SpringUtil.getBean(ISessionService.class);
		try {
			// Get the private field "threadLocalCache" from the SessionServiceImpl class
			Field field = SessionServiceImpl.class.getDeclaredField("threadLocalCache");
			// Set the accessibility of the field to true, as it is private
			field.setAccessible(true);
			// Set the value of the "threadLocalCache" field to true
			field.set(service, true);
		}
		catch (NoSuchFieldException | IllegalAccessException e) {
			e.printStackTrace();
		}
	}

	// Create a new test method
// Rewrite according to query: "After unit testing, delete test data"
	@Test
	public void testFlushAll() {
		// Create a new list of AuthRoleScopeDo objects
		List<AuthRoleScopeDo> authRoleScopeDoList = new ArrayList<>();
		// Create a new AuthRoleScopeDo object and set all its properties
		AuthRoleScopeDo authRoleScopeDo = createAuthRoleScopeDo(testRoleId, "target1,target2");
		// Add the AuthRoleScopeDo object to the list
		authRoleScopeDoList.add(authRoleScopeDo);
		// Create another AuthRoleScopeDo object and set all its properties with different target parameter
		AuthRoleScopeDo authRoleScopeDo2 = createAuthRoleScopeDo(testCopyRoleId, "target3,target4");
		// Add the AuthRoleScopeDo object to the list
		authRoleScopeDoList.add(authRoleScopeDo2);
		// Create another AuthRoleScopeDo object and set all its properties with different target parameter
		AuthRoleScopeDo authRoleScopeDo3 = createAuthRoleScopeDo(testCopyRoleId, "target5,target6");
		// Add the AuthRoleScopeDo object to the list
		authRoleScopeDoList.add(authRoleScopeDo3);
		// Call the flushAll method of authRoleScopeRepository and pass in the list
		authRoleScopeRepository.flushAll(testRoleId, authRoleScopeDoList);

		// Verify saved AuthRoleScopeDo objects
		List<AuthRoleScopeDo> savedAuthRoleScopeDoList = authRoleScopeRepository.load(testRoleId);
		assertEquals(1, savedAuthRoleScopeDoList.size());
		assertEquals(authRoleScopeDo, savedAuthRoleScopeDoList.get(0));
		log.info("saved data={}", savedAuthRoleScopeDoList.get(0));
	}

	@After
	public void cleanUp() {
		// Delete the records associated with the given role ids using deleteByRoleIds api
		authRoleScopeRepository.deleteByRoleIds(Arrays.asList(testRoleId, testCopyRoleId));
	}

	private AuthRoleScopeDo createAuthRoleScopeDo(Long roleId, String targets) {
		AuthRoleScopeDo scope = new AuthRoleScopeDo();
		scope.setRoleId(roleId);
		scope.setTargetType(AuthRoleScopeTargetType.STANDARD);
		scope.setTargets(targets);
		scope.setFilterProperty("filter");
		scope.setComparator(AuthRoleScopeComparator.EQ);
		scope.setRestriction(AuthRoleScopeRestriction.MY_ORG);
		scope.setValues("value1,value2");
		scope.setSimpleValues("simpleValue1,simpleValue2");
		return scope;
	}

}