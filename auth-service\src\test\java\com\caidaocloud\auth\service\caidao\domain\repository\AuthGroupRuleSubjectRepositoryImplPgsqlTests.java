package com.caidaocloud.auth.service.caidao.domain.repository;

import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.auth.service.AuthApplication;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRuleIdAndSubjectIdDo;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthSubjectDo;
import com.caidaocloud.auth.service.caidao.infrastructure.util.SnowUtil;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Sequences;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/5/4
 */
@SpringBootTest(classes = AuthApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
@Slf4j
public class AuthGroupRuleSubjectRepositoryImplPgsqlTests {

	@Autowired
	private IAuthGroupRuleSubjectRepository authGroupRuleSubjectRepository;
	@Autowired
	private IUserBaseInfoRepository userBaseInfoRepository;

	private  List<Long> ruleIds = new ArrayList<>();
	private  List<Long> subjectIds = new ArrayList<>();
	private  List<Long> subjectIds2 = new ArrayList<>();

	@SneakyThrows
	@Before
	public void bf() {
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("8");
		userInfo.setUserId(0L);
		userInfo.setEmpId(0L);
		SecurityUserUtil.setSecurityUserInfo(userInfo);

		ruleIds.add(Long.valueOf(SnowUtil.nextId()));
		ruleIds.add(Long.valueOf(SnowUtil.nextId()));
		ruleIds.sort(Long::compareTo);

		List<String> ids = FastjsonUtil.toList(IOUtils.toString(new ClassPathResource("src/test/resources/userBaseInfoIds.json").getInputStream(), "UTF-8"), String.class);
		for (int i = 0; i < ids.size(); i++) {
			String id = ids.get(i);
			subjectIds.add(Long.valueOf(id));
			if (i % 2 == 0) {
				subjectIds2.add(Long.valueOf(id));
			}
		}

	}

	@Test
	public void testSave() {
		log.info("start create subject");
		authGroupRuleSubjectRepository.createSubject(ruleIds.get(0), subjectIds);
		authGroupRuleSubjectRepository.createSubject(ruleIds.get(1), subjectIds2);

		QueryPageBean page = new QueryPageBean();
		page.setPageSize(100);
		PageResult<AuthSubjectDo> pageResult = authGroupRuleSubjectRepository.getPage(ruleIds, page);
		assertEquals(subjectIds.size() + subjectIds2.size(), pageResult.getTotal());
		log.info("create subject succeed,data={}", pageResult);


		log.info("start delete subject");
		List<Long> removeSubjects = Sequences.sequence(subjectIds).filter(subjectId -> !subjectIds2.contains(subjectId))
				.toList();
		authGroupRuleSubjectRepository.deleteSubject(ruleIds.get(0), removeSubjects);

		pageResult = authGroupRuleSubjectRepository.getPage(ruleIds, page);
		assertEquals(subjectIds.size() + subjectIds2.size() - removeSubjects.size(), pageResult.getTotal());
		log.info("delete subject succeed");

		log.info("start getRuleIdListBySubject");
		List<Long> list = authGroupRuleSubjectRepository.getRuleIdListBySubject(subjectIds2.get(0));
		list.sort(Long::compareTo);
		assertArrayEquals(ruleIds.toArray(), list.toArray());
		log.info("getRuleIdListBySubject succeed");

		log.info("start getRuleIdAndSubjectIdBySubjectIds");
		List<AuthRuleIdAndSubjectIdDo> id = authGroupRuleSubjectRepository.getRuleIdAndSubjectIdBySubjectIds(subjectIds2);
		log.info("getRuleIdAndSubjectIdBySubjectIds succeed,data={}", id);
	}

	@After
	public void clear(){
		authGroupRuleSubjectRepository.deleteSubjects(StringUtils.join(subjectIds, ","));
	}
}