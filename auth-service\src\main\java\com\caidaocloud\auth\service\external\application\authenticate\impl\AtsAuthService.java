package com.caidaocloud.auth.service.external.application.authenticate.impl;

import com.caidaocloud.auth.service.caidao.application.dto.masterdata.EmpInfoVo;
import com.caidaocloud.auth.service.caidao.application.dto.masterdata.EmpSearchDto;
import com.caidaocloud.auth.service.common.application.feign.MasterDataFeignV2;
import com.caidaocloud.auth.service.external.application.authenticate.IExternalAuthService;
import com.caidaocloud.auth.service.external.infrastructure.enums.ExternalAuthEnum;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/8
 **/
@Slf4j
@Service
public class AtsAuthService implements IExternalAuthService {
    @Autowired
    private RestTemplate restTemplate;
    @Value("${caidaocloud.ats.domain:}")
    private String atsDomain;
    @Value("${caidaocloud.ats.apiDomain:}")
    private String atsApiDomain;
    @Value("${caidaocloud.ats.redirect:/user/login}")
    private String redirectUri;
    @Value("${caidaocloud.ats.tokenUri:/prod-api/openapi/accessTokenOnApp}")
    private String toeknUri;
    @Autowired
    private AtsAuthService atsAuthService;
    @Autowired
    private MasterDataFeignV2 masterDataFeignV2;

    @Override
    public Map<String, Object> sso(HttpServletRequest request, HttpServletResponse response) {
        var userInfo = SecurityUserUtil.getSecurityUserInfo();
        PreCheck.preCheckArgument(userInfo == null || StringUtils.isBlank(userInfo.getTenantId()) || userInfo.getUserId() == null, ErrorMessage.fromCode("USER_NO_LOG_IN"));
        String token = null;
        try {
            token = atsAuthService.getToken(userInfo);
        } catch (Exception e) {
            log.error("occur error when get ast token", e);
            if (e instanceof ServerException) {
                throw e;
            }
            throw ServerException.globalException(ErrorMessage.fromCode("UNKNOWN_ERROR"));
        }
        var domain = this.atsDomain;
        PreCheck.preCheckArgument(StringUtils.isBlank(domain), ErrorMessage.fromCode("ATS_DOMIAN_CONFIG"));
        if (StringUtils.isNotBlank(domain) && domain.lastIndexOf("/") == domain.length() - 1) {
            domain = StringUtils.substring(atsApiDomain, 0, atsApiDomain.length() - 1);
        }
        HashMap<String, Object> result = Maps.newHashMap();
        result.put("redirectUrl", String.format("%s%s?tokenKey=%s", domain, redirectUri, token));
        return result;
    }

    private String getToken(SecurityUserInfo userInfo) {
        var appSecurity = getgetAppSecurity(userInfo.getTenantId(), ExternalAuthEnum.ATS);
        PreCheck.preCheckArgument(appSecurity == null ||
                StringUtils.isBlank(appSecurity.getAppSecret()) ||
                StringUtils.isBlank(appSecurity.getAppKey()), ErrorMessage.fromCode("APP_SECURITY_NOT"));
        var domain = this.atsApiDomain;
        PreCheck.preCheckArgument(StringUtils.isBlank(domain), ErrorMessage.fromCode("ATS_DOMIAN_CONFIG"));
        if (StringUtils.isNotBlank(domain) && domain.lastIndexOf("/") == domain.length() - 1) {
            domain = StringUtils.substring(atsApiDomain, 0, atsApiDomain.length() - 1);
        }
        PreCheck.preCheckArgument(userInfo.getEmpId() == null, ErrorMessage.fromCode("NOT_EMP"));
        var dto = new EmpSearchDto();
        dto.setEmpIds(Arrays.asList(userInfo.getEmpId().toString()));
        dto.setDatetime(System.currentTimeMillis());
        Result<List<EmpInfoVo>> result = masterDataFeignV2.getEmpInfoByEmpIds(dto);
        PreCheck.preCheckArgument(result == null || CollectionUtils.isEmpty(result.getData()), ErrorMessage.fromCode("NOT_EMP"));
        PreCheck.preCheckArgument(StringUtils.isBlank(result.getData().get(0).getCompanyEmail()),
                ErrorMessage.fromCode("NO_EMAIL"));
        var url = new StringBuilder();
        url.append(domain);
        if (!toeknUri.startsWith("/")) {
            url.append("/");
        }
        url.append(toeknUri);
        var headers = new HttpHeaders();
        headers.set("Accept", "application/json");
        HashMap<String, String> body = Maps.newHashMap();
        body.put("account", result.getData().get(0).getCompanyEmail());
        body.put("appKey", appSecurity.getAppKey());
        body.put("appSecret", appSecurity.getAppSecret());
        HttpEntity httpEntity = new HttpEntity(body, headers);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url.toString(), HttpMethod.POST, httpEntity, String.class);
        if (responseEntity.getStatusCode() == HttpStatus.OK) {
            log.info("request atr url={} body={} data={}", url.toString(), body, responseEntity.getBody());
            Map<String, Object> map = FastjsonUtil.toObject(responseEntity.getBody(), Map.class);
            if (map.containsKey("code") && Integer.valueOf(map.get("code").toString()).intValue() == 200) {
                String data = String.valueOf(map.get("data"));
                Map<String, Object> dataResult = FastjsonUtil.toObject(data, Map.class);
                if (dataResult.containsKey("tokenKey")) {
                    String tokenKey = dataResult.get("tokenKey").toString();
                    return tokenKey;
                }
                log.info("result data = {}", data);
                throw ServerException.globalException(ErrorMessage.fromCode("UNKNOWN_ERROR"));
            }
        }
        throw ServerException.globalException(ErrorMessage.fromCode("NO_ATS_AUTH"));
    }
}