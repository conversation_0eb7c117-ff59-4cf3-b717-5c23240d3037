package com.caidaocloud.auth.service.caidao.application.util;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.caidaocloud.excption.ServerException;
import org.apache.poi.ss.usermodel.Workbook;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ExcelUtils {
    public static void downloadDataMapExcel(List<ExcelExportEntity> colList, List<Map<String, Object>> list, String xlsFileName, HttpServletResponse response) {
        try {
            final Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), colList, list);
            downLoadExcel(xlsFileName, response, workbook);
        } catch (IOException e) {
            throw new ServerException("download excel err.");
        }
    }

    /**
     * excel下载
     * @param fileName 下载时的文件名称
     * @param response
     * @param workbook excel数据
     */
    public static void downLoadExcel(String fileName, HttpServletResponse response, Workbook workbook) throws IOException {
        try {
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xls", "UTF-8"));
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    public static void writeDataFile(File file, List<ExcelExportEntity> colList, List<Map<String, Object>> list, String xlsFileName) throws IOException {
        final Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), colList, list);
        workbook.write(new FileOutputStream(file));
    }
}
