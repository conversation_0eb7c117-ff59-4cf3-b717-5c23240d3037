package com.caidaocloud.auth.service.caidao.domain.service;

import com.caidaocloud.auth.service.caidao.application.dto.AuthDefaultRuleOfRoleDto;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleGroupDo;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthRoleGroupRepository;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.ServerException;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/10
 **/
@Slf4j
@Service
public class AuthRoleGroupDomainService {

    @Resource
    private IAuthRoleGroupRepository oAuthRoleGroupRepository;

    public void saveOrUpdateRoleGroup(AuthRoleGroupDo authRoleGroupDo) {
        oAuthRoleGroupRepository.saveOrUpdateRoleGroup(authRoleGroupDo);
    }

    public List<Long> getRoleDefaultGroupRuleId(List<Long> roleIds) {
        return oAuthRoleGroupRepository.getRoleDefaultGroupRuleId(roleIds);
    }

    public List<Long> getRoleIdListByRuleIdList(List<Long> ruleIdList) {
        if (CollectionUtils.isEmpty(ruleIdList)) {
            throw new ServerException("this user has no authorization");
        }
        return oAuthRoleGroupRepository.getRoleIdListByRuleIdList(ruleIdList);
    }

    public List<AuthDefaultRuleOfRoleDto> getAuthRoleGroupByRoleId(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            throw ServerException.globalException(ErrorMessage.fromCode("AUTH_ROLE_NOT_EXISTED"));
        }
        return Sequences.sequence(oAuthRoleGroupRepository.getAuthDefaulfRuleIdByRoleId(roleIdList))
                .map(e -> {
                    var entity = new AuthDefaultRuleOfRoleDto();
                    entity.setRuleId(e.getRuleId());
                    List<Long> list = e.getRoleIdList();
                    if (!CollectionUtils.isEmpty(list)) {
                        entity.setRoleId(list.get(0));
                    }
                    return entity;
                }).filter(e -> e.getRoleId() != null).toList();
    }

    public void removeByRoleIds(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            log.info("roleIdList is emtpy");
            return;
        }
        oAuthRoleGroupRepository.removeByRoleIds(roleIdList);
    }
}
