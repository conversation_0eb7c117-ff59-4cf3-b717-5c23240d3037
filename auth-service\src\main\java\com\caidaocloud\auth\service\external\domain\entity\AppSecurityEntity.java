package com.caidaocloud.auth.service.external.domain.entity;

import com.caidaocloud.auth.service.external.domain.repository.IAppSecurityRepository;
import com.caidaocloud.auth.service.external.infrastructure.enums.ExternalAuthEnum;
import com.caidaocloud.auth.service.external.infrastructure.repository.po.AppSecurityPo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Map;
import java.util.Optional;

@Data
@Accessors(chain = true)
public class AppSecurityEntity implements Serializable {
    private String id;
    private String tenantId;
    private String appKey;
    private String appSecret;
    private ExternalAuthEnum type;
    private Map<String, Object> ext;

    public AppSecurityPo convertToPo() {
        return FastjsonUtil.convertObject(this, AppSecurityPo.class);
    }

    public void saveOrUpdate() {
        if (StringUtils.isBlank(this.id)) {
            SpringUtil.getBean(IAppSecurityRepository.class).save(this);
        } else {
            SpringUtil.getBean(IAppSecurityRepository.class).update(this);
        }
    }

    public static Optional<AppSecurityEntity> getEntity(ExternalAuthEnum type) {
        return SpringUtil.getBean(IAppSecurityRepository.class).findAppSecurity(type);
    }
}