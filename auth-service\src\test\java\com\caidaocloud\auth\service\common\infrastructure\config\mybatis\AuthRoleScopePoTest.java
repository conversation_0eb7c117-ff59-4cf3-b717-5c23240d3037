package com.caidaocloud.auth.service.common.infrastructure.config.mybatis;

import com.caidaocloud.auth.service.AuthApplication;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleScopeDo;
import com.caidaocloud.auth.service.caidao.domain.repository.IAuthRoleScopeRepository;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest(classes = AuthApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
@Slf4j
public class AuthRoleScopePoTest {
    @Resource
    private IAuthRoleScopeRepository authRoleScopeRepository;
    @Test
    public void testQuery(){
        List<AuthRoleScopeDo> load = authRoleScopeRepository.load(1L);
        System.out.println("----------");
    }

    @Before
    public void beforeInit(){
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("8");
        userInfo.setUserId(0L);
        userInfo.setEmpId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }
}
