CREATE TABLE IF NOT EXISTS auth_role_permission_$tenant_id
(
    id                SERIAL NOT NULL,
    tenant_id         VARCHAR(50) DEFAULT '',
    role_id           BIGINT NOT NULL,
    resource_code     VARCHAR(50) DEFAULT '',
    create_time       BIGINT NOT NULL,
    create_by         VARCHAR(50) DEFAULT '',
    update_time       BIGINT      DEFAULT NULL,
    update_by         VARCHAR(50) DEFAULT '',
    parent_code       VA<PERSON>HAR(50) DEFAULT NULL,
    deleted           SMALLINT    DEFAULT 0,
    data_scope_detail text        default null,
    category          varchar(20) default NULL,
    PRIMARY KEY (id)
);

COMMENT
ON TABLE auth_role_permission_$tenant_id IS '权限映射';
COMMENT
ON COLUMN auth_role_permission_$tenant_id.tenant_id IS '租户id';
COMMENT
ON COLUMN auth_role_permission_$tenant_id.role_id IS '角色id';
COMMENT
ON COLUMN auth_role_permission_$tenant_id.resource_code IS '资源编码';
COMMENT
ON COLUMN auth_role_permission_$tenant_id.create_time IS '创建时间';
COMMENT
ON COLUMN auth_role_permission_$tenant_id.create_by IS '创建者';
COMMENT
ON COLUMN auth_role_permission_$tenant_id.update_time IS '更新时间';
COMMENT
ON COLUMN auth_role_permission_$tenant_id.update_by IS '更新者';
COMMENT
ON COLUMN auth_role_permission_$tenant_id.parent_code IS '父级编码';
COMMENT
ON COLUMN auth_role_permission_$tenant_id.deleted IS '是否删除';
COMMENT
ON COLUMN auth_role_permission_$tenant_id.data_scope_detail IS '数据权限详情';
COMMENT
ON COLUMN auth_role_permission_$tenant_id.category IS '资源分类';