package com.caidaocloud.auth.core.feign;

import java.util.List;

import com.caidaocloud.auth.core.dto.AuthRoleScopeFilterDetailDto;
import com.caidaocloud.auth.core.dto.AuthRoleScopeTargetRegisterDto;
import com.caidaocloud.web.Result;

import org.springframework.stereotype.Component;

@Component
public class AuthFeignFallback implements IAuthClient {
    @Override
    public Result<List<AuthRoleScopeFilterDetailDto>> getRoleScope(String identifier, String subjectId) {
        return Result.fail();
    }

    @Override
    public Result register(AuthRoleScopeTargetRegisterDto dto) {
        return Result.ok();
    }
}
