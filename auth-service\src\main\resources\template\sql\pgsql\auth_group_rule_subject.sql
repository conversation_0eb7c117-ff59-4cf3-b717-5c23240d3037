CREATE TABLE IF NOT EXISTS auth_group_rule_subject
(
    id          SERIAL      NOT NULL,
    rule_id     BIGINT      NOT NULL,
    subject_id  BIGINT      NOT NULL,
    tenant_id   VARCHAR(50) NOT NULL,
    create_time BIGINT      NOT NULL,
    create_by   VA<PERSON>HAR(50) DEFAULT '',
    update_time BIGINT      DEFAULT NULL,
    update_by   VARCHAR(50) DEFAULT '',
    deleted     SMALLINT    DEFAULT 0,
    PRIMARY KEY (id)
);

COMMENT
ON TABLE auth_group_rule_subject IS '分组与员工对象关系';
COMMENT
ON COLUMN auth_group_rule_subject.id IS '主键唯一标识';
COMMENT
ON COLUMN auth_group_rule_subject.rule_id IS '分组规则id';
COMMENT
ON COLUMN auth_group_rule_subject.subject_id IS '员工id';
COMMENT
ON COLUMN auth_group_rule_subject.tenant_id IS '租户id';
COMMENT
ON COLUMN auth_group_rule_subject.create_time IS '创建时间';
COMMENT
ON COLUMN auth_group_rule_subject.create_by IS '创建者';
COMMENT
ON COLUMN auth_group_rule_subject.update_time IS '更新时间';
COMMENT
ON COLUMN auth_group_rule_subject.update_by IS '更新者';
COMMENT
ON COLUMN auth_group_rule_subject.deleted IS '是否删除';
