package com.caidaocloud.auth.service.external.domain.repository;

import com.caidaocloud.auth.service.external.domain.entity.AppSecurityEntity;
import com.caidaocloud.auth.service.external.infrastructure.enums.ExternalAuthEnum;

import java.util.Optional;

public interface IAppSecurityRepository {
    void save(AppSecurityEntity entity);
    void update(AppSecurityEntity entity);
    Optional<AppSecurityEntity> findAppSecurity(ExternalAuthEnum type);
}
