package com.caidaocloud.auth.service.caidao.facade.vo;

import com.caidaocloud.auth.core.enums.AuthRoleScopeRestriction;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AuthScopeRestrictionVo {
    private AuthRoleScopeRestriction restriction;
    private String simpleValues;
    private String targets;
}