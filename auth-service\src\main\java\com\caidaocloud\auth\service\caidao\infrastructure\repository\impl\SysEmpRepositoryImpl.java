package com.caidaocloud.auth.service.caidao.infrastructure.repository.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.caidaocloud.auth.service.caidao.application.dto.hr.EmpSearchColumnsDto;
import com.caidaocloud.auth.service.caidao.application.dto.masterdata.*;
import com.caidaocloud.auth.service.caidao.application.feign.IHrFeign;
import com.caidaocloud.auth.service.common.application.feign.MasterDataFeignV2;
import com.caidaocloud.auth.service.caidao.domain.repository.ISysEmpRepositoy;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Sequences;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 * @date 2023/5/25
 */
@Repository
public class SysEmpRepositoryImpl implements ISysEmpRepositoy {

	@Autowired
	private MasterDataFeignV2 masterDataFeign;

	@Autowired
	private IHrFeign hrFeign;

	@Override
	public List<SysEmpInfoDto> getEmpInfoByEmpIds(String empIds) {
		EmpSearchDto dto = new EmpSearchDto();
		dto.setEmpIds(Arrays.asList(empIds.split(",")));
		dto.setDatetime(System.currentTimeMillis());
		Result<List<EmpInfoVo>> result = masterDataFeign.getEmpInfoByEmpIds(dto);
		return Sequences.sequence(result.getData())
				.map(emp -> {
					SysEmpInfoDto info = ObjectConverter.convert(emp, SysEmpInfoDto.class);
					info.setEmpid(emp.getEmpId());
					info.setEmail(emp.getCompanyEmail());
					return info;
				}).toList();
	}

	@Override
	public PageResult<EmpNodeVo> queryEmpPage(EmpSearchDto dto) {

		Result<PageResult<EmpNodeVo>> result = masterDataFeign.queryEmpPage(dto);

		return result.getData();
	}

	@Override
	public PageResult<EmpWorkInfoVo> queryEmpColumnsPage(EmpSearchDto dto) {
		Result<PageResult<EmpWorkInfoVo>> result = masterDataFeign.queryEmpColumnsPage(dto);

		return result.getData();
	}

	@Override
	public PageResult<Map<String, String>> searchEmpColumnsPage(EmpSearchColumnsDto dto) {

		Result<PageResult<Map<String, String>>> result = hrFeign.searchEmpColumnsPage(dto);
		return result.getData();
	}

}
