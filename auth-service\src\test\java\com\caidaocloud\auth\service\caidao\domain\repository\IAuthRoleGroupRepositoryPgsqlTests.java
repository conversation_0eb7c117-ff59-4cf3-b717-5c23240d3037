package com.caidaocloud.auth.service.caidao.domain.repository;

import com.caidaocloud.auth.service.AuthApplication;
import com.caidaocloud.auth.service.caidao.domain.entity.AuthRoleGroupDo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 *
 * <AUTHOR>
 * @date 2023/5/5
 */
@SpringBootTest(classes = AuthApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
@Slf4j
public class IAuthRoleGroupRepositoryPgsqlTests {
	@Autowired
	private IAuthRoleGroupRepository authRoleGroupRep;

	@Before
	public void setUp() throws Exception {
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("8");
		userInfo.setUserId(0L);
		userInfo.setEmpId(0L);
		SecurityUserUtil.setSecurityUserInfo(userInfo);
	}

	@Test
	public void test(){
		authRoleGroupRep.saveOrUpdateRoleGroup(new AuthRoleGroupDo());
	}
}